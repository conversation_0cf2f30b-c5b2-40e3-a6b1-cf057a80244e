import 'package:flutter/material.dart';
import 'package:stock_detail/gen/assets.gen.dart';
import 'package:stock_detail/tabs/stock_trading_tab/matching_history_buy_up_sell_down/matching_history_buy_up_sell_down_view.dart';
import 'package:stock_detail/tabs/stock_trading_tab/stock_holding/stock_holding_view.dart';
import 'package:stock_detail/tabs/stock_trading_tab/stock_trading_cubit.dart';
import 'package:stock_detail/widgets/circle_icon_view.dart';
import 'package:vp_common/vp_common.dart' hide Assets;
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart' hide CircleIconView;

import 'chart/stock_detail_chart_view.dart';

class StockTradingTabView extends StatefulWidget {
  const StockTradingTabView({required this.stock, super.key});

  final StockInfoModel stock;

  @override
  State<StockTradingTabView> createState() => _StockTradingTabViewState();
}

class _StockTradingTabViewState extends State<StockTradingTabView>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocProvider<StockTradingCubit>(
      create: (_) => StockTradingCubit(symbol: widget.stock.symbol),
      child: SingleChildScrollView(
        child: Column(
          spacing: 8,
          children: [
            BlocSelector<StockTradingCubit, StockTradingState, bool>(
              selector: (state) => state.showTradingView,
              builder: (context, showTradingView) {
                if (showTradingView) {
                  return VPTradingView(
                    padding: EdgeInsets.zero,
                    symbol: widget.stock.symbol,
                    actionPadding: const EdgeInsets.fromLTRB(16, 8, 5, 0),
                    actions: [
                      IconButton(
                        onPressed:
                            () => context
                                .read<StockTradingCubit>()
                                .onChartChange(false),
                        icon: CircleIconView(
                          child: VPStockDetailAssets.icons.icToggleChart.svg(
                            width: 16,
                            height: 16,
                          ),
                        ),
                      ),
                    ],
                  );
                }

                return StockDetailChartView(stock: widget.stock);
              },
            ),

            StockHoldingView(stock: widget.stock),

            SupplyDemandSectionView(stockInfoModel: widget.stock),

            MatchingHistoryBuyUpSellDownView(stock: widget.stock),
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class SupplyDemandSectionView extends StatelessWidget {
  const SupplyDemandSectionView({required this.stockInfoModel, super.key});

  final StockInfoModel stockInfoModel;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: vpColor.backgroundElevation0,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Cung cầu hiện tại',
            style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),
          ),

          const SizedBox(height: 8),

          buildPriceLevelsSection(),

          const SizedBox(height: 24),

          buildSectionView(),

          const SizedBox(height: 16),

          VPSingleLineChartView(
            leftPercent: stockInfoModel.bidPercent,
            rightPercent: stockInfoModel.offerPercent,
          ),

          const SizedBox(height: 8),

          VPTop3View(stock: stockInfoModel),
        ],
      ),
    );
  }

  Widget buildSectionView() {
    return Row(
      children: [
        _ItemView(
          title: 'Bên mua',
          alignment: CrossAxisAlignment.start,
          child: VPTotalBidQttyItemView.stock(stock: stockInfoModel),
        ),

        const Spacer(),

        _ItemView(
          title: 'NN mua',
          alignment: CrossAxisAlignment.end,
          child: VPForeignBuyVolumeItemView.stock(stock: stockInfoModel),
        ),

        const SizedBox(width: 16),

        _ItemView(
          title: 'NN bán',
          alignment: CrossAxisAlignment.start,
          child: VPForeignSellVolumeItemView.stock(stock: stockInfoModel),
        ),

        const Spacer(),

        _ItemView(
          title: 'Bên bán',
          alignment: CrossAxisAlignment.end,
          child: VPTotalOfferQttyItemView.stock(stock: stockInfoModel),
        ),
      ],
    );
  }

  Widget buildPriceLevelsSection() {
    return Row(
      children: [
        Expanded(child: FloorItemView(stock: stockInfoModel)),

        Expanded(child: ReferenceItemView(stock: stockInfoModel)),

        Expanded(child: CeilingItemView(stock: stockInfoModel)),

        const SizedBox(width: 10),

        Expanded(
          child: LowItemView(
            stock: stockInfoModel,
            priceBuilder:
                (low) => low == 0 ? '-' : FormatUtils.formatClosePrice(low),
          ),
        ),

        Expanded(
          child: AverageItemView(
            stock: stockInfoModel,
            priceBuilder:
                (average) =>
                    average == 0 ? '-' : FormatUtils.formatClosePrice(average),
          ),
        ),

        Expanded(
          child: HighItemView(
            stock: stockInfoModel,
            priceBuilder:
                (high) => high == 0 ? '-' : FormatUtils.formatClosePrice(high),
          ),
        ),
      ],
    );
  }
}

class _ItemView extends StatelessWidget {
  const _ItemView({
    required this.title,
    required this.child,
    required this.alignment,
  });

  final String title;

  final Widget child;

  final CrossAxisAlignment alignment;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: alignment,
      children: [
        Text(
          title,
          style: vpTextStyle.captionRegular.copyColor(vpColor.textSecondary),
        ),
        child,
      ],
    );
  }
}
