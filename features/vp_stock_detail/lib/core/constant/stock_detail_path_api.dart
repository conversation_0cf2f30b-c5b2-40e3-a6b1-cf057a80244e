import 'package:vp_core/vp_core.dart';

class StockDetailAPI {
  static const tagging =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/stockTagging';

  static const stockListByTag =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/stockListByTag';

  static const news =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/stockNews';
  static const eventCalendar =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/stockEventCalendar';
  static const getBuyUpSellDown =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/stockBuyUpSellDown';
  static const getCapitalAndDividendInfo =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/capitalAndDividendInfo';
  static const getCompanyInformation =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/stockCompanyInfo';
  static const getCorporateShareholder =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/stockCompanyShareholders';
  static const getStockDetailRating =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/stockDetailRating';
  static const getStockProfit =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/stockProfit';
  static const getStockPriceHistory =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/stockPriceHistory';
  static const getStockForeignTrading =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/stockForeignTrading';
  static const getStockPlaceOrder =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/stockPlaceOrder';
  static const getStockShareholderTransactions =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/stockShareholderTransactions';
  static const matchingHistoryBuyUpSellDown =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/matchingHistoryBuyUpSellDown';

  static const intradayMatchingHistory =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/intradayMatchingHistory';

  static const getCwInfo =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/CWInfo';

  static String getETFNewsUrl({
    required String symbol,
    AppTheme theme = AppTheme.light,
  }) {
    return 'https://vpbs.stock-gpt.ai/news/index.html?symbol=$symbol&theme=${theme.name}';
  }

  static String getTradingViewUrl({
    required String symbol,
    bool isFullScreen = false,
    AppTheme theme = AppTheme.light,
    String? interval,
    String? userId,
  }) {
    return 'https://neopro-sit.vpbanks.com.vn/tradingview/?symbol=$symbol&timescaleMarks=1&theme=${theme.name}&language=&interval=$interval&isLoggedIn=${userId != null ? 1 : 0}&autoSave=1${isFullScreen ? '&hideHeaderFullscreen=1' : ''}';
  }
}
