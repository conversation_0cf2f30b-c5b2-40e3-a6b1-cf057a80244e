import 'package:flutter/material.dart';
import 'package:stock_detail/fu_stock_detail/fu_stock_detail_cubit.dart';
import 'package:stock_detail/fu_stock_detail/price_step/price_step_chart.dart';
import 'package:stock_detail/fu_stock_detail/widgets/fu_profit_view.dart';
import 'package:stock_detail/fu_stock_detail/widgets/fu_top_10_price_view.dart';
import 'package:stock_detail/fu_stock_detail/widgets/supply_demand_section_view.dart';
import 'package:stock_detail/tabs/stock_trading_tab/matching_history_buy_up_sell_down/fu_matching_history_buy_up_sell_down_header_view.dart';
import 'package:stock_detail/widgets/stock_detail_shimmer_view.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/widgets/trading_view/trading_view.dart';

class FuStockDetailContentView extends StatelessWidget {
  const FuStockDetailContentView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FuStockDetailCubit, FuStockDetailState>(
      builder: (context, state) {
        if (state.apiStatus.isError) {
          return ErrorWithRetryView(onRetry: () => onRetry(context));
        }

        if (state.apiStatus.isLoading) {
          return const StockDetailShimmerView();
        }

        final stock = state.stockInfo;

        if (stock == null) return NoDataView();

        final symbol = stock.symbol;

        return SingleChildScrollView(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
          child: Column(
            spacing: 10,
            children: [
              FuProfitView(stock: stock),

              VPTradingView(symbol: symbol, padding: EdgeInsets.zero),

              SupplyDemandSectionView(stock: stock),

              FuTop10PriceView(stock: stock),

              PriceStepChart(symbol: symbol, title: 'Biểu đồ bước giá'),

              FutureMatchingHistoryBuyUpSellDownHeaderView(stock: stock),
            ],
          ),
        );
      },
    );
  }

  void onRetry(BuildContext context) {
    context.read<FuStockDetailCubit>().loadData();
  }
}
