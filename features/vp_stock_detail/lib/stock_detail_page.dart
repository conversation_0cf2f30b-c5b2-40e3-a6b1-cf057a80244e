import 'package:flutter/material.dart';
import 'package:stock_detail/stock_detail_content_view.dart';
import 'package:stock_detail/stock_detail_navigator.dart';
import 'package:stock_detail/widgets/stock_detail_shimmer_view.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

import 'stock_detail_cubit.dart';

class StockDetailPage extends StatelessWidget {
  const StockDetailPage({required this.args, super.key});

  final StockDetailArgs args;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<StockDetailCubit>(
      create: (_) => StockDetailCubit(symbol: args.symbol)..loadData(),
      child: VPScaffold(
        appBar: VPAppBar.layer(
          titleBuilder: (style) {
            return BlocBuilder<StockDetailCubit, StockDetailState>(
              buildWhen: (pState, state) => pState.stockInfo != state.stockInfo,
              builder: (_, state) {
                final stock = state.stockInfo;

                final marketCode = stock?.marketCode;

                final marketCodeName =
                    marketCode != null ? '(${marketCode.name})' : '';

                return Text('${state.symbol} $marketCodeName', style: style);
              },
            );
          },
          subTitleBuilder:
              (style) => BlocBuilder<StockDetailCubit, StockDetailState>(
                buildWhen:
                    (pState, state) => pState.stockInfo != state.stockInfo,
                builder: (_, state) {
                  final stock = state.stockInfo;

                  return stock?.stockName != null
                      ? Text(stock!.stockName, style: style)
                      : const SizedBox.shrink();
                },
              ),
          revertSubTitle: true,
          actions: [
            IconButton(
              onPressed: () => stockDetailNavigator.openCopilot(context),
              icon: DesignAssets.icons.icCodepilot.svg(),
            ),

            IconButton(
              onPressed: () => addSymbolToWatchlist(context),
              icon: DesignAssets.icons.add.icAddSquare.svg(
                color: vpColor.iconPrimary,
              ),
            ),
            Builder(
              builder: (context) {
                return IconButton(
                  onPressed: () => openSearchPage(context),
                  icon: DesignAssets.icons.icSearch.svg(
                    color: vpColor.iconPrimary,
                  ),
                );
              },
            ),
          ],
        ),
        body: _StockDetailBody(args: args),
      ),
    );
  }

  void openSearchPage(BuildContext context) async {
    final data = await stockDetailNavigator.openSearchPage(
      context,
      args: SearchArgs(
        hint: 'Nhập mã tìm kiếm',
        itemAction: SearchItemAction.pickAndReturn,
      ),
    );

    if (data is! StockModel || !context.mounted) return;

    final stockType = data.stockType;

    if (stockType == null || stockType.isStock) {
      return context.read<StockDetailCubit>().onSymbolChanged(data.symbol);
    }

    if (stockType.isFU) {
      stockDetailNavigator.openFuDetailPage(
        context,
        args: FuStockDetailArgs(symbol: data.symbol),
        pushReplacementNamed: true,
      );
    }
  }

  void addSymbolToWatchlist(BuildContext context) {
    if (!isLoggedIn) {
      return stockDetailNavigator.openSignInRequiredDialog(
        context,
        onSignInSuccess:
            () => stockDetailNavigator.openStockDetailPage(
              getContext,
              args: StockDetailArgs(symbol: args.symbol),
            ),
      );
    }

    stockDetailNavigator.openAddSymbolToWatchlistSelectorBottomSheet(
      context,
      symbols: [args.symbol],
    );
  }
}

class _StockDetailBody extends StatefulWidget {
  const _StockDetailBody({required this.args});

  final StockDetailArgs args;

  @override
  State<_StockDetailBody> createState() => _StockDetailBodyState();
}

class _StockDetailBodyState extends State<_StockDetailBody>
    with AppLifecycleMixin<_StockDetailBody> {
  StockDetailArgs get args => widget.args;

  late StockDetailTab? initialTab = args.initialTab;

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listenWhen: (pState, state) => pState.isLoggedIn != state.isLoggedIn,
      listener: (_, state) {
        onRefresh();
      },
      child: BlocBuilder<StockDetailCubit, StockDetailState>(
        buildWhen:
            (pState, state) =>
                pState.stockInfo != state.stockInfo ||
                pState.apiStatus != state.apiStatus,
        builder: (context, state) {
          if (state.apiStatus.isError) {
            return VPErrorView(
              onRetry: () => onRefresh(),
              error: state.apiStatus.error,
            );
          }

          if (state.apiStatus.isLoading) {
            return const StockDetailShimmerView();
          }

          final stock = state.stockInfo;

          if (stock == null) return const NoDataView();

          return StockDetailContentView(stock: stock, initialTab: initialTab);
        },
      ),
    );
  }

  @override
  void onEnterForeground() {
    super.onEnterForeground();

    onRefresh();
  }

  @override
  void networkUp() => onRefresh();

  void onRefresh() {
    if (mounted) {
      context.read<StockDetailCubit>().onRefresh();
    }
  }
}
