import 'package:flutter/material.dart';
import 'package:stock_detail/model/enum/trading_view_period.dart';
import 'package:stock_detail/model/stock_news_model.dart';
import 'package:stock_detail/model/stock_tagging_model.dart';
import 'package:stock_detail/pages/intraday_matching_history/intraday_matching_history_bottom_sheet.dart';
import 'package:stock_detail/pages/intraday_matching_history/widgets/fu_intraday_matching_history_header_view.dart';
import 'package:stock_detail/pages/intraday_matching_history/widgets/fu_intraday_matching_history_item_view.dart';
import 'package:stock_detail/pages/intraday_matching_history/widgets/intraday_matching_history_item_view.dart';
import 'package:stock_detail/router/stock_detail_router.dart';
import 'package:stock_detail/tabs/stock_news_tab/etf/widgets/stock_etf_news_detail_bottom_sheet.dart';
import 'package:stock_detail/widgets/tagging/tagging_detail/stock_tag_bottom_sheet.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/model/pdf_or_webview/pdf_or_webview_args.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

StockDetailNavigator get stockDetailNavigator =>
    GetIt.instance.get<StockDetailNavigator>();

abstract class StockDetailNavigator {
  Future openSearchPage(BuildContext context, {SearchArgs? args});

  Future openIntradayMatchingHistoryBottomSheet(
    BuildContext context, {
    required String symbol,
    num? reference,
    num? celling,
    num? floor,
  });

  Future openFutureIntradayMatchingHistoryBottomSheet(
    BuildContext context, {
    required String symbol,
    num? reference,
    num? celling,
    num? floor,
  });

  Future openNewsDetailBottomSheet(
    BuildContext context, {
    required StockNewsModel news,
  });

  Future openPlaceOrderPage(
    BuildContext context, {
    required PlaceOrderArgs args,
  });

  Future openStockDetailPage(
    BuildContext context, {
    required StockDetailArgs args,
    bool pushReplacementNamed = false,
  });

  Future openFuDetailPage(
    BuildContext context, {
    required FuStockDetailArgs args,
    bool pushReplacementNamed = false,
  });

  Future openStockListByTagBottomSheet(
    BuildContext context, {
    required StockTaggingModel tag,
  });

  Future openAddSymbolToWatchlistSelectorBottomSheet(
    BuildContext context, {
    required List<String> symbols,
  });

  void openSignInRequiredDialog(
    BuildContext context, {
    VoidCallback? onSignInSuccess,
  });

  Future openPdfOrWebViewPage(
    BuildContext context, {
    required PdfOrWebViewArgs args,
  });

  Future openCopilot(BuildContext context);
}

class StockDetailNavigatorImpl extends StockDetailNavigator {
  @override
  Future openSearchPage(BuildContext context, {SearchArgs? args}) {
    return context.pushNamed(
      StockDetailRouterName.search.routeName,
      queryParameters: args?.toQueryParams() ?? const {},
    );
  }

  @override
  Future openIntradayMatchingHistoryBottomSheet(
    BuildContext context, {
    required String symbol,
    num? reference,
    num? celling,
    num? floor,
  }) async {
    await VPPopup.bottomSheet(
      IntradayMatchingHistoryBottomSheet(
        symbol: symbol,
        reference: reference,
        celling: celling,
        floor: floor,
        itemBuilder: (item) {
          return IntradayMatchingHistoryItemView(
            item: item,
            celling: celling,
            floor: floor,
          );
        },
      ),
    ).showSheet(context);
  }

  @override
  Future openFutureIntradayMatchingHistoryBottomSheet(
    BuildContext context, {
    required String symbol,
    num? reference,
    num? celling,
    num? floor,
  }) async {
    await VPPopup.bottomSheet(
      IntradayMatchingHistoryBottomSheet(
        symbol: symbol,
        reference: reference,
        celling: celling,
        floor: floor,
        header: const FutureIntradayMatchingHistoryHeaderView(),
        itemBuilder: (item) {
          return FutureIntradayMatchingHistoryItemView(
            item: item,
            celling: celling,
            floor: floor,
          );
        },
      ),
    ).showSheet(context);
  }

  @override
  Future openNewsDetailBottomSheet(
    BuildContext context, {
    required StockNewsModel news,
  }) async {
    await VPPopup.bottomSheet(
      StockEtfNewsDetailBottomSheet(news: news),
    ).showSheet(context);
  }

  @override
  Future openPlaceOrderPage(
    BuildContext context, {
    required PlaceOrderArgs args,
  }) {
    return context.pushNamed(
      StockDetailRouterName.placeOrder.routeName,
      queryParameters: args.toQueryParams(),
    );
  }

  @override
  Future openStockDetailPage(
    BuildContext context, {
    required StockDetailArgs args,
    bool pushReplacementNamed = false,
  }) async {
    if (pushReplacementNamed) {
      context.pushReplacementNamed(
        StockDetailRouterName.stockDetail.routeName,
        queryParameters: args.toQueryParams(),
      );

      return;
    }

    return context.pushNamed(
      StockDetailRouterName.stockDetail.routeName,
      queryParameters: args.toQueryParams(),
    );
  }

  @override
  Future openStockListByTagBottomSheet(
    BuildContext context, {
    required StockTaggingModel tag,
  }) async {
    await VPPopup.bottomSheet(StockTagBottomSheet(tag: tag)).showSheet(context);
  }

  @override
  Future openAddSymbolToWatchlistSelectorBottomSheet(
    BuildContext context, {
    required List<String> symbols,
  }) {
    return VPPopup.bottomSheet(
      AddSymbolToWatchListBottomSheet(symbols: symbols),
    ).showSheet(context);
  }

  @override
  void openSignInRequiredDialog(
    BuildContext context, {
    VoidCallback? onSignInSuccess,
  }) {
    VPPopup.custom(
      child: SignInRequiredDialog(
        onSignIn:
            () => context.push(
              StockDetailRouterName.signIn.routeName,
              extra: SignInArgs(onSignInSuccess: onSignInSuccess),
            ),
      ),
    ).showDialog(context);
  }

  @override
  Future openFuDetailPage(
    BuildContext context, {
    required FuStockDetailArgs args,
    bool pushReplacementNamed = false,
  }) async {
    if (pushReplacementNamed) {
      context.pushReplacementNamed(
        StockDetailRouterName.fuStockDetail.routeName,
        queryParameters: args.toQueryParams(),
      );

      return;
    }

    return context.pushNamed(
      StockDetailRouterName.fuStockDetail.routeName,
      queryParameters: args.toQueryParams(),
    );
  }

  @override
  Future openPdfOrWebViewPage(
    BuildContext context, {
    required PdfOrWebViewArgs args,
  }) {
    return context.pushNamed(
      StockDetailRouterName.pdfOrWebView.routeName,
      queryParameters: args.toQueryParams(),
    );
  }

  @override
  Future openCopilot(BuildContext context) {
    return context.pushNamed(StockDetailRouterName.codePilotGetToken.routeName);
  }
}
