import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

import 'core/repository/stock_detail_repository.dart';

part 'stock_detail_state.dart';

class StockDetailCubit extends AppCubit<StockDetailState> {
  StockDetailCubit({required String symbol})
    : super(StockDetailState(symbol: symbol, apiStatus: ApiStatus.done()));

  final StockCommonRepository stockCommonRepository = GetIt.instance.get();

  final StockDetailRepository stockDetailRepository = GetIt.instance.get();

  void loadData() async {
    if (state.apiStatus.isLoading) return;

    try {
      emit(state.copyWith(apiStatus: ApiStatus.loading()));

      final data = await stockCommonRepository.getStockInfoV2(
        symbols: [state.symbol],
      );

      emit(
        state.copyWith(
          stockInfoModel: data?.content.firstOrNull,
          apiStatus: ApiStatus.done(),
        ),
      );
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      emit(state.copyWith(apiStatus: ApiStatus.error(e)));
    }
  }

  void onSymbolChanged(String symbol) {
    if (state.symbol == symbol) return;

    emit(state.symbolChange(symbol: symbol));

    loadData();
  }

  void onIndexTabChanged(int index) {
    if (index == state.tabIndex) return;

    emit(state.copyWith(tabIndex: index));
  }

  void onRefresh() => loadData();
}
