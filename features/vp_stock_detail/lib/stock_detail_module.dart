import 'package:flutter/src/widgets/localizations.dart';
import 'package:stock_detail/core/repository/stock_detail_repository.dart';
import 'package:stock_detail/core/service/stock_detail_service.dart';
import 'package:stock_detail/fu_stock_detail/fu_stock_detail_page.dart';
import 'package:stock_detail/stock_detail_navigator.dart';
import 'package:stock_detail/stock_detail_page.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/model/fu_stock_detail/fu_stock_detail_args.dart';
import 'package:vp_stock_common/model/stock_detail/stock_detail_args.dart';

import 'router/stock_detail_router.dart';

class VPStockDetailModule implements Module {
  @override
  void injectServices(GetIt service) {
    service.registerLazySingleton(() => StockDetailService(service()));
    service.registerLazySingleton<StockDetailRepository>(
      () => StockDetailRepositoryImpl(stockDetailService: service()),
    );

    service.registerLazySingleton<StockDetailNavigator>(
      () => StockDetailNavigatorImpl(),
    );
  }

  @override
  List<RouteBase> router() {
    return [
      GoRoute(
        path: VPStockDetailRouter.stockDetail.routeName,
        name: VPStockDetailRouter.stockDetail.routeName,
        builder: (_, state) {
          return StockDetailPage(
            args: StockDetailArgsExtensions.fromQueryParams(
              state.uri.queryParameters,
            ),
          );
        },
      ),
      GoRoute(
        path: VPStockDetailRouter.fuStockDetail.routeName,
        name: VPStockDetailRouter.fuStockDetail.routeName,
        builder: (_, state) {
          return FuStockDetailPage(
            args: FuStockDetailArgsExtensions.fromQueryParams(
              state.uri.queryParameters,
            ),
          );
        },
      ),
    ];
  }

  @override
  List<LocalizationsDelegate> localizationsDelegates() {
    return [];
  }

  @override
  String modulePath() {
    return 'vpStockDetail';
  }
}
