import 'package:json_annotation/json_annotation.dart';

part 'delete_order_request.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class DeleteOrderRequest {
  final String? accountId;
  final String? orderId;
  final String? market;
  final String? via;
  final String? requestId;
  final String? orderIds;
  const DeleteOrderRequest({
    this.accountId,
    this.orderId,
    this.market,
    this.via,
    this.requestId,
    this.orderIds,
  });

  factory DeleteOrderRequest.fromJson(Map<String, dynamic> json) =>
      _$DeleteOrderRequestFromJson(json);

  Map<String, dynamic> toJson() => _$DeleteOrderRequestToJson(this);
}

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class DeleteFuConditionOrderRequest {
  final String? accountId;
  final String? orderId;
  final String? via;
  final String? requestId;
  @Json<PERSON>ey(name: "orderIds")
  final List<String>? fuConfitionOrderIds;

  const DeleteFuConditionOrderRequest({
    this.accountId,
    this.orderId,
    this.via = "V",
    this.requestId,
    this.fuConfitionOrderIds,
  });

  factory DeleteFuConditionOrderRequest.fromJson(Map<String, dynamic> json) =>
      _$DeleteFuConditionOrderRequestFromJson(json);

  Map<String, dynamic> toJson() => _$DeleteFuConditionOrderRequestToJson(this);
}
