// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delete_order_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeleteOrderRequest _$DeleteOrderRequestFromJson(Map<String, dynamic> json) =>
    DeleteOrderRequest(
      accountId: json['accountId'] as String?,
      orderId: json['orderId'] as String?,
      market: json['market'] as String?,
      via: json['via'] as String?,
      requestId: json['requestId'] as String?,
      orderIds: json['orderIds'] as String?,
    );

Map<String, dynamic> _$DeleteOrderRequestToJson(DeleteOrderRequest instance) =>
    <String, dynamic>{
      if (instance.accountId case final value?) 'accountId': value,
      if (instance.orderId case final value?) 'orderId': value,
      if (instance.market case final value?) 'market': value,
      if (instance.via case final value?) 'via': value,
      if (instance.requestId case final value?) 'requestId': value,
      if (instance.orderIds case final value?) 'orderIds': value,
    };

DeleteFuConditionOrderRequest _$DeleteFuConditionOrderRequestFromJson(
  Map<String, dynamic> json,
) => DeleteFuConditionOrderRequest(
  accountId: json['accountId'] as String?,
  orderId: json['orderId'] as String?,
  via: json['via'] as String? ?? "V",
  requestId: json['requestId'] as String?,
  fuConfitionOrderIds:
      (json['orderIds'] as List<dynamic>?)?.map((e) => e as String).toList(),
);

Map<String, dynamic> _$DeleteFuConditionOrderRequestToJson(
  DeleteFuConditionOrderRequest instance,
) => <String, dynamic>{
  if (instance.accountId case final value?) 'accountId': value,
  if (instance.orderId case final value?) 'orderId': value,
  if (instance.via case final value?) 'via': value,
  if (instance.requestId case final value?) 'requestId': value,
  if (instance.fuConfitionOrderIds case final value?) 'orderIds': value,
};
