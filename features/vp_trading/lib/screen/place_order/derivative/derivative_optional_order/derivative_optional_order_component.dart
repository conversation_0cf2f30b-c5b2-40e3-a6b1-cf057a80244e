import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/constant/constants.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/cubit/derivative/derivative_order/derivative_order_cubit.dart';
import 'package:vp_trading/cubit/derivative/mixin/derivative_confirm_order_create_mixin.dart';
import 'package:vp_trading/cubit/derivative/mixin/derivative_update_user_config_mixin.dart';
import 'package:vp_trading/cubit/derivative/validate_order/derivative_validate_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/stock_info/stock_info_cubit.dart';
import 'package:vp_trading/screen/place_order/derivative/derivative_optional_order/mixin/order_optional_type_mixin.dart';
import 'package:vp_trading/utils/app_keyboard_utils.dart';

typedef OptionalType = DerivativeOrderOptionalType;

class DerivativeOptionalOrderComponent extends StatelessWidget
    with
        DerivativeConfirmOrderCreateMixin,
        DerivativeUpdateUserConfigMixin,
        OrderOptionalTypeMixin {
  const DerivativeOptionalOrderComponent({super.key});

  void _handleOrderOnPress(BuildContext context, OrderAction action) async {
    AppKeyboardUtils.unFocusTextField();

    final orderType =
        context.read<DerivativeOrderCubit>().state.orderOptionalCurrentType;

    switch (orderType.type) {
      case DerivativeOrderOptionalType.regular:
        await orderRegular(context, action, updateUserConfig);
        return;
      case DerivativeOrderOptionalType.stopOrder:
      case DerivativeOrderOptionalType.trailingStop:
      case DerivativeOrderOptionalType.stopLossOrTakeProfit:
        await orderCondition(context, action, orderType.type, updateUserConfig);
        return;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<StockInfoCubit, StockInfoState>(
      builder: (context, state) {
        bool isDisable =
            GetIt.instance<SubAccountCubit>().derivativeAccount == null ||
            state.stockInfo == null;
        return ColoredBox(
          color: vpColor.backgroundElevation0,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: VpsButton.custom(
                    onPressed:
                        () => _handleOrderOnPress(context, OrderAction.buy),
                    buttonType: ButtonType.primary,
                    disabled: isDisable,
                    title: OrderAction.buy.nameDerivativeUI,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: VpsButton.custom(
                    onPressed:
                        () => _handleOrderOnPress(context, OrderAction.sell),
                    buttonType: ButtonType.primary,
                    disabled: isDisable,
                    typeDanger: ButtonDangerType.danger,
                    title: OrderAction.sell.nameDerivativeUI,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  bool validateVolume(BuildContext context, String volume) {
    if (context.read<DerivativeValidateOrderCubit>().state.isErrorVolume) {
      context.read<DerivativeValidateOrderCubit>().focusField(
        FocusKeyboard.volume,
      );
      context.read<DerivativeValidateOrderCubit>().onChangeVolume(
        volume,
        isOrder: true,
      );
      return true;
    }
    return false;
  }

  @override
  bool validatePrice(BuildContext context, String price) {
    if (context.read<DerivativeValidateOrderCubit>().state.isErrorPrice) {
      context.read<DerivativeValidateOrderCubit>().focusField(
        FocusKeyboard.price,
      );
      context.read<DerivativeValidateOrderCubit>().onChangePrice(
        price,
        isOrder: true,
      );
      return true;
    }
    return false;
  }

  @override
  bool validateActivePrice(BuildContext context, String price) {
    if (context.read<DerivativeValidateOrderCubit>().state.isErrorActivePrice) {
      context.read<DerivativeValidateOrderCubit>().focusField(
        FocusKeyboard.priceActive,
      );
      context.read<DerivativeValidateOrderCubit>().onChangeActivePrice(
        price,
        isOrder: true,
      );
      return true;
    }
    return false;
  }
}
