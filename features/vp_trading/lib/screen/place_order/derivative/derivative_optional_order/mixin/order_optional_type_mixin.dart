import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/constant/constants.dart';
import 'package:vp_trading/core/constant/delta_type.dart';
import 'package:vp_trading/core/constant/derivative_app_configs.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/core/constant/time_type.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/derivative/validate_condition_order/derivative_validate_condition_order_cubit.dart';
import 'package:vp_trading/cubit/derivative/validate_order/derivative_validate_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/submit/place_order_submit_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/enum/activation_conditions_type.dart';
import 'package:vp_trading/model/enum/market_type.dart';
import 'package:vp_trading/model/enum/order_trading_type.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/model/order/request/order_request_model.dart';
import 'package:vp_trading/screen/place_order/derivative/derivative_optional_order/mixin/derivative_validate_text_field_mixin.dart';
import 'package:vp_trading/screen/place_order/widgets/confirm/place_derivative_condition_order_confirm_dialog.dart';
import 'package:vp_trading/screen/place_order/widgets/confirm/place_order_confirm_dialog.dart';

mixin OrderOptionalTypeMixin implements DerivativeValidateTextFieldMixin {
  Future<void> orderRegular(
    BuildContext context,
    OrderAction action,
    Function() updateUserConfig,
  ) async {
    final symbol = context.read<PlaceOrderCubit>().state.symbol;
    final side = action.value;
    final calculateValue =
        context.read<DerivativeValidateOrderCubit>().state.calculateValue;
    final username =
        GetIt.instance<AuthCubit>().userInfo?.userinfo?.username ?? '';
    final currentVolume =
        context.read<DerivativeValidateOrderCubit>().state.currentVolume ?? "0";
    final currentPrice =
        context.read<DerivativeValidateOrderCubit>().state.currentPrice ?? "0";
    final subAccountType = context.read<PlaceOrderCubit>().state.subAccountType;
    final sessionType =
        context.read<DerivativeValidateOrderCubit>().state.sessionType;
    final orderTradingType =
        sessionType != null
            ? OrderTradingType.market.nameServer
            : OrderTradingType.limit.nameServer;
    final price =
        sessionType != null
            ? sessionType.name.toUpperCase()
            : currentPrice.priceDerivative ?? 0;
    final subAccountId = subAccountType.toSubAccountModel()?.id ?? "";

    // Validate volume
    context.read<DerivativeValidateOrderCubit>().onValidateVolume(
      currentVolume,
    );

    if (validatePrice(context, currentPrice)) return;
    if (validateVolume(context, currentVolume)) return;

    // Create request
    final request = OrderRequestModel(
      qty: (currentVolume.volume).toInt(),
      price: price,
      symbol: symbol,
      market: MarketType.derivatives.nameServer,
      side: side,
      type: orderTradingType,
      username: username,
      accountId: subAccountId,
    );

    if (DerivativeAppConfigs().userConfig.isShowNotifyConfirmOrder) {
      await showDialog(
        context: context,
        builder:
            (_) => PlaceOrderConfirmDialog(
              price: sessionType?.name ?? currentPrice,
              value: calculateValue,
              requestModel: request,
              isDerivative: true,
              onConfirm: (showConfirmOrder) {
                DerivativeAppConfigs().userConfig.setNotifyConfirmOrder(
                  showConfirmOrder,
                );
                updateUserConfig();
                context.read<PlaceOrderSubmitCubit>().submitPlaceOrder(
                  request: request,
                );
              },
            ),
      );
    } else {
      context.read<PlaceOrderSubmitCubit>().submitPlaceOrder(request: request);
    }
  }

  Future<void> orderCondition(
    BuildContext context,
    OrderAction action,
    DerivativeOrderOptionalType orderType,
    Function() updateUserConfig,
  ) async {
    final symbol = context.read<PlaceOrderCubit>().state.symbol;
    final side = action.value;
    final currentVolume =
        context.read<DerivativeValidateOrderCubit>().state.currentVolume ?? "0";
    final currentPrice =
        context.read<DerivativeValidateOrderCubit>().state.currentPrice ?? "0";
    final subAccountType = context.read<PlaceOrderCubit>().state.subAccountType;
    final sessionType =
        context.read<DerivativeValidateOrderCubit>().state.sessionType;
    final price =
        sessionType != null
            ? sessionType.name.toUpperCase()
            : currentPrice.priceDerivative ?? 0;
    final subAccountId = subAccountType.toSubAccountModel()?.id ?? "";

    // Validate volume
    if (validateVolume(context, currentVolume)) return;

    var conditionInfo = ConditionInfo(
      symbol: symbol,
      qty: currentVolume.volume.toInt(),
      side: side,
      type: OrderTradingType.limit.nameServer,
      timetype: TimeType.T.toServer,
      fromDate: DateTime.now().formatToDdMmYyyy(),
      toDate: DateTime.now().formatToDdMmYyyy(),
    );

    var request = ConditionOrderRequestModel(
      market: 'derivatives',
      accountId: subAccountId,
      orderType: orderType.toServer,
      conditionInfo: conditionInfo,
    );

    switch (orderType) {
      case DerivativeOrderOptionalType.stopOrder:
        final state = context.read<DerivativeValidateOrderCubit>().state;
        if (validatePrice(context, currentPrice)) return;

        final currentActivePrice = state.currentActivePrice ?? '';
        if (validateActivePrice(context, currentActivePrice)) return;

        final activePrice = currentActivePrice.priceDerivative ?? 0;
        final activeType = state.activationType;
        if (activeType.isGreaterThan &&
            (price as num) < activePrice &&
            action == OrderAction.buy) {
          showErrorMessage(
            VPTradingLocalize
                .current
                .derivative_active_price_must_be_less_than_price,
          );
          return;
        } else if (activeType.isLessThan &&
            (price as num) > activePrice &&
            action == OrderAction.sell) {
          showErrorMessage(
            VPTradingLocalize
                .current
                .derivative_active_price_must_be_greater_than_price,
          );
          return;
        }
        request = request.copyWith(
          conditionInfo: conditionInfo.copyWith(
            activePrice: activePrice,
            price: price,
            activeType: activeType.toParamRequest(),
          ),
        );
        break;
      case DerivativeOrderOptionalType.trailingStop:
        final conditionState =
            context.read<DerivativeValidateConditionOrderCubit>().state;

        final currentRange = conditionState.currentRange ?? '';

        final currentStepPrice = conditionState.currentStepPrice ?? '';

        if (conditionState.isErrorRange) {
          context.read<DerivativeValidateConditionOrderCubit>().focusField(
            FocusKeyboard.range,
          );
          context.read<DerivativeValidateConditionOrderCubit>().onChangeRange(
            currentRange,
          );
          return;
        }
        request = request.copyWith(
          conditionInfo: conditionInfo.copyWith(
            activeType: 'C',
            priceStep: currentStepPrice.priceDerivative,
            deltaValue: currentRange.priceDerivative,
            deltaType: DeltaType.D.toServer,
          ),
        );
        break;
      case DerivativeOrderOptionalType.stopLossOrTakeProfit:
        final conditionState =
            context.read<DerivativeValidateConditionOrderCubit>().state;

        final currentTakeProfit = conditionState.currentTakeProfit ?? '';

        final currentStopLoss = conditionState.currentStopLoss ?? '';

        final currentConditionStopLoss =
            conditionState.currentConditionStopLoss ?? '';

        if (conditionState.isErrorTakeProfit) {
          context.read<DerivativeValidateConditionOrderCubit>().focusField(
            FocusKeyboard.takeProfit,
          );
          context.read<DerivativeValidateConditionOrderCubit>().onChangePrice(
            currentTakeProfit,
          );
          return;
        }

        if (conditionState.isErrorStopLoss) {
          context.read<DerivativeValidateConditionOrderCubit>().focusField(
            FocusKeyboard.stopLoss,
          );
          context.read<DerivativeValidateConditionOrderCubit>().onChangePrice(
            currentStopLoss,
            stopLoss: true,
          );
          return;
        }

        if (conditionState.isErrorConditionStopLoss) {
          context.read<DerivativeValidateConditionOrderCubit>().focusField(
            FocusKeyboard.conditionStopLoss,
          );
          context.read<DerivativeValidateConditionOrderCubit>().onChangePrice(
            currentConditionStopLoss,
            conditionStopLoss: true,
          );
          return;
        }

        if (action == OrderAction.buy) {
          if ((currentTakeProfit.priceDerivative ?? 0) <= (price as num)) {
            showErrorMessage(
              VPTradingLocalize.current.takeProfitMustBeGreaterThanPrice,
            );
            return;
          }
          if ((currentConditionStopLoss.priceDerivative ?? 0) >= price) {
            showErrorMessage(
              VPTradingLocalize.current.conditionalPriceMustBeLessThanPrice,
            );
            return;
          }
          if ((currentStopLoss.priceDerivative ?? 0) >
              (currentConditionStopLoss.priceDerivative ?? 0)) {
            showErrorMessage(
              VPTradingLocalize
                  .current
                  .stopLossCannotLessThanStopLossActivePrice,
            );
            return;
          }
          if ((currentStopLoss.priceDerivative ?? 0) >=
              (currentTakeProfit.priceDerivative ?? 0)) {
            showErrorMessage(
              VPTradingLocalize.current.stopLossMustBeLessThanTakeProfit,
            );
            return;
          }
        } else {
          if ((currentTakeProfit.priceDerivative ?? 0) >= (price as num)) {
            showErrorMessage(
              VPTradingLocalize.current.takeProfitMustBeLessThanPrice,
            );

            return;
          }
          if ((currentConditionStopLoss.priceDerivative ?? 0) <= price) {
            showErrorMessage(
              VPTradingLocalize.current.conditionalPriceMustBeGreaterThanPrice,
            );

            return;
          }
          if ((currentStopLoss.priceDerivative ?? 0) <
              (currentConditionStopLoss.priceDerivative ?? 0)) {
            showErrorMessage(
              VPTradingLocalize.current.stopLossProfitCannotBeGreaterThanPrice,
            );
            return;
          }
          if ((currentStopLoss.priceDerivative ?? 0) <=
              (currentTakeProfit.priceDerivative ?? 0)) {
            showErrorMessage(
              VPTradingLocalize.current.stopLossMustBeGreaterThanTakeProfit,
            );
            return;
          }
        }

        request = request.copyWith(
          conditionInfo: conditionInfo.copyWith(
            price: price,
            priceTP: currentTakeProfit.priceDerivative,
            priceSL: currentStopLoss.priceDerivative,
            split: 'true',
            activepriceSL: currentConditionStopLoss.priceDerivative,
          ),
        );
        break;
      case DerivativeOrderOptionalType.regular:
        break;
    }

    if (DerivativeAppConfigs().userConfig.isShowNotifyConfirmOrder) {
      await showDialog(
        context: context,
        builder:
            (_) => PlaceDerivativeConditionOrderConfirmDialog(
              requestModel: request,
              orderType: orderType,
              onConfirm: (showConfirmOrder) {
                DerivativeAppConfigs().userConfig.setNotifyConfirmOrder(
                  showConfirmOrder,
                );
                updateUserConfig();
                context.read<PlaceOrderSubmitCubit>().submitPlaceConditionOrder(
                  request: request,
                  isDerivative: true,
                );
              },
            ),
      );
    } else {
      context.read<PlaceOrderSubmitCubit>().submitPlaceConditionOrder(
        request: request,
        isDerivative: true,
      );
    }
  }
}
