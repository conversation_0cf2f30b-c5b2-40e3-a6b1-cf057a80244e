import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/cubit/derivative/derivative_order/derivative_order_cubit.dart';
import 'package:vp_trading/cubit/derivative/validate_order/derivative_validate_order_cubit.dart';
import 'package:vp_trading/model/positions/order_optional_type_model.dart';
import 'package:vp_trading/screen/place_order/derivative/available_trade/derivative_available_trade_label.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/derivatives_book_section.dart';
import 'package:vp_trading/screen/place_order/derivative/input_view/derivative_price_volume_widget.dart';
import 'package:vp_trading/screen/place_order/derivative/positions/all_open_position.dart';
import 'package:vp_trading/screen/place_order/derivative/positions/unpaid_interest.dart';
import 'package:vp_trading/screen/place_order/derivative/widget/bottom_sheet_select_type/bottom_sheet_select_type.dart';
import 'package:vp_trading/screen/place_order/widgets/save_command/save_command_widget.dart';

class DerivativeOrderEntryFormView extends StatelessWidget {
  const DerivativeOrderEntryFormView({super.key});

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: vpColor.backgroundElevation0,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
        child: Column(
          children: [
            BlocBuilder<DerivativeOrderCubit, DerivativeOrderState>(
              builder: (context, state) {
                final currentType = state.orderOptionalCurrentType;
                return Row(
                  children: [
                    Expanded(
                      child: VPDropdownView.large(
                        value: currentType.type.shortName,
                        onTap:
                            () => onShowBottomSheetSelectType(
                              context,
                              state.optionalTypeList,
                            ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Column(
                        children: [
                          AllOpenPosition(),
                          SizedBox(height: 2),
                          UnpaidInterest(),
                        ],
                      ),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 16),
            const DerivativeAvailableTradeLabel(),
            const SizedBox(height: 16),
            const DerivativePriceVolumeWidget(),
            const SizedBox(height: 12),
            const SaveCommandWidget(),
            const SizedBox(height: 12),
            const DerivativesBookSection(),
          ],
        ),
      ),
    );
  }

  Future onShowBottomSheetSelectType(
    BuildContext context,
    List<OrderOptionalTypeModel> type,
  ) async {
    final value = await showBottomSheetSelectType<OrderOptionalTypeModel>(
      context: context,
      transactionType: type,
    );

    if (!context.mounted) return;

    context.read<DerivativeOrderCubit>().onUpdateCurrentType(value);
    context.read<DerivativeValidateOrderCubit>().clearData();
  }
}
