import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_helper.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/derivative/derivatives_order_book/derivatives_order_book_cubit.dart';
import 'package:vp_trading/cubit/derivative/derivatives_order_book_multi_select/derivatives_order_book_multi_select_cubit.dart';
import 'package:vp_trading/cubit/order_container/delete_update_order/delete_update_order_cubit.dart';
import 'package:vp_trading/model/enum/market_type.dart';
import 'package:vp_trading/model/request/order/delete_order_request.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/regular_order_book/widget/derivatives_order_list.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/regular_order_book/widget/derivatives_order_multi_select_bottom_bar.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/regular_order_book/widget/derivatives_order_title_widget.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/widget/no_data_derivatives_order_book.dart';
import 'package:vp_trading/widgets/command_history_loading_widget.dart';

class RegularOrderPage extends StatefulWidget {
  const RegularOrderPage({super.key});

  @override
  State<RegularOrderPage> createState() => _RegularOrderPageState();
}

class _RegularOrderPageState extends State<RegularOrderPage> {
  late DerivativesOrderBookCubit _cubit;
  late DeleteUpdateOrderCubit _deleteUpdateOrderCubit;
  late DerivativesOrderBookMultiSelectCubit _multiSelectCubit;

  @override
  void initState() {
    super.initState();
    _cubit = context.read<DerivativesOrderBookCubit>();
    _deleteUpdateOrderCubit = DeleteUpdateOrderCubit();
    _multiSelectCubit = DerivativesOrderBookMultiSelectCubit();
  }

  @override
  void dispose() {
    _multiSelectCubit.close();
    super.dispose();
  }

  void _handleEnterMultiSelectMode() {
    _multiSelectCubit.enterMultiSelectMode();
  }

  void _handleExitMultiSelectMode() {
    _multiSelectCubit.exitMultiSelectMode();
  }

  Future<void> _handleCancelAllOrders(
    String listOrderId,
    int orderCount,
  ) async {
    dialogConfirmDeleteMultipleOrders(context, orderCount, () async {
      _deleteUpdateOrderCubit.deleteAllOrder(
        DeleteOrderRequest(
          accountId:
              GetIt.instance<SubAccountCubit>().derivativeActiveAccount?.id,
          requestId: "app_${AppHelper().genXRequestID()}",
          via: "V",
          orderId: listOrderId,
          market: MarketType.derivatives.nameServer,
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => _deleteUpdateOrderCubit,
      child: BlocBuilder<DerivativesOrderBookCubit, DerivativesOrderBookState>(
        builder: (context, orderState) {
          return BlocBuilder<
            DerivativesOrderBookMultiSelectCubit,
            DerivativesOrderBookMultiSelectState
          >(
            bloc: _multiSelectCubit,
            builder: (context, multiSelectState) {
              return Scaffold(
                body: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                  child: Column(
                    children: [
                      if (orderState.isLoading)
                        const Expanded(child: CommandHistoryLoadingWidget()),
                      if (!orderState.isLoading && orderState.listItems.isEmpty)
                        Expanded(
                          child: PullToRefreshView(
                            onRefresh: () async {
                              await _cubit.loadData();
                            },
                            child: const NoDataDerivativesOrderBook(),
                          ),
                        ),
                      if (orderState.listItems.isNotEmpty &&
                          !orderState.isLoading) ...[
                        // Filter button
                        DerivativesOrderTitleWidget(
                          expandTitleWidget: const [10, 6, 7, 12],
                          showTitleDeleteAll: true,
                          onDeleteAll:
                              () => _handleCancelAllOrders(
                                orderState.listOrderIdCancel.join(','),
                                0,
                              ),
                          onMultiSelectMode: _handleEnterMultiSelectMode,
                          isEnableCancelButton: orderState.isEnableCancelButton,
                        ),

                        const SizedBox(height: 8),
                        Expanded(
                          child: PullToRefreshView(
                            onRefresh: () async {
                              await _cubit.loadData();
                            },
                            child: DerivativesOrderList(
                              items: orderState.listItems,
                              refresh: () async {
                                await _cubit.loadData();
                              },
                              editSuccess: () async {
                                await _cubit.loadData();
                              },
                              isMultiSelectMode:
                                  multiSelectState.isMultiSelectMode,
                              isOrderSelected:
                                  _multiSelectCubit.isOrderSelected,
                              onSelectionChanged:
                                  _multiSelectCubit.toggleOrderSelection,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                bottomNavigationBar:
                    multiSelectState.isMultiSelectMode
                        ? DerivativesOrderMultiSelectBottomBar(
                          selectedCount: multiSelectState.selectedCount,
                          onBack: _handleExitMultiSelectMode,
                          onCancelOrders:
                              () => _handleCancelAllOrders(
                                multiSelectState.selectedOrderIds.join(','),
                                multiSelectState.selectedOrderIds.length,
                              ),
                          isLoading: multiSelectState.isCancelling,
                        )
                        : null,
              );
            },
          );
        },
      ),
    );
  }
}
