import 'package:flutter/material.dart';
import 'package:vp_common/generated/l10n.dart';
import 'package:vp_common/utils/format_utils.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/enum/condition_order_type_fu_enum.dart';

void dialogConfirmDeleteFuConditionOrder(
  BuildContext context,
  ConditionOrderBookModel item,
  VoidCallback onConfirmCallback,
) async {
  VPPopup.custom(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Hủy lệnh',
              style: context.textStyle.headineBold6?.copyWith(
                color: vpColor.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Column(
              children: [
                _buildInfoRow(
                  context,
                  label: 'Mã hợp đồng',
                  value: item.symbol ?? '-',
                ),
                const SizedBox(height: 8),
                _buildInfoRow(
                  context,
                  label: 'Điều kiện',
                  value: (item.conditionOrderTypeFuEnum.title),
                ),
                const SizedBox(height: 8),
                _buildInfoRow(
                  context,
                  label: 'Loại lệnh',
                  value:
                      item.orderTypeFUEnum == OrderTypeEnum.buy
                          ? 'LONG'
                          : 'SHORT',
                  valueColor:
                      item.orderTypeFUEnum == OrderTypeEnum.buy
                          ? vpColor.textAccentGreen
                          : vpColor.textAccentRed,
                ),
                const SizedBox(height: 8),
                _buildInfoRow(
                  context,
                  label: 'Số lượng',
                  value: '${item.qty ?? 0}',
                ),
                ..._buildOrderTypeSpecificFields(context, item),
              ],
            ),
            const SizedBox(height: 16),
          ],
        ),
      )
      .copyWith(
        button: VpsButton.secondaryXsSmall(
          title: 'Đóng',
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      )
      .copyWith(
        button: VpsButton.primaryDangerXsSmall(
          title: VPCommonLocalize.current.confirm,
          onPressed: () {
            Navigator.of(context).pop();
            onConfirmCallback();
          },
        ),
      )
      .showDialog(context);
}

Widget _buildInfoRow(
  BuildContext context, {
  required String label,
  required String value,
  Color? valueColor,
}) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Text(
        label,
        style: context.textStyle.body14?.copyWith(color: vpColor.textSecondary),
      ),
      Text(
        value,
        style: context.textStyle.subtitle14?.copyWith(
          color: valueColor ?? vpColor.textPrimary,
          fontWeight: FontWeight.w600,
        ),
      ),
    ],
  );
}

List<Widget> _buildOrderTypeSpecificFields(
  BuildContext context,
  ConditionOrderBookModel item,
) {
  switch (item.conditionOrderTypeFuEnum) {
    case ConditionOrderTypeFuEnum.bb:
      return _buildStopLossTakeProfitFields(context, item);
    case ConditionOrderTypeFuEnum.tso:
      return _buildTrailingStopFields(context, item);
    case ConditionOrderTypeFuEnum.sto:
      return _buildStopOrderFields(context, item);
    default:
      return [];
  }
}

List<Widget> _buildStopLossTakeProfitFields(
  BuildContext context,
  ConditionOrderBookModel item,
) {
  return [
    const SizedBox(height: 8),
    _buildInfoRow(context, label: 'Giá đặt', value: item.price ?? '-'),
    const SizedBox(height: 8),
    _buildInfoRow(context, label: 'Giá chốt lời', value: item.priceTP ?? '-'),
    const SizedBox(height: 8),
    _buildInfoRow(context, label: 'Giá cắt lỗ', value: item.priceSL ?? '-'),
    const SizedBox(height: 8),
    _buildInfoRow(
      context,
      label: 'Giá kích hoạt cắt lỗ',
      value: _buildActivationPriceDisplay(item.activepriceSL, item),
    ),
  ];
}

List<Widget> _buildTrailingStopFields(
  BuildContext context,
  ConditionOrderBookModel item,
) {
  return [
    const SizedBox(height: 8),
    _buildInfoRow(context, label: 'Bước giá', value: item.priceStep ?? '-'),
    const SizedBox(height: 8),
    _buildInfoRow(
      context,
      label: 'Biên độ',
      value: '${item.deltaValue ?? '-'}',
    ),
  ];
}

List<Widget> _buildStopOrderFields(
  BuildContext context,
  ConditionOrderBookModel item,
) {
  return [
    const SizedBox(height: 8),
    _buildInfoRow(context, label: 'Giá đặt', value: item.price ?? '-'),
    const SizedBox(height: 8),
    _buildInfoRow(
      context,
      label: 'Giá kích hoạt',
      value: '≤ ${item.activePrice ?? '-'}',
    ),
  ];
}

String _buildActivationPriceDisplay(
  num? activePriceSL,
  ConditionOrderBookModel item,
) {
  if (activePriceSL == null) return '-';
  final formattedPrice =
      FormatUtils.formatNumberMaxTwoDecimals(activePriceSL) ?? '-';
  return item.orderTypeFUEnum == OrderTypeEnum.buy
      ? '≤ $formattedPrice'
      : '≥ $formattedPrice';
}
