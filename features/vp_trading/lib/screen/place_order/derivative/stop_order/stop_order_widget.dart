import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/animation/blink.dart';
import 'package:vp_trading/core/constant/constants.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/derivative/validate_order/derivative_validate_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/available_trade/available_trade_cubit.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/stock_info/stock_info_cubit.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/enum/activation_conditions_type.dart';
import 'package:vp_trading/screen/place_order/awaiting/widget/choice_equal_buy_button.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_box.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_error.dart';
import 'package:vp_trading/utils/text_inputformater.dart';

class StopOrderWidget extends StatefulWidget {
  final TextEditingController priceController;

  const StopOrderWidget({super.key, required this.priceController});

  @override
  State<StopOrderWidget> createState() => StopOrderWidgetState();
}

class StopOrderWidgetState extends State<StopOrderWidget> {
  final ValueNotifier<bool> _priceBlink = ValueNotifier(false);
  final _priceFocusNode = FocusNode();

  void _focusListener() {
    if (_priceFocusNode.hasFocus) {
      context.read<DerivativeValidateOrderCubit>().focusField(
        FocusKeyboard.priceActive,
      );
    } else {
      context.read<DerivativeValidateOrderCubit>().focusField(
        FocusKeyboard.none,
      );
    }
  }

  @override
  void initState() {
    super.initState();
    _priceFocusNode.addListener(_focusListener);
    if (widget.priceController.text.isNotEmpty) {
      context.read<DerivativeValidateOrderCubit>().onChangeActivePrice(
        widget.priceController.text,
      );
    }
  }

  @override
  void dispose() {
    _priceFocusNode.dispose();
    _priceBlink.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<
          DerivativeValidateOrderCubit,
          DerivativeValidateOrderState
        >(
          listenWhen:
              (previous, current) =>
                  previous.currentActivePrice !=
                  current.currentActivePrice,
          listener: (context, state) {
            if (state.currentActivePrice != null) {
              widget.priceController.text = state.currentActivePrice!;
              widget.priceController.selection = TextSelection.fromPosition(
                TextPosition(offset: widget.priceController.text.length),
              );
            }
          },
        ),

        BlocListener<
          DerivativeValidateOrderCubit,
          DerivativeValidateOrderState
        >(
          listenWhen:
              (previous, current) =>
                  previous.focusKeyboard != current.focusKeyboard,
          listener: (context, state) {
            if (state.focusKeyboard == FocusKeyboard.priceActive) {
              _priceFocusNode.requestFocus();
            }
          },
        ),
      ],
      child: BlocBuilder<
        DerivativeValidateOrderCubit,
        DerivativeValidateOrderState
      >(
        builder: (context, state) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    VPTradingLocalize.current.derivative_activation_conditions,
                    style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
                  ),
                  const SizedBox(width: 16),
                  const ButtonLessThan(),
                  const SizedBox(width: 4),
                  const ButtonGreaterThan(),
                  const SizedBox(width: 4),
                  Expanded(
                    child: ColoredBox(
                      color: vpColor.backgroundElevation0,
                      child: Blink(
                        blink: _priceBlink,
                        child: InputFieldBox(
                          onTapField: () {
                            if (state.sessionType != null) {
                              context
                                  .read<DerivativeValidateOrderCubit>()
                                  .clearSession();
                              _priceFocusNode.requestFocus();
                            }
                          },
                          isError:
                              state.errorActivePrice.isError &&
                              widget.priceController.text.isNotEmpty,
                          sessionValue: state.sessionType?.name.toUpperCase(),
                          controller: widget.priceController,
                          hintText: 'Giá',
                          onChange: (value) {
                            context
                                .read<DerivativeValidateOrderCubit>()
                                .onChangeActivePrice(value);
                          },
                          focusNode: _priceFocusNode,
                          onTap: (increase) {
                            context
                                .read<DerivativeValidateOrderCubit>()
                                .priceTap(
                                  text: widget.priceController.text,
                                  increase: increase,
                                  activation: true,
                                );
                          },
                          inputFormatters: [
                            removeZeroStartInputFormatter,
                            LengthLimitingTextInputFormatter(8),
                            ...priceInputFormatter,
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              BlocBuilder<
                DerivativeValidateOrderCubit,
                DerivativeValidateOrderState
              >(
                buildWhen:
                    (previous, current) =>
                        previous.errorActivePrice !=
                            current.errorActivePrice ||
                        previous.currentActivePrice !=
                            current.currentActivePrice,
                builder: (context, state) {
                  return InputFieldError(
                    errorMessage: state.errorActivePrice.message,
                    text: widget.priceController.text,
                    isShake: true,
                  );
                },
              ),
            ],
          );
        },
      ),
    );
  }
}

class ButtonGreaterThan extends StatelessWidget {
  const ButtonGreaterThan({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<
      DerivativeValidateOrderCubit,
      DerivativeValidateOrderState,
      ActivationConditionsType?
    >(
      selector: (state) => state.activationType,
      builder: (_, activationType) {
        return ChoiceEqualButton(
          assetsIcon: VpTradingAssets.icons.icGreaterThanEqual.path,
          isFocus: activationType == ActivationConditionsType.greaterThan,
          onTap:
              () => context
                  .read<DerivativeValidateOrderCubit>()
                  .setActivationConditions(
                    ActivationConditionsType.greaterThan,
                  ),
        );
      },
    );
  }
}

class ButtonLessThan extends StatelessWidget {
  const ButtonLessThan({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<
      DerivativeValidateOrderCubit,
      DerivativeValidateOrderState,
      ActivationConditionsType?
    >(
      selector: (state) => state.activationType,
      builder: (_, activationType) {
        return ChoiceEqualButton(
          assetsIcon: VpTradingAssets.icons.icLessThanEqual.path,
          isFocus: activationType == ActivationConditionsType.lessThan,
          onTap:
              () => context
                  .read<DerivativeValidateOrderCubit>()
                  .setActivationConditions(ActivationConditionsType.lessThan),
        );
      },
    );
  }
}
