import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/cubit/derivative/derivative_positions/derivative_positions_cubit.dart';
import 'package:vp_trading/cubit/place_order/available_trade/available_trade_cubit.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';

class DerivativeAvailableTradeLabel extends StatelessWidget {
  const DerivativeAvailableTradeLabel({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PlaceOrderCubit, PlaceOrderState>(
      builder: (context, statePlaceOrder) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Text(
                  VPTradingLocalize.current.derivative_average_price,
                  style: vpTextStyle.captionMedium.copyColor(
                    vpColor.textPrimary,
                  ),
                ),
                BlocBuilder<DerivativePositionsCubit, DerivativePositionsState>(
                  builder: (context, state) {
                    final vwap = state.currentOpenPosition.vwap;
                    return Text(
                      vwap?.toMoney(showSymbol: false) ?? '--',
                      style: vpTextStyle.captionMedium.copyColor(
                        vpColor.textPrimary,
                      ),
                    );
                  },
                ),
              ],
            ),
            BlocBuilder<AvailableTradeCubit, AvailableTradeState>(
              builder: (context, state) {
                return RichText(
                  text: TextSpan(
                    text: VPTradingLocalize.current.derivative_maximum_weight,
                    style: vpTextStyle.captionMedium.copyColor(
                      vpColor.textPrimary,
                    ),
                    children: <TextSpan>[
                      TextSpan(
                        text: ' ${state.availableTrade?.maxBuyQty ?? 0}L ',
                        style: vpTextStyle.captionMedium.copyColor(
                          vpColor.textPriceGreen,
                        ),
                      ),
                      TextSpan(
                        text: '(HĐ) / ',
                        style: vpTextStyle.captionMedium.copyColor(
                          vpColor.textPrimary,
                        ),
                      ),
                      TextSpan(
                        text: ' ${state.availableTrade?.maxSellQty ?? 0}S ',
                        style: vpTextStyle.captionMedium.copyColor(
                          vpColor.textPriceRed,
                        ),
                      ),
                      TextSpan(
                        text: '(HĐ)',
                        style: vpTextStyle.captionMedium.copyColor(
                          vpColor.textPrimary,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }
}
