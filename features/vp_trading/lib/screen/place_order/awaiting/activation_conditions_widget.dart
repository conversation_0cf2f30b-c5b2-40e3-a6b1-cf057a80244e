import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/animation/blink.dart';
import 'package:vp_trading/core/constant/constants.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/place_order/available_trade/available_trade_cubit.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/stock_info/stock_info_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_condition_order/validate_condition_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/enum/activation_conditions_type.dart';
import 'package:vp_trading/screen/place_order/awaiting/widget/choice_equal_buy_button.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_box.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_error.dart';
import 'package:vp_trading/utils/text_inputformater.dart';

class ActivationConditionsWidget extends StatefulWidget {
  final TextEditingController priceController;

  const ActivationConditionsWidget({super.key, required this.priceController});

  @override
  State<ActivationConditionsWidget> createState() =>
      ActivationConditionsWidgetState();
}

class ActivationConditionsWidgetState
    extends State<ActivationConditionsWidget> {
  late final StreamSubscription<String> _subscriptionPrice;
  late final StreamSubscription<String> _subscriptionSocketPrice;
  final ValueNotifier<bool> _priceBlink = ValueNotifier(false);
  final _priceFocusNode = FocusNode();
  late Throttle _availableTradeThrottle;

  void _focusListener() {
    if (_priceFocusNode.hasFocus) {
      context.read<StockInfoCubit>().onRealtimeChanged(false);
      context.read<ValidateOrderCubit>().focusField(
        FocusKeyboard.priceActive,
      );
    } else {
      context.read<ValidateOrderCubit>().focusField(FocusKeyboard.none);
    }
  }

  @override
  void initState() {
    super.initState();
    _availableTradeThrottle = Throttle(const Duration(milliseconds: 500));
    _priceFocusNode.addListener(_focusListener);
    if (widget.priceController.text.isNotEmpty) {
      context.read<ValidateOrderCubit>().onChangeActivationPrice(
        widget.priceController.text,
      );
    }
    _subscriptionPrice = context.read<PlaceOrderCubit>().priceStream.listen((
      data,
    ) {
      if (!mounted) return;

      if (context.read<ValidateOrderCubit>().state.sessionType == null) {
        context.read<ValidateOrderCubit>().onChangeActivationPrice(data);
      } else {
        context.read<ValidateOrderCubit>().clearSession();
        context.read<ValidateOrderCubit>().onChangeActivationPrice(data);
      }
      if (data.isEmpty) {
        context.read<ValidateOrderCubit>().onChangeActivationPrice("");
        context.read<ValidateOrderCubit>().onChangeVolumne("");
      } else {
        context.read<StockInfoCubit>().onRealtimeChanged(false);
      }
      //  context.read<ValidateOrderCubit>().clearSession();
    });
    _subscriptionSocketPrice = context
        .read<StockInfoCubit>()
        .priceStream
        .listen((data) {
          if (!mounted) return;
          if (context.read<ValidateOrderCubit>().state.sessionType == null) {
            context.read<ValidateOrderCubit>().onChangeActivationPrice(data);
          } else {
            context.read<ValidateOrderCubit>().clearSession();
            context.read<ValidateOrderCubit>().onChangeActivationPrice(data);
          }
        });
  }

  @override
  void dispose() {
    _availableTradeThrottle.dispose();

    _priceFocusNode.dispose();
    _priceBlink.dispose();
    _subscriptionPrice.cancel();
    _subscriptionSocketPrice.cancel();
    super.dispose();
  }

  void onChangeAvailableTrade() {
    var subAccountType = context.read<PlaceOrderCubit>().state.subAccountType;
    _availableTradeThrottle(() {
      context.read<AvailableTradeCubit>().getAvailableTrade(
        accountId: subAccountType.toSubAccountModel()?.id ?? "",
        symbol: context.read<PlaceOrderCubit>().state.symbol,
        quotePrice: widget.priceController.text.price.toString(),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<ValidateOrderCubit, ValidateOrderState>(
          listenWhen:
              (previous, current) =>
                  previous.currentActivationPrice !=
                  current.currentActivationPrice,
          listener: (context, state) {
            if (state.currentActivationPrice != null) {
              widget.priceController.text = state.currentActivationPrice!;
              widget.priceController.selection = TextSelection.fromPosition(
                TextPosition(offset: widget.priceController.text.length),
              );
              onChangeAvailableTrade();
            }
          },
        ),

        BlocListener<ValidateOrderCubit, ValidateOrderState>(
          listenWhen:
              (previous, current) =>
                  previous.focusKeyboard != current.focusKeyboard,
          listener: (context, state) {
            if (state.focusKeyboard == FocusKeyboard.priceActive) {
              _priceFocusNode.requestFocus();
            }
          },
        ),
      ],
      child: BlocBuilder<ValidateOrderCubit, ValidateOrderState>(
        builder: (context, state) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  VPTradingLocalize.current.trading_activation_conditions,
                  style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    const ButtonLessThan(),
                    const SizedBox(width: 4),
                    const ButtonGreaterThan(),
                    const SizedBox(width: 4),
                    Expanded(
                      child: ColoredBox(
                        color: vpColor.backgroundElevation0,
                        child: Blink(
                          blink: _priceBlink,
                          child: InputFieldBox(
                            onTapField: () {
                              if (state.sessionType != null) {
                                context
                                    .read<ValidateOrderCubit>()
                                    .clearSession();
                                _priceFocusNode.requestFocus();
                              }
                            },
                            isError:
                                state.errorActivationPrice.isError &&
                                widget.priceController.text.isNotEmpty,
                            sessionValue: state.sessionType?.name.toUpperCase(),
                            controller: widget.priceController,
                            hintText: 'Giá',
                            onChange: (value) {
                              context.read<StockInfoCubit>().onRealtimeChanged(
                                false,
                              );
                              context
                                  .read<ValidateOrderCubit>()
                                  .onChangeActivationPrice(value);
                            },
                            focusNode: _priceFocusNode,
                            onTap: (increase) {
                              context.read<StockInfoCubit>().onRealtimeChanged(
                                false,
                              );

                              context.read<ValidateOrderCubit>().priceTap(
                                text: widget.priceController.text,
                                increase: increase,
                                activation: true,
                              );
                            },
                            inputFormatters: [
                              removeZeroStartInputFormatter,
                              LengthLimitingTextInputFormatter(8),
                              ...priceInputFormatter,
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                BlocBuilder<ValidateOrderCubit, ValidateOrderState>(
                  buildWhen:
                      (previous, current) =>
                          previous.errorActivationPrice !=
                              current.errorActivationPrice ||
                          previous.currentActivationPrice !=
                              current.currentActivationPrice,
                  builder: (context, state) {
                    return InputFieldError(
                      errorMessage: state.errorActivationPrice.message,
                      text: widget.priceController.text,
                      isShake: true,
                    );
                  },
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

class ButtonGreaterThan extends StatelessWidget {
  const ButtonGreaterThan({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<
      ValidateConditionOrderCubit,
      ValidateConditionOrderState,
      ActivationConditionsType?
    >(
      selector: (state) => state.activationType,
      builder: (_, activationType) {
        return ChoiceEqualButton(
          assetsIcon: VpTradingAssets.icons.icGreaterThanEqual.path,
          isFocus: activationType == ActivationConditionsType.greaterThan,
          onTap:
              () => context
                  .read<ValidateConditionOrderCubit>()
                  .setActivationConditions(
                    ActivationConditionsType.greaterThan,
                  ),
        );
      },
    );
  }
}

class ButtonLessThan extends StatelessWidget {
  const ButtonLessThan({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<
      ValidateConditionOrderCubit,
      ValidateConditionOrderState,
      ActivationConditionsType?
    >(
      selector: (state) => state.activationType,
      builder: (_, activationType) {
        return ChoiceEqualButton(
          assetsIcon: VpTradingAssets.icons.icLessThanEqual.path,
          isFocus: activationType == ActivationConditionsType.lessThan,
          onTap:
              () => context
                  .read<ValidateConditionOrderCubit>()
                  .setActivationConditions(ActivationConditionsType.lessThan),
        );
      },
    );
  }
}
