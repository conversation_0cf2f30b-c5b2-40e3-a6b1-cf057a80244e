import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/constant/constants.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/submit/place_order_submit_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/screen/place_order/widgets/bottom/mixin/order_type_mixin.dart';

class OrderButton extends StatefulWidget {
  const OrderButton({super.key});

  @override
  State<OrderButton> createState() => _OrderButtonState();
}

class _OrderButtonState extends State<OrderButton>
    with SingleTickerProviderStateMixin, OrderTypeMixin {
  void onTap(BuildContext context) async {
    final orderType = context.read<PlaceOrderCubit>().state.orderType;

    switch (orderType) {
      case OrderType.lo:
        await orderLo(context);
        return;
      case OrderType.gtc:
      case OrderType.stopLoss:
      case OrderType.takeProfit:
      case OrderType.waiting:
        await orderCondition(context, orderType);
        return;
      case OrderType.buyIn:
        return;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PlaceOrderCubit, PlaceOrderState>(
      builder: (context, state) {
        return BlocBuilder<ValidateOrderCubit, ValidateOrderState>(
          builder: (context, stateValidate) {
            final isEnable = stateValidate.isValid;
            return BlocBuilder<PlaceOrderSubmitCubit, PlaceOrderSubmitState>(
              builder: (context, _) {
                return state.action == OrderAction.buy
                    ? VpsButton.primarySmall(
                      dismissKeyboardWhenTap: true,
                      onPressed: () => onTap(context),
                      //    disabled: !isEnable,
                      title: state.action.orderTitleButton,
                    )
                    : VpsButton.primaryDangerSmall(
                      dismissKeyboardWhenTap: true,
                      onPressed: () => onTap(context),
                      title: state.action.orderTitleButton,
                      //     disabled: !isEnable,
                    );
              },
            );
          },
        );
      },
    );
  }

  @override
  bool validatePrice(BuildContext context, String price) {
    if (context.read<ValidateOrderCubit>().state.isErrorPrice) {
      context.read<ValidateOrderCubit>().focusField(FocusKeyboard.price);
      context.read<ValidateOrderCubit>().onChangePrice(price, isOrder: true);
      return true;
    }
    return false;
  }

  @override
  bool validateVolume(BuildContext context, String volume) {
    if (context.read<ValidateOrderCubit>().state.isErrorVolume) {
      context.read<ValidateOrderCubit>().focusField(FocusKeyboard.volume);
      context.read<ValidateOrderCubit>().onChangeVolumne(volume, isOrder: true);
      return true;
    }
    return false;
  }

  @override
  bool validateActivationPrice(BuildContext context, String price) {
    if (context.read<ValidateOrderCubit>().state.isErrorActivationPrice) {
      context.read<ValidateOrderCubit>().focusField(
        FocusKeyboard.priceActive,
      );

      context.read<ValidateOrderCubit>().onChangeActivationPrice(
        price,
        isOrder: true,
      );
      return true;
    }
    return false;
  }
}
