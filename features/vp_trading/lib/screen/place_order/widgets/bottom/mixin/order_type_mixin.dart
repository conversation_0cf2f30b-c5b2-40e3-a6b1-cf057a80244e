import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/constant/constants.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/submit/place_order_submit_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_condition_order/validate_condition_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/enum/activation_conditions_type.dart';
import 'package:vp_trading/model/order/gtc_old/pre_check_order_request.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/model/order/request/order_request_model.dart';
import 'package:vp_trading/screen/place_order/widgets/bottom/mixin/validate_text_field_mixin.dart';
import 'package:vp_trading/screen/place_order/widgets/confirm/place_condition_order_confirm_dialog.dart';
import 'package:vp_trading/screen/place_order/widgets/confirm/place_order_confirm_dialog.dart';

const isGtcOld = true;

mixin OrderTypeMixin implements ValidateTextFieldMixin {
  Future<void> orderLo(BuildContext context) async {
    final isEnable = context.read<ValidateOrderCubit>().state.isValid;
    if (!isEnable) {
      if (context.read<ValidateOrderCubit>().state.isErrorPrice) {
        context.read<ValidateOrderCubit>().focusField(FocusKeyboard.price);
      } else {
        context.read<ValidateOrderCubit>().focusField(FocusKeyboard.volume);
      }

      return;
    }

    final sessionType = context.read<ValidateOrderCubit>().state.sessionType;
    final subAccountType = context.read<PlaceOrderCubit>().state.subAccountType;
    final symbol = context.read<PlaceOrderCubit>().state.symbol;
    final side = context.read<PlaceOrderCubit>().state.action.value;
    final currentPrice =
        context.read<ValidateOrderCubit>().state.currentPrice ?? "0";
    final currentVolume =
        context.read<ValidateOrderCubit>().state.currentVolume;
    final price =
        sessionType != null
            ? sessionType.name.toUpperCase()
            : currentPrice.price;
    final subAccountId = subAccountType.toSubAccountModel()?.id ?? "";
    final request = OrderRequestModel(
      qty: (currentVolume?.volume ?? 0).toInt(),
      price: price,
      symbol: symbol,
      market: 'equity',
      side: side,
      type: sessionType != null ? 'market' : "limit",
      username: GetIt.instance<AuthCubit>().userInfo?.userinfo?.username ?? '',
      accountId: subAccountId,
    );

    if (context.read<PlaceOrderSubmitCubit>().showConfirmOrderMessage) {
      await showDialog(
        context: context,
        builder:
            (_) => PlaceOrderConfirmDialog(
              price: sessionType?.name ?? currentPrice,
              value: context.read<ValidateOrderCubit>().state.calculateValue,
              requestModel: request,
              onConfirm: (showConfirmOrder) {
                context.read<PlaceOrderSubmitCubit>().showConfirmOrderMessage =
                    showConfirmOrder;
                SharedPref.setBool(
                  KeyShared.showConfirmOrderMessage,
                  showConfirmOrder,
                );
                context.read<PlaceOrderSubmitCubit>().submitPlaceOrder(
                  request: request,
                );
              },
            ),
      );
    } else {
      context.read<PlaceOrderSubmitCubit>().submitPlaceOrder(request: request);
    }
  }

  Future<void> orderCondition(BuildContext context, OrderType orderType) async {
    final sessionType = context.read<ValidateOrderCubit>().state.sessionType;
    final subAccountType = context.read<PlaceOrderCubit>().state.subAccountType;
    final symbol = context.read<PlaceOrderCubit>().state.symbol;
    final side = context.read<PlaceOrderCubit>().state.action.value;
    final currentPrice =
        context.read<ValidateOrderCubit>().state.currentPrice ?? "0";
    final currentVolume =
        context.read<ValidateOrderCubit>().state.currentVolume;
    final fromDate =
        context.read<ValidateConditionOrderCubit>().state.fromDate ?? '';
    final triggerType =
        context.read<ValidateConditionOrderCubit>().state.triggerConditionEnum;
    final toDate =
        context.read<ValidateConditionOrderCubit>().state.toDate ?? '';
    final subAccountString =
        context.read<PlaceOrderCubit>().state.subAccountType.shortName;
    final price =
        sessionType != null
            ? sessionType.name.toUpperCase()
            : currentPrice.price;
    final subAccountId = subAccountType.toSubAccountModel()?.id ?? "";

    var conditionInfo = ConditionInfo(
      symbol: symbol,
      qty: (currentVolume?.volume ?? 0).toInt(),
      side: side,
      type: sessionType != null ? 'market' : "limit",
      price: price,
      fromDate: fromDate,
      toDate: toDate,
    );

    var request = ConditionOrderRequestModel(
      market: 'equity',
      accountId: subAccountId,
      orderType: orderType.toServer,
      conditionInfo: conditionInfo,
    );

    var requestGtcOld = PreCheckOrderRequest(
      instrument: symbol,
      volume: (currentVolume?.volume ?? 0).toInt(),
      order: side,
      type: sessionType != null ? 'market' : "limit",
      price: price,
      buyIn: 'N',
    );

    switch (orderType) {
      case OrderType.stopLoss:
      case OrderType.takeProfit:
        final orderPriceAbs =
            context.read<ValidateConditionOrderCubit>().state.orderPriceAbs;
        final slipPagePrice =
            context.read<ValidateConditionOrderCubit>().state.slippagePrice;
        final rateProfit =
            context.read<ValidateConditionOrderCubit>().state.rateProfit;
        final costPrice =
            context.read<ValidateConditionOrderCubit>().state.costPrice;
        final isRateProfit =
            context.read<ValidateConditionOrderCubit>().state.isRateProfit;
        final isErrorPriceOrder =
            context.read<ValidateConditionOrderCubit>().state.isErrorPriceOrder;
        final activeType =
            orderType.isTakeProfit
                ? ActivationConditionsType.greaterThan.toParamRequest()
                : ActivationConditionsType.lessThan.toParamRequest();
        num? stopLossRate;
        num? stopLossPriceAmp;

        if (isRateProfit) {
          stopLossRate = rateProfit;
        } else {
          stopLossPriceAmp = rateProfit * 1000;
        }

        /// Validate pre order
        if (context.read<ValidateConditionOrderCubit>().state.isErrorTrigger) {
          context.read<ValidateConditionOrderCubit>().focusField(
            FocusKeyboard.trigger,
          );
          final currentTrigger =
              context.read<ValidateConditionOrderCubit>().state.currentTrigger;
          context.read<ValidateConditionOrderCubit>().onChangeTrigger(
            currentTrigger ?? '',
            isOrder: true,
          );
          return;
        }

        if (validateVolume(context, currentVolume ?? '')) return;

        if (isErrorPriceOrder) return;

        request = request.copyWith(
          conditionInfo: conditionInfo.copyWith(
            price: orderPriceAbs,
            stopLossRate: stopLossRate,
            stopLossPriceAmp: stopLossPriceAmp,
            slipPagePrice: slipPagePrice * 1000,
            activeType: activeType,
            costPrice: costPrice,
          ),
        );
        break;
      case OrderType.waiting:

        /// Validate pre order
        final currentActivationPrice =
            context.read<ValidateOrderCubit>().state.currentActivationPrice ??
            '';
        if (validateActivationPrice(context, currentActivationPrice)) return;

        if (validatePrice(context, currentPrice)) return;

        if (validateVolume(context, currentVolume ?? '')) return;

        final activePrice =
            context.read<ValidateOrderCubit>().state.currentActivationPrice ??
            '0';
        final activeType =
            context.read<ValidateConditionOrderCubit>().state.activationType;
        request = request.copyWith(
          conditionInfo: conditionInfo.copyWith(
            activePrice: ((double.tryParse(activePrice) ?? 0) * 1000).toInt(),
            activeType: activeType.toParamRequest(),
          ),
        );
        break;
      case OrderType.lo:
      case OrderType.gtc:
        if (validatePrice(context, currentPrice)) return;
        if (validateVolume(context, currentVolume ?? '')) return;
      case OrderType.buyIn:
        break;
    }

    if (context.read<PlaceOrderSubmitCubit>().showConfirmOrderMessage) {
      String orderPriceDisplay =
          context.read<ValidateConditionOrderCubit>().state.orderPriceDisplay;
      await showDialog(
        context: context,
        builder:
            (_) => PlaceConditionOrderConfirmDialog(
              price:
                  orderType == OrderType.waiting
                      ? (sessionType?.name ?? currentPrice)
                      : orderPriceDisplay,
              value: context.read<ValidateOrderCubit>().state.calculateValue,
              requestModel: request,
              subAccountString: subAccountString,
              triggerType: triggerType,
              orderType: orderType,
              onConfirm: (showConfirmOrder) {
                context.read<PlaceOrderSubmitCubit>().showConfirmOrderMessage =
                    showConfirmOrder;
                SharedPref.setBool(
                  KeyShared.showConfirmOrderMessage,
                  showConfirmOrder,
                );
                if (orderType.isGtc) {
                  context
                      .read<PlaceOrderSubmitCubit>()
                      .submitPlaceConditionOrderGtcOld(
                        request: requestGtcOld,
                        accountId: subAccountId,
                        startDate: fromDate,
                        endDate: toDate,
                      );
                  return;
                }
                context.read<PlaceOrderSubmitCubit>().submitPlaceConditionOrder(
                  request: request,
                );
              },
            ),
      );
    } else {
      if (orderType.isGtc) {
        context.read<PlaceOrderSubmitCubit>().submitPlaceConditionOrderGtcOld(
          request: requestGtcOld,
          accountId: subAccountId,
          startDate: fromDate,
          endDate: toDate,
        );
        return;
      }
      context.read<PlaceOrderSubmitCubit>().submitPlaceConditionOrder(
        request: request,
      );
    }
  }
}
