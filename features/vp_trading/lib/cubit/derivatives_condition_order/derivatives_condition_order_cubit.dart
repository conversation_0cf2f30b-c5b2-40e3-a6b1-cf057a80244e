import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:vp_common/error/show_error.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/helper/derivative_order_socket_mixin.dart';
import 'package:vp_stock_common/model/socket/socket_der_condition_order_data.dart';
import 'package:vp_trading/core/repository/command_history_repository.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/model/request/order/order_book_request.dart';

part 'derivatives_condition_order_state.dart';

class DerivativesConditionOrderCubit
    extends Cubit<DerivativesConditionOrderState>
    with DerivativeOrdersSocketMixin {
  DerivativesConditionOrderCubit()
    : super(const DerivativesConditionOrderState());

  final CommandHistoryRepository _repository =
      GetIt.instance<CommandHistoryRepository>();

  void init() {
    if (GetIt.instance<SubAccountCubit>().derivativeActiveAccount == null) {
      emit(state.copyWith(listItems: []));
    } else {
      // Subscribe to WebSocket for order status updates
      subscribeDerivativeOrderSocket();
      loadData();
    }
  }

  Future<void> loadData({bool showLoading = true}) async {
    try {
      if (showLoading) {
        emit(state.copyWith(isLoading: true));
      }

      final result = await _repository.getFuConditionOrderBook(
        state.request.accountId?.isNotEmpty == true
            ? state.request
            : OrderBookRequest(
              accountId:
                  GetIt.instance<SubAccountCubit>().derivativeActiveAccount?.id,
              pageNo: 1,
              pageSize: 1000,
            ),
      );

      if (result.isSuccess) {
        final items = result.data?.content ?? [];

        emit(
          state.copyWith(
            isLoading: false,
            listItems: items,
            // Reset selection when reloading data
            selectedItems: {},
            isSelectAll: false,
          ),
        );

        // Update cancel button state after loading data
        updateEnableCancelButton();
      } else {
        emit(state.copyWith(isLoading: false, errorMessage: result.message));
      }
    } catch (e) {
      showError(e);
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: await getErrorMessage(e),
        ),
      );
      debugPrintStack(stackTrace: StackTrace.current);
    }
  }

  void toggleSelectAll(bool isSelectAll) {
    emit(
      state.copyWith(
        isSelectAll: isSelectAll,
        selectedItems: isSelectAll ? Set.from(state.listItems) : {},
      ),
    );
  }

  void toggleSelectItem(ConditionOrderBookModel item) {
    final selectedItems = Set<ConditionOrderBookModel>.from(
      state.selectedItems,
    );

    if (selectedItems.contains(item)) {
      selectedItems.remove(item);
    } else {
      selectedItems.add(item);
    }

    emit(
      state.copyWith(
        selectedItems: selectedItems,
        isSelectAll: selectedItems.length == state.listItems.length,
      ),
    );
  }

  void toggleMultiSelectMode() {
    emit(
      state.copyWith(
        isMultiSelectMode: !state.isMultiSelectMode,
        selectedItems: {},
        isSelectAll: false,
      ),
    );
  }

  void applyFilter(OrderBookRequest filterRequest) {
    emit(state.copyWith(request: filterRequest));
    loadData();
  }

  void resetErrorMessage() {
    emit(state.copyWith(errorMessage: null));
  }

  void updateEnableCancelButton() {
    final hasOrdersWithCancelCapability = state.listItems.any(
      (order) => order.isEnableCancel,
    );
    emit(state.copyWith(isEnableCancelButton: hasOrdersWithCancelCapability));
  }

  @override
  void onDerivativeOrderListener(VPDerConditionOrderData data) {
    // Reload order data when receiving WebSocket order status updates without loading indicator
    loadData(showLoading: false);
  }

  @override
  Future<void> close() {
    // Unsubscribe from WebSocket when cubit is closed
    unsubscribeDerivativeOrderSocket();
    return super.close();
  }
}
