import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/constant/constants.dart';
import 'package:vp_trading/core/constant/derivative_app_configs.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/derivative/mixin/derivative_update_user_config_mixin.dart';
import 'package:vp_trading/model/available_trade/available_trade_model.dart';
import 'package:vp_trading/model/enum/activation_conditions_type.dart';
import 'package:vp_trading/utils/condition_command_util.dart';

part 'derivative_validate_order_state.dart';

enum ErrorPrice { overRange, underRange, none, orderEmpty, empty, init }

enum ErrorVolume {
  invalid,
  invalidMax,
  invalid500,
  none,
  orderEmpty,
  empty,
  init,
  exceedMaxEditVolume,
  belowMatchedVolume,
}

extension ErrorPriceExtension on ErrorPrice {
  bool get isError {
    return this != ErrorPrice.none &&
        this != ErrorPrice.empty &&
        this != ErrorPrice.init;
  }

  bool get isErrorEditOrder {
    return this != ErrorPrice.none && this != ErrorPrice.init;
  }

  String get message {
    switch (this) {
      case ErrorPrice.overRange:
        return 'Vượt mức giá trần';
      case ErrorPrice.underRange:
        return 'Dưới mức giá sàn';
      case ErrorPrice.orderEmpty:
        return 'Vui lòng nhập giá';
      case ErrorPrice.empty:
      case ErrorPrice.none:
      case ErrorPrice.init:
        return '';
    }
  }
}

extension ErrorVolumeExtension on ErrorVolume {
  bool get isError {
    return this != ErrorVolume.none &&
        this != ErrorVolume.empty &&
        this != ErrorVolume.init;
  }

  bool get isEditOrderError {
    return this != ErrorVolume.none && this != ErrorVolume.init;
  }

  String message(String? value) {
    switch (this) {
      case ErrorVolume.invalid:
        return 'Khối lượng không hợp lệ';
      case ErrorVolume.orderEmpty:
        return 'Vui lòng nhập khối lượng';
      case ErrorVolume.invalidMax:
        return 'Vượt khối lượng tối đa là $value';
      case ErrorVolume.invalid500:
        return 'KL lệnh không vượt quá 500';
      case ErrorVolume.exceedMaxEditVolume:
        return 'Khối lượng sửa vượt quá sức mua';
      case ErrorVolume.belowMatchedVolume:
        return 'Khối lượng sửa phải lớn hơn khối lượng đã khớp';
      case ErrorVolume.empty:
      case ErrorVolume.none:
      case ErrorVolume.init:
        return '';
    }
  }
}

class DerivativeValidateOrderCubit extends Cubit<DerivativeValidateOrderState>
    with DerivativeUpdateUserConfigMixin {
  DerivativeValidateOrderCubit() : super(const DerivativeValidateOrderState());
  StockInfoModel? _stockInfo;
  OrderType _orderType = OrderType.lo;
  OrderAction _actionType = OrderAction.buy;

  bool get isLoOrGtcOrBuyIn =>
      _orderType.isLoOrGtc || _orderType == OrderType.buyIn;
  AvailableTradeModel? _availableTrade;

  int? _originalVolume;
  int? _matchedVolume;
  bool _isEditMode = false;

  void updateParam({
    StockInfoModel? stockInfo,
    AvailableTradeModel? availableTrade,
    OrderAction? action,
    OrderType? orderType,
  }) {
    if (stockInfo != null) {
      _stockInfo = stockInfo;
    }
    if (availableTrade != null) {
      _availableTrade = availableTrade;
    }
    if (action != null) {
      _actionType = action;
    }
    if (orderType != null) {
      _orderType = orderType;
    }
  }

  void validateError({ErrorPrice? errorPrice, ErrorVolume? errorVolume}) {
    emit(state.copyWith(errorPrice: errorPrice, errorVolume: errorVolume));
  }

  void clear() {
    emit(const DerivativeValidateOrderState());
    clearData();
    clearEditMode();
  }

  void focusField(FocusKeyboard type) {
    emit(state.copyWith(focusKeyboard: type));
  }

  void calculateValue(String value) {
    emit(state.copyWith(calculateValue: value));
  }

  void setSessionType(SessionType sessionType) {
    emit(state.copyWith(sessionType: sessionType));
    final error = validatePrice(text: sessionType.name);
    emit(state.copyWith(errorPrice: error));
  }

  void setActivationConditions(ActivationConditionsType activationType) {
    emit(state.copyWith(activationType: activationType));
  }

  void clearSession() {
    emit(state.clearSession());
  }

  void clearData() {
    emit(state.clearData());
    onChangePrice('');
    onChangeVolume('');
    onChangeActivePrice('');
  }

  void setEditOrderInfo({
    required int originalVolume,
    required int matchedVolume,
  }) {
    _originalVolume = originalVolume;
    _matchedVolume = matchedVolume;
    _isEditMode = true;
  }

  void clearEditMode() {
    _isEditMode = false;
    _originalVolume = null;
    _matchedVolume = null;
  }

  double _valuePrice(String text) {
    return text.priceDerivative ?? 0.0;
  }

  double _quotePrice(String text) {
    return _valuePrice(text);
  }

  double _stepPrice(String text) {
    return _stockInfo?.stockType == StockType.FUGB
        ? 1.0
        : _stockInfo?.stockType == StockType.FUVN30 ||
            _stockInfo?.stockType == StockType.FUVN100
        ? (DerivativeAppConfigs().userConfig.priceJump ?? 0.1).toDouble()
        : 0.1;
  }

  priceTap({
    required String text,
    bool increase = true,
    bool activation = false,
  }) {
    if (text.isEmpty) {
      if (activation) {
        onChangeActivePrice(
          (_stockInfo?.closePrice ?? _stockInfo?.refPrice ?? 0)
              .toFormatThousandSeparator(),
        );
      } else {
        onChangePrice(
          (_stockInfo?.closePrice ?? _stockInfo?.refPrice ?? 0)
              .toFormatThousandSeparator(),
        );
      }
      return;
    }
    if (text.priceDerivative == 0.0 && !increase) {
      if (activation) {
        onChangeActivePrice(0.0.toFormatThousandSeparator());
      } else {
        onChangePrice(0.0.toFormatThousandSeparator());
      }
      return;
    }
    final newText =
        ConditionCommandUtil.updateValueDerivative(
          increase,
          text.priceDerivative ?? 0.0,
          _stepPrice(text),
        ).toDouble().toFormatThousandSeparator();
    final finalText = newText.length > 8 ? text : newText;
    if (activation) {
      onChangeActivePrice(finalText);
    } else {
      onChangePrice(finalText);
    }
  }

  onChangePrice(String value, {bool isOrder = false}) {
    final error = validatePrice(text: value, isOrder: isOrder);
    String valueDisplay() {
      return (_quotePrice(value) * (state.currentVolume?.volume ?? 0))
          .valueText;
    }

    var currentPrice =
        value.isEmpty
            ? value
            : num.parse(
              value.priceDerivative.toString(),
            ).toFormatThousandSeparator();
    emit(
      state.copyWith(
        errorPrice: error,
        calculateValue: valueDisplay(),
        currentPrice: currentPrice,
      ),
    );
  }

  ErrorPrice validatePrice({required String text, bool isOrder = false}) {
    if (state.sessionType != null) {
      return ErrorPrice.none;
    }

    if (_stockInfo == null) return ErrorPrice.none;

    final floor = _stockInfo!.floor ?? 0.0;
    final ceiling = _stockInfo!.ceiling ?? 0.0;

    if (isOrder && text.isEmpty) {
      return ErrorPrice.orderEmpty;
    }

    if (text.isEmpty) {
      return ErrorPrice.empty;
    }
    if (_orderType.isLo && _valuePrice(text) > ceiling) {
      return ErrorPrice.overRange;
    }

    if (_orderType.isLo && _valuePrice(text) < floor) {
      return ErrorPrice.underRange;
    }
    return ErrorPrice.none;
  }

  num getMaxVolumeByAvailableTradeResponse(AvailableTradeModel availableTrade) {
    var maxValue =
        (_actionType == OrderAction.buy
            ? availableTrade.maxBuyQty
            : availableTrade.maxSellQty) ??
        0.0;
    return maxValue;
  }

  num maxVolume() {
    if (_availableTrade == null) return 0.0;
    final maxVolume = getMaxVolumeByAvailableTradeResponse(_availableTrade!);
    return maxVolume > 0.0 ? maxVolume : 0.0;
  }

  ErrorVolume _validateVolumeForEdit({required String text}) {
    if (text.isEmpty) {
      return ErrorVolume.empty;
    }

    final valueVolume = text.volume;

    if (valueVolume > 500.0) {
      return ErrorVolume.invalid500;
    }

    if (valueVolume == 0) return ErrorVolume.invalid;

    if (_originalVolume != null && _matchedVolume != null) {
      final newVolume = valueVolume.toInt();
      final originalVolume = _originalVolume!;
      final matchedVolume = _matchedVolume!;

      if (newVolume > originalVolume) {
        final volumeDiff = newVolume - originalVolume;
        final maxAvailable =
            _actionType == OrderAction.buy
                ? (_availableTrade?.maxBuyQty ?? 0.0)
                : (_availableTrade?.maxSellQty ?? 0.0);

        if (volumeDiff > maxAvailable) {
          return ErrorVolume.exceedMaxEditVolume;
        }
      } else if (newVolume < originalVolume) {
        if (newVolume <= matchedVolume) {
          return ErrorVolume.belowMatchedVolume;
        }
      }
    }

    return ErrorVolume.none;
  }

  ErrorVolume validateVolume({required String text, bool isOrder = false}) {
    if (_isEditMode) {
      return _validateVolumeForEdit(text: text);
    }

    if (isOrder && text.isEmpty) return ErrorVolume.orderEmpty;

    if (text.isEmpty) return ErrorVolume.empty;

    final valueVolume = text.volume;

    if (valueVolume > 500.0) {
      return ErrorVolume.invalid500;
    }

    if (valueVolume > maxVolume()) {
      return ErrorVolume.invalidMax;
    }

    if (valueVolume == 0) return ErrorVolume.invalid;

    return ErrorVolume.none;
  }

  onChangeVolume(String value, {bool isOrder = false}) {
    final error = validateVolume(text: value, isOrder: isOrder);
    String valueDisplay() {
      return (_quotePrice(state.currentPrice ?? "") * (value.volume)).valueText;
    }

    emit(
      state.copyWith(
        calculateValue: valueDisplay(),
        currentVolume: value,
        errorVolume: error,
      ),
    );
  }

  onValidateVolume(String value) {
    final error = validateVolume(text: value);
    emit(state.copyWith(errorVolume: error));
  }

  volumeTap({required String text, bool increase = true}) {
    String onTap({required String text, bool increase = true}) {
      const step = 1.0;
      final newText =
          ConditionCommandUtil.updateValue(
            increase,
            text.volume,
            step,
          ).volumeString;
      if (newText.length > 12) return text;
      return newText;
    }

    final newText = onTap(text: text, increase: increase);
    onChangeVolume(newText);
  }

  onChangeActivePrice(String value, {bool isOrder = false}) {
    final error = validatePrice(text: value, isOrder: isOrder);

    emit(state.copyWith(errorActivePrice: error, currentActivePrice: value));
  }
}
