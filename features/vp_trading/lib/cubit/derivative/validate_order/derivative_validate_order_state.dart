part of 'derivative_validate_order_cubit.dart';

class DerivativeValidateOrderState extends Equatable {
  final SessionType? sessionType;
  final ErrorPrice errorPrice;
  final ErrorVolume errorVolume;
  final ErrorPrice errorActivePrice;
  final FocusKeyboard focusKeyboard;
  final String calculateValue;
  final String? currentPrice;
  final String? currentVolume;
  final String? currentActivePrice;
  final ActivationConditionsType activationType;

  const DerivativeValidateOrderState({
    this.sessionType,
    this.errorPrice = ErrorPrice.init,
    this.errorVolume = ErrorVolume.init,
    this.errorActivePrice = ErrorPrice.init,
    this.focusKeyboard = FocusKeyboard.none,
    this.activationType = ActivationConditionsType.lessThan,
    this.calculateValue = '',
    this.currentPrice,
    this.currentVolume,
    this.currentActivePrice,
  });

  @override
  List<Object?> get props => [
    sessionType,
    errorPrice,
    errorActivePrice,
    errorVolume,
    focusKeyboard,
    calculateValue,
    currentPrice,
    currentVolume,
    currentActivePrice,
    activationType,
  ];

  DerivativeValidateOrderState copyWith({
    SessionType? sessionType,
    ErrorPrice? errorPrice,
    ErrorPrice? errorActivePrice,
    ErrorVolume? errorVolume,
    FocusKeyboard? focusKeyboard,
    String? calculateValue,
    String? currentPrice,
    String? currentVolume,
    String? currentActivePrice,
    ActivationConditionsType? activationType,
  }) {
    return DerivativeValidateOrderState(
      sessionType: sessionType ?? this.sessionType,
      errorPrice: errorPrice ?? this.errorPrice,
      errorActivePrice: errorActivePrice ?? this.errorActivePrice,
      errorVolume: errorVolume ?? this.errorVolume,
      focusKeyboard: focusKeyboard ?? this.focusKeyboard,
      calculateValue: calculateValue ?? this.calculateValue,
      currentPrice: currentPrice ?? this.currentPrice,
      currentVolume: currentVolume ?? this.currentVolume,
      activationType: activationType ?? this.activationType,
      currentActivePrice: currentActivePrice ?? this.currentActivePrice,
    );
  }

  DerivativeValidateOrderState clearSession() {
    return DerivativeValidateOrderState(
      sessionType: null,
      errorPrice: errorPrice,
      errorActivePrice: errorActivePrice,
      errorVolume: errorVolume,
      focusKeyboard: focusKeyboard,
      calculateValue: calculateValue,
      currentPrice: currentPrice,
      currentVolume: currentVolume,
      activationType: activationType,
      currentActivePrice: currentActivePrice,
    );
  }

  DerivativeValidateOrderState clearData() {
    return const DerivativeValidateOrderState(
      sessionType: null,
      errorPrice: ErrorPrice.init,
      errorActivePrice: ErrorPrice.init,
      errorVolume: ErrorVolume.init,
      focusKeyboard: FocusKeyboard.none,
      activationType: ActivationConditionsType.lessThan,
      calculateValue: '',
      currentPrice: null,
      currentActivePrice: null,
      currentVolume: null,
    );
  }
}

extension ValidateOrderStateExtension on DerivativeValidateOrderState {
  bool get isErrorPrice => errorPrice != ErrorPrice.none && sessionType == null;

  bool get isErrorVolume => errorVolume != ErrorVolume.none;

  bool get isErrorActivePrice => errorActivePrice != ErrorPrice.none;

  bool get isValid {
    final isValidVolume =
        errorVolume == ErrorVolume.none && currentVolume?.isNotEmpty == true;
    if (sessionType != null) {
      return isValidVolume;
    }
    return isValidVolume &&
        errorPrice == ErrorPrice.none &&
        currentPrice?.isNotEmpty == true;
  }

  // Thêm getter riêng cho validation sửa lệnh
  bool get isValidForEdit {
    final isValidVolume =
        errorVolume == ErrorVolume.none &&
        currentVolume?.isNotEmpty == true &&
        errorVolume != ErrorVolume.exceedMaxEditVolume &&
        errorVolume != ErrorVolume.belowMatchedVolume;

    if (sessionType != null) {
      return isValidVolume;
    }

    return isValidVolume &&
        errorPrice == ErrorPrice.none &&
        currentPrice?.isNotEmpty == true;
  }
}
