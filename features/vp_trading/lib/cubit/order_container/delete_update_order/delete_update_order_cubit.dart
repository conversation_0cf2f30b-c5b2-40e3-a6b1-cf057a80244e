import 'package:vp_common/error/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/repository/command_history_repository.dart';
import 'package:vp_trading/model/request/order/delete_order_request.dart';

import 'delete_update_order_state.dart';

class DeleteUpdateOrderCubit extends Cubit<DeleteUpdateOrderState> {
  final CommandHistoryRepository commandHistoryRepository =
      GetIt.instance<CommandHistoryRepository>();

  DeleteUpdateOrderCubit() : super(const DeleteUpdateOrderState());

  void resetState() {
    emit(const DeleteUpdateOrderState());
  }

  Future<void> deleteOrder(DeleteOrderRequest request) async {
    try {
      emit(state.copyWith(status: DeleteUpdateOrderStateEnum.loading));

      final BaseResponse response = await commandHistoryRepository.deleteOrder(
        request,
      );

      if (response.isSuccess) {
        emit(
          state.copyWith(status: DeleteUpdateOrderStateEnum.isDeleteSuccess),
        );
      } else {
        emit(
          state.copyWith(
            status: DeleteUpdateOrderStateEnum.failure,
            errorMessage: response.message,
          ),
        );
      }
    } catch (error) {
      emit(
        state.copyWith(
          status: DeleteUpdateOrderStateEnum.failure,
          errorMessage: await getErrorMessage(error),
        ),
      );
    }
  }

  // Huỷ tất cả lệnh thường
  Future<void> deleteAllOrder(
    DeleteOrderRequest request, {
    bool isConditional = false,
  }) async {
    try {
      emit(state.copyWith(status: DeleteUpdateOrderStateEnum.loading));

      final result =
          isConditional
              ? await commandHistoryRepository.deleteConditionOrder(request)
              : await commandHistoryRepository.deleteBatchOrder(request);

      if (result.isSuccess) {
        emit(
          state.copyWith(status: DeleteUpdateOrderStateEnum.isDeleteAllSuccess),
        );
      } else {
        emit(
          state.copyWith(
            status: DeleteUpdateOrderStateEnum.failure,
            errorMessage: result.message,
          ),
        );
      }
    } catch (error) {
      emit(
        state.copyWith(
          status: DeleteUpdateOrderStateEnum.failure,
          errorMessage: await getErrorMessage(error),
        ),
      );
    }
  }

  // Huỷ tất cả lệnh điều kiện phái sinh
  Future<void> deleteAllFUConditionOrder(DeleteFuConditionOrderRequest request) async {
    try {
      emit(state.copyWith(status: DeleteUpdateOrderStateEnum.loading));

      final result = await commandHistoryRepository.deleteAllFuConditionOrder(
        request,
      );
      if (result.isSuccess) {
        emit(
          state.copyWith(status: DeleteUpdateOrderStateEnum.isDeleteAllSuccess),
        );
      } else {
        emit(
          state.copyWith(
            status: DeleteUpdateOrderStateEnum.failure,
            errorMessage: result.message,
          ),
        );
      }
    } catch (error) {
      emit(
        state.copyWith(
          status: DeleteUpdateOrderStateEnum.failure,
          errorMessage: await getErrorMessage(error),
        ),
      );
    }
  }

  void deleteFUConditionOrder(DeleteFuConditionOrderRequest request) async {
    try {
      emit(state.copyWith(status: DeleteUpdateOrderStateEnum.loading));
      final result = await commandHistoryRepository.deleteFuConditionOrder(
        request,
      );
      if (result.isSuccess) {
        emit(
          state.copyWith(status: DeleteUpdateOrderStateEnum.isDeleteSuccess),
        );
      } else {
        emit(
          state.copyWith(
            status: DeleteUpdateOrderStateEnum.failure,
            errorMessage: result.message,
          ),
        );
      }
    } catch (error) {
      emit(
        state.copyWith(
          status: DeleteUpdateOrderStateEnum.failure,
          errorMessage: await getErrorMessage(error),
        ),
      );
    }
  }

  void resetErrorMessage() {
    emit(state.copyWith(errorMessage: null));
  }

  void resetIsDeleteSuccess() {
    emit(state.copyWith(status: DeleteUpdateOrderStateEnum.initial));
  }

  void resetIsUpdateSuccess() {
    emit(state.copyWith(status: DeleteUpdateOrderStateEnum.initial));
  }
}
