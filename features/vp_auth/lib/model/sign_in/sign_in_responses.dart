class SignInResponses {
  String? accessToken;
  String? refreshToken;
  int? expiresIn;
  String? tokenType;
  String? scope;
  bool? isFirstLogin;
  bool? requireCaptcha;

  SignInResponses(
      {this.accessToken,
      this.refreshToken,
      this.expiresIn,
      this.tokenType,
      this.scope,
      this.isFirstLogin,
      this.requireCaptcha});

  SignInResponses.fromJson(Map<String, dynamic> json) {
    accessToken = json['access_token'];
    refreshToken = json['refresh_token'];
    expiresIn = json['expires_in'];
    tokenType = json['token_type'];
    scope = json['scope'];
    isFirstLogin = json['is_first_login'];
    requireCaptcha = json['requireCaptcha'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['access_token'] = accessToken;
    data['refresh_token'] = refreshToken;
    data['expires_in'] = expiresIn;
    data['token_type'] = tokenType;
    data['is_first_login'] = isFirstLogin;
    data['scope'] = scope;
    data['requireCaptcha'] = requireCaptcha;
    return data;
  }
}
