import 'dart:io';

import 'package:json_annotation/json_annotation.dart';

part 'signin_request_model.g.dart';

@JsonSerializable()
class SignInRequestModel {
  String username;
  String password;

  String? recaptchaToken;
  String? recaptchaAction;

  SignInRequestModel({
    required this.username,
    required this.password,
    this.recaptchaToken,
    this.recaptchaAction,
  });

  factory SignInRequestModel.fromJson(Map<String, dynamic> json) =>
      _$SignInRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$SignInRequestModelToJson(this);

  Map<String, dynamic> toJsonRecaptcha() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['username'] = username;
    data['password'] = password;
    data['recaptchaToken'] = recaptchaToken;
    data['recaptchaAction'] = recaptchaAction;
    data['platform'] = Platform.isAndroid ? 'android' : 'ios';
    return data;
  }
}
