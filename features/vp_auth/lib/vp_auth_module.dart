import 'package:flutter/material.dart';
import 'package:multiple_localization/multiple_localization.dart';
import 'package:vp_auth/core/constant/account_path_api.dart';
import 'package:vp_auth/core/repository/account_register_repository.dart';
import 'package:vp_auth/core/repository/account_register_repository_impl.dart';
import 'package:vp_auth/core/repository/account_repository.dart';
import 'package:vp_auth/core/repository/forgot_password_repository.dart';
import 'package:vp_auth/core/repository/forgot_pin_repository.dart';
import 'package:vp_auth/core/repository/onboarding_repository.dart';
import 'package:vp_auth/core/repository/onboarding_repositoty_impl.dart';
import 'package:vp_auth/core/repository/sign_up_repository.dart';
import 'package:vp_auth/core/service/account_service.dart';
import 'package:vp_auth/core/service/sign_up_service.dart';
import 'package:vp_auth/generated/l10n.dart';
import 'package:vp_auth/screen/change_pass_and_pin_for_first/change_pass_and_pin_for_first_page.dart';
import 'package:vp_auth/screen/change_pass_and_pin_for_first/change_pass_for_first/change_pass_for_first_page.dart';
import 'package:vp_auth/screen/change_pass_and_pin_for_first/change_pin_for_first/change_pin_for_first_page.dart';
import 'package:vp_auth/screen/forgot_password/forgot_password_auth/forgot_password_auth_page.dart';
import 'package:vp_auth/screen/forgot_password/forgot_password_change/forgot_password_change_page.dart';
import 'package:vp_auth/screen/forgot_password/forgot_password_page.dart';
import 'package:vp_auth/screen/forgot_password/forgot_password_verify/forgot_password_verify_page.dart';
import 'package:vp_auth/screen/forgot_pin/forgot_pin_page.dart';
import 'package:vp_auth/screen/info_ocr/info_ocr_page.dart';

//import 'package:vp_auth/screen/forgot_password/forgot_password_main/forgot_password_main_page.dart';
import 'package:vp_auth/screen/sign_in/sign_in_page.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/view/sign_up_page.dart';
import 'package:vp_auth/screen/splash_default/splash_default_screen.dart';
import 'package:vp_auth/screen/upload_ocr/upload_ocr_for_identification_page.dart';
import 'package:vp_auth/screen/upload_ocr/upload_ocr_page.dart';
import 'package:vp_auth/screen/user_info_iam/user_info_iam_screen.dart';
import 'package:vp_core/vp_core.dart';

import 'generated/intl/messages_all.dart';
import 'router/account_router.dart';

class AuthModule implements Module {
  @override
  void injectServices(GetIt service) {
    service.registerLazySingleton(() => AccountService(service()));
    service.registerLazySingleton<AccountRepository>(
        () => AccountRepositoryImpl(accountService: service()));
    service.registerLazySingleton<ForgotPasswordRepository>(
        () => ForgotPasswordRepositoryImpl(accountService: service()));
    service.registerLazySingleton<ForgotPinRepository>(
        () => ForgotPinRepositoryImpl(accountService: service()));

    service.registerLazySingleton(() => SignUpService(service()));
    service.registerLazySingleton<SignUpRepository>(
        () => SignUpRepositoryImpl(signUpService: service()));
    service.registerLazySingleton<AccountRegisterRepository>(
        () => AccountRegisterRepositoryImpl(restClient: service()));
    service.registerLazySingleton<OnboardingRepository>(
        () => OnboardingRepositoryIpml(restClient: service()));
  }

  @override
  List<RouteBase> router() {
    return [
      GoRoute(
        name: AccountRouter.signIn.routeName,
        path: AccountRouter.signIn.routeName,
        pageBuilder: (context, state) {
          // final args = state.extra as SignInArgs?;
          final returnTo = state.uri.queryParameters['returnTo'];

          return MaterialPage(
            key: state.pageKey,
            fullscreenDialog: true,
            child: SignInPage(returnTo: returnTo),
          );
        },
      ),
      GoRoute(
          path: AccountRouter.splashDefault.routeName,
          builder: (context, state) => const SplashDefaultScreen()),
      ShellRoute(
          builder: (context, state, child) => ForgotPasswordPage(child: child),
          routes: [
            GoRoute(
                path: AccountRouter.forgotPasswordVerifyPage.routeName,
                builder: (context, state) => ForgotPasswordVerifyPage()),
            GoRoute(
                path: AccountRouter.forgotPasswordAuthPage.routeName,
                builder: (context, state) => ForgotPasswordAuthPage()),
            GoRoute(
                path: AccountRouter.forgotPasswordChangePage.routeName,
                builder: (context, state) => ForgotPasswordChangePage()),
          ]),
      GoRoute(
          path: AccountRouter.forgotPin.routeName,
          builder: (context, state) => ForgotPinPage()),
      GoRoute(
          path: AccountRouter.regsiter.routeName,
          builder: (context, state) => SignUpPage(
                navigateFromDeeplink: (state.extra as bool?) ?? false,
              )),
      ShellRoute(
          builder: (context, state, child) => ChangePassAndPinForFirstPage(
                goRouterState: state,
                child: child,
              ),
          routes: [
            GoRoute(
                path: AccountRouter.changePassForFirst.routeName,
                builder: (context, state) => ChangePassForFirstPage(
                    updatePin: state.extra as bool? ?? false)),
            GoRoute(
                path: AccountRouter.changePinForFirst.routeName,
                builder: (context, state) =>
                    ChangePinForFirst(isStep2: state.extra as bool? ?? false)),
          ]),
      // ShellRoute(
      //   builder: (context, state, child) => SignUpPage(child: child),
      //   routes: [
      //     GoRoute(
      //         path: AccountRouter.regsiter.routeName,
      //         builder: (context, state) => const RegisterReferencePage()),
      //     GoRoute(
      //         path: SignUpTypeScreen.phone.routerName,
      //         name: SignUpTypeScreen.phone.name,
      //         builder: (context, state) => const SignUpPhonePage()),
      //     GoRoute(
      //         path: SignUpTypeScreen.otp.routerName,
      //         builder: (context, state) => const SignUpOtpPage()),
      //     GoRoute(
      //         path: SignUpTypeScreen.email.routerName,
      //         name: SignUpTypeScreen.email.name,
      //         builder: (context, state) => const SignUpEmailPage()),
      //     GoRoute(
      //         path: SignUpTypeScreen.signature.routerName,
      //         name: SignUpTypeScreen.signature.name,
      //         builder: (context, state) => const SignUpSignaturePage()),
      //     GoRoute(
      //         path: SignUpTypeScreen.referrer.routerName,
      //         name: SignUpTypeScreen.referrer.name,
      //         builder: (context, state) => const RegisterReferencePage()),
      //   ],
      // ),
      GoRoute(
        path: AccountRouter.overlayUserIamInfo.routeName,
        pageBuilder: (context, state) {
          return CustomTransitionPage(
            opaque: false,
            barrierDismissible: true,
            barrierColor: Colors.transparent,
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
              return FadeTransition(opacity: animation, child: child);
            },
            child: UserInfoIamScreen(),
          );
        },
      ),
      GoRoute(
          path: AccountRouter.infoOCRPage.routeName,
          builder: (context, state) => InfoOcrPage()),
      GoRoute(
          path: AccountRouter.uploadOcrPage.routeName,
          builder: (context, state) => UploadOCRPage()),
      GoRoute(
          path: AccountRouter.uploadOcrForIdentificationPage.routeName,
          builder: (context, state) => UploadOcrForIdentificationPage(
                type: state.extra as NavigateNFCType,
              )),
    ];
  }

  @override
  List<LocalizationsDelegate> localizationsDelegates() {
    return [MultiLocalizationsDelegate.delegate];
  }

  @override
  String modulePath() {
    return vpAuth;
  }
}

class MultiLocalizationsDelegate extends AppLocalizationDelegate {
  const MultiLocalizationsDelegate();

  static const AppLocalizationDelegate delegate = MultiLocalizationsDelegate();

  @override
  Future<S> load(Locale locale) {
    return MultipleLocalizations.load(
        initializeMessages, locale, (l) => S.load(locale),
        setDefaultLocale: true);
  }
}
