import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vp_auth/core/constant/account_path_api.dart';
import 'package:vp_auth/gen/assets.gen.dart';
import 'package:vp_auth/generated/l10n.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:vp_auth/vp_auth.dart';
import 'package:vp_common/generated/l10n.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class SplashDefaultScreen extends StatefulWidget {
  const SplashDefaultScreen({super.key});

  @override
  State<SplashDefaultScreen> createState() => _SplashDefaultScreenState();
}

class _SplashDefaultScreenState extends State<SplashDefaultScreen> {
  int currentPage = 0;

  final controllerUI = StreamController();
  late Timer periodicTimer;

  final PageController _controller = PageController(
    initialPage: 0,
  );

  @override
  void dispose() {
    _controller.dispose();
    controllerUI.close();
    periodicTimer.cancel();
    super.dispose();
  }

  List<String> listImage = [
    Assets.images.splash1.path,
    Assets.images.splash2.path,
  ];

  @override
  void initState() {
    periodicTimer = Timer.periodic(
      const Duration(seconds: 5),
      (_) {
        if (currentPage == listImage.length - 1) {
          currentPage = 0;
        } else {
          currentPage++;
        }
        controllerUI.sink.add(true);
        _controller.animateToPage(currentPage,
            duration: const Duration(milliseconds: 300), curve: Curves.linear);
      },
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
          child: Stack(children: [
        Column(
          children: [
            buildPageView(),
            buildListDotView(),
            buildButton(context),
            const SizedBox(
              height: 16,
            )
          ],
        ),
        Align(
          alignment: Alignment.topRight,
          child: IconButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              icon: SvgPicture.asset(
                Assets.icons.icClose,
                package: vpAuth,
                colorFilter: ColorFilter.mode(
                  themeData.colorIcon,
                  BlendMode.srcIn,
                ),
              )),
        ),
      ])),
    );
  }

  // Build
  Padding buildButton(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: VpsButton.primarySmall(
          title: VPCommonLocalize.current.next,
          width: double.infinity,
          onPressed: () {
            context.pushReplacement(AccountRouter.regsiter.routeName);
          },
        ));
  }

  // Build List DotView
  Padding buildListDotView() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 24),
      child: StreamBuilder(
          stream: controllerUI.stream,
          builder: (context, snapshot) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                  listImage.length, (index) => buildDot(index: index)),
            );
          }),
    );
  }

  // Build PageView
  Expanded buildPageView() {
    return Expanded(
      child: PageView.builder(
        onPageChanged: (index) {
          currentPage = index;
          controllerUI.sink.add(true);
        },
        controller: _controller,
        itemCount: listImage.length,
        itemBuilder: (context, index) {
          return Center(
              child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(listImage[index],
                    package: vpAuth,
                    fit: BoxFit.contain,
                    width: MediaQuery.of(context).size.width),
                const SizedBox(
                  height: 42,
                ),
                buildTextView(index, context),
              ],
            ),
          ));
        },
      ),
    );
  }

  // Dot View
  AnimatedContainer buildDot({int? index}) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: const EdgeInsets.only(right: 8),
      height: 8,
      width: currentPage == index ? 24 : 8,
      decoration: BoxDecoration(
        color: currentPage == index ? themeData.primary : themeData.gray300,
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }

  // Build TextView
  Widget buildTextView(int index, BuildContext context) {
    if (index == 0) {
      return RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          text: S.current.account_splash1_1,
          style: vpTextStyle.subtitle16.copyColor(themeData.gray900),
          children: [
            TextSpan(
                text: S.current.account_splash1_2,
                style: vpTextStyle.headineBold6
                    ?.copyWith(color: themeData.primary, height: 1.5)),
          ],
        ),
      );
    }

    if (index == 1) {
      return Text(
        S.current.account_splash2,
        style: vpTextStyle.subtitle16.copyColor(themeData.gray900),
        textAlign: TextAlign.center,
      );
    }
    return const SizedBox.shrink();
  }
}
