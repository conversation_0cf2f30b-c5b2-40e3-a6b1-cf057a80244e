import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vp_auth/utils/widgets/button_widget.dart';
import 'package:vp_common/vp_common.dart' hide NoDuplicate;
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class SignUpButton extends StatelessWidget {
  const SignUpButton(
      {Key? key,
      required this.text,
      required this.press,
      this.color = const Color(0xffE5544F),
      this.colorTitle = const Color(0xffffffff),
      this.haveBorder = false,
      this.evelation = 1})
      : super(key: key);
  final String text;
  final Function press;
  final Color? color;
  final Color? colorTitle;
  final bool haveBorder;
  final double? evelation;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        width: double.infinity,
        height: 40,
        child: ElevatedButton(
            style: ElevatedButton.styleFrom(
                elevation: evelation,
                side: BorderSide(
                    width: 1.0,
                    color: haveBorder ? themeData.gray700 : Colors.transparent),
                backgroundColor: color,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10))),
            onPressed: () {
              press();
            },
            child: Text(text,
                style: vpTextStyle.subtitle14?.copyWith(color: colorTitle),
                textAlign: TextAlign.center)));
  }
}

class SignUpButtonNext extends StatelessWidget {
  const SignUpButtonNext({
    Key? key,
    required this.onPress,
    this.enable = false,
    required this.title,
  }) : super(key: key);

  final VoidCallback onPress;
  final bool enable;
  final String title;

  @override
  Widget build(BuildContext context) {
    final colorDisable = themeData.gray500;
    return SizedBox(
      height: 40,
      child: ElevatedButton(
        style: ButtonStyle(
          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          elevation: MaterialStateProperty.all<double>(enable ? 1 : 0),
          backgroundColor: MaterialStateProperty.all<Color>(
            enable
                ? vpColor.backgroundBrandPressed
                : vpColor.backgroundElevationMinus1,
          ),
        ),
        onPressed: () => handleOnPress(),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Row(
            children: [
              Text(
                title,
                style: vpTextStyle.subtitle14?.copyWith(
                    color: enable ? vpColor.textWhite : vpColor.textDisabled),
              ),
              const SizedBox(
                width: 8,
              ),
              SvgPicture.asset(Assets.icons.icArrowNext.path,
                  package: 'vp_common',
                  color: enable ? themeData.black : colorDisable)
            ],
          ),
        ),
      ),
    );
  }

  void handleOnPress() {
    if (!enable) return;

    NoDuplicate(() => onPress(), inMilliseconds: 300);
  }
}
