import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vp_auth/core/constant/account_path_api.dart';
import 'package:vp_auth/gen/assets.gen.dart';
import 'package:vp_auth/generated/l10n.dart';
import 'package:vp_core/vp_core.dart';

class BottomButton extends StatelessWidget {
  final bool showSmartOtp;
  final VoidCallback onTapSmartOtp;
  final VoidCallback onTapSupport;
  final VoidCallback onTapForgotPassword;
  final VoidCallback onGoToMarketPage;

  const BottomButton({
    Key? key,
    required this.showSmartOtp,
    required this.onTapSmartOtp,
    required this.onTapSupport,
    required this.onTapForgotPassword,
    required this.onGoToMarketPage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 48),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Expanded(
                flex: 6,
                child: BottomButtonItem(
                  icon: Assets.icons.icForgotPassword,
                  title: S.current.account_forgot_password,
                  onTap: onTapForgotPassword,
                ),
              ),
              Expanded(
                flex: 5,
                child: BottomButtonItem(
                  icon: Assets.icons.icMarket,
                  title: S.current.account_market,
                  onTap: onGoToMarketPage,
                ),
              ),
              if (showSmartOtp)
                Expanded(
                  flex: 5,
                  child: BottomButtonItem(
                    icon: Assets.icons.icSmartOtp,
                    title: S.current.account_smartOTP,
                    onTap: onTapSmartOtp,
                  ),
                ),
              Expanded(
                flex: 5,
                child: BottomButtonItem(
                  icon: Assets.icons.icSupport,
                  title: S.current.account_support,
                  onTap: onTapSupport,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class BottomButtonItem extends StatelessWidget {
  final VoidCallback onTap;
  final String icon;
  final String title;

  const BottomButtonItem({
    Key? key,
    required this.onTap,
    required this.icon,
    required this.title,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          SvgPicture.asset(
            icon,
            width: 32,
            height: 32,
            package: vpAuth,
            
          ),
          const SizedBox(height: 32),
          Text(
            title,
            style: vpTextStyle.captionMedium.copyColor(vpColor.textWhite),
          ),
        ],
      ),
    );
  }
}
