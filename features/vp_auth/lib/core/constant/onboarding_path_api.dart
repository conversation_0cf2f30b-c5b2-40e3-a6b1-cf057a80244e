class OnboardingPathAPI {
  static const checkPhoneNumber =
      '/iam/no-auth/v2/users/otp-captcha'; // check phone
  static const otpVerification =
      '/iam/no-auth/users/otp-verification'; // Verify OTP
  static const onboardingUsersEkyc = '/iam/onboarding/users/ekyc'; // OCR
  static const onboardingOCRV2 = '/iam/onboarding/v3/users/fw-ocr'; // OCR
  static const onboardingLivenessV2 =
      '/iam/onboarding/v3/users/fw-face-liveness'; // OCR
  static const onboardingUsersEkycFace =
      '/iam/onboarding/users/ekyc/face'; // Xac thuc khuon mat
  static String getOnboardingUserEmail(String email) {
    return '/iam/onboarding/users/email?email=$email';
  }

  static const onboardingUserBank = '/iam/onboarding/users/bank'; // Update bank
  static const onboardingUsersBrokers =
      '/iam/onboarding/users/brokers?'; // Check broker
  static String getOnBoardingUsersPutBroker(
      {String brokerNeeded = '', String brokerNo = ''}) {
    return '/iam/onboarding/users/broker?brokerNeeded=$brokerNeeded&brokerNo=$brokerNo';
  } // Update broker

  static const onboardingUserOCR =
      '/iam/onboarding/users/ocr'; // Update thong tin OCR
  static const onboardingUsersAccNum =
      '/iam/onboarding/users/account-number?'; // API gen so luu ky/ API check so luu ky
  static String getOnboardingUsersAccount(String accountNo) {
    return '/iam/onboarding/users/account?accountNo=$accountNo';
  } // API Update so tieu khoan

  static String getOnboardingUserAccount(String accountNo) {
    return '/iam/onboarding/users/account?accountNo=$accountNo';
  }
  static const onboardingUsersEkycActions =
      '/iam/onboarding/v2/users/ekyc/actions'; // Danh sach hanh dong

  static const readNFCOnboarding = '/iam/onboarding/read-nfc';
  static const saveNFCLogOnboarding = '/iam/onboarding/nfc-log';
  static const verifyNFC = '/iam/onboarding/vpbank/verify-nfc';
  static const faceFont = '/iam/onboarding/users/face-front-image';
  static String confirmOCRUpdate() {
    return '/iam/external/users/ekyc-modification/confirm';
  }
  static const String signatureImage = '/iam/onboarding/v3/users/signature-image';
 static const String getFile = '/file/onboarding/files';

  static const String checkExistAccountBank =
      '/iam/onboarding/vpbank/user/existence';

  static const onboardingUsers = '/iam/onboarding/users'; // Mo tai khoan

  static const String agreeOpenAccountBank = '/iam/onboarding/partner/open-status';

  static const String updateReferenceCode = '/iam/onboarding/users/referrer';

  static const String updateDefaultBroker = '/iam/onboarding/users/broker';

  static const String submitMoneyLaundering = '/iam/onboarding/vpbank/submit-info';

  static const String listQuestionMoney = '/iam/onboarding/vpbank/questions';
  static const String genContractRegister =
      '/iam/onboarding/vpbank-contract/generation';
  static const openAccount = '/iam/onboarding/users';
  static const genOnboardingOTP = '/iam/onboarding/users/finish-otp';
  static const onboardingFiles = '/file/onboarding/files';
  static const onboardingContractGeneration =
      '/iam/onboarding/contract/generation'; // Tao hop dong

}
