import 'package:flutter/foundation.dart';
import 'package:recaptcha_enterprise_flutter/recaptcha_action.dart';
import 'package:recaptcha_enterprise_flutter/recaptcha_enterprise.dart';
import 'package:vp_auth/core/repository/account_repository.dart';
import 'package:vp_auth/cubit/sign_in/user_info_mixin.dart';
import 'package:vp_auth/model/sign_in/signin_request_model.dart';
import 'package:vp_auth/utils/list_account.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/base/local_storage/local_storage.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';

part 'sign_in_state.dart';

class SignInCubit extends Cubit<SignInState> with UserInfoMixin {
  SignInCubit() : super(SignInState()) {
    checkLogged();
  }

  final AccountRepository _accountRepository =
      GetIt.instance<AccountRepository>();
  bool _checkRecaptcha = false;
  Future<int> emitShowSelectAcc() async {
    List<UserInfoModel> listUser = await getListAccount();
    emit(state.copyWith(listUser: listUser));
    return listUser.length;
  }

  void checkLogged() async {
    final listUser = await getListAccount();
    if (listUser.isEmpty) {
      return;
    }
    emit(state.copyWith(listUser: listUser));
    final objFist = listUser.first;
    setUserInfo(objFist);
  }

  void setUserInfo(UserInfoModel? info) {
    if (info == null) {
      emit(SignInState());
    } else {
      emit(state.copyWith(userCurrent: info));
    }

    //  clearData();
  }

  void onSubmitted(String username, String password) async {
    // _loading = true;

    emit(state.copyWith(status: SignInStatus.loading));

    final SignInRequestModel requestLogin;

    if (_checkRecaptcha) {
      var recaptchaToken =
          await RecaptchaEnterprise.execute(RecaptchaAction.LOGIN());

      requestLogin = SignInRequestModel(
          username: username,
          password: password,
          recaptchaToken: recaptchaToken,
          recaptchaAction: 'login');
    } else {
      requestLogin = SignInRequestModel(
        username: username,
        password: password,
      );
    }

    try {
      final responseLogin = await _accountRepository.login(requestLogin);
      if (responseLogin.data?.accessToken != null) {
        SharedPref.setString(
            KeyShared.accessToken, responseLogin.data!.accessToken!);
        SharedPref.setString(
            KeyShared.tokenType, responseLogin.data!.tokenType!);

        await getUserInfo(
          onSuccess: (info) async {
            await LocalStorage.instance.write<String>(
              key: username,
              data: password,
            );

            emit(state.copyWith(
                status: SignInStatus.success, userCurrent: info));

            //  _loading = false;

            //  emit(SignInSuccess(info, password));

            //   SharedPref.setString("${info.userinfo!.username}", password);
          },
          onUpdatePinForFirst: (info, updatePassword, updatePin) {
            emit(state.copyWith(
              status: SignInStatus.changePassForFirst,
              updatePassword: updatePassword,
              updatePin: updatePin,
            ));

            // send(Message(
            //   type: SignInStatus.changePassForFirst,
            //   content: (updatePassword, updatePin),
            // ));
            //    _loading = false;

            // emit(
            //   ChangeInfoForFirstLogin(
            //     updatePassword: updatePassword,
            //     updatePin: updatePin,
            //   ),
            // );
          },
          onUpdateEkyc: (verificationInfoDto) {
            emit(state.copyWith(status: SignInStatus.verificationInfo));
            // emit(UpdateEkyc(
            //     needChangeIdType: verificationInfoDto.needChangeIdType ?? false));
          },
          onFail: (e) => throw e,
        );
      } else if (responseLogin.data?.requireCaptcha == true) {
        _checkRecaptcha = true;
        emit(state.copyWith(
            status: SignInStatus.wrongPass, message: responseLogin.message));
      } else {
        if (responseLogin.code == "IABERR202") {
          emit(state.copyWith(
              status: SignInStatus.wrongPass, message: responseLogin.message));
        } else {
          emit(state.copyWith(
              status: SignInStatus.failure, message: responseLogin.message));
        }
      }
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      emit(state.copyWith(status: SignInStatus.failure, message: e.toString()));
      showError(e);
    }
  }

  bool get getStatusBioemetric {
    final accNo = state.userCurrent?.userinfo?.username;
    if (accNo == null) {
      return false;
    }
    final key = '${KeyShared.biometrics}_${accNo}';

    final bool isSaveBiometrics = SharedPref.getBool(key, defValue: false);
    return isSaveBiometrics;
  }
}
