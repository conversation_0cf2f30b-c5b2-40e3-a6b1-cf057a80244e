part of 'sign_in_validator_cubit.dart';

class SignInValidatorState extends Equatable {
  final String? accountErrorLabel;
  final String? passwordErrorLabel;
  final InputType accountInputType;
  final InputType passwordInputType;
  final int maxLengthAccount;

  SignInValidatorState({
    this.accountErrorLabel,
    this.passwordErrorLabel,
    this.accountInputType = InputType.rest,
    this.passwordInputType = InputType.rest,
    this.maxLengthAccount = 12,
  });

  SignInValidatorState copyWith({
    String? accountErrorLabel,
    String? passwordErrorLabel,
    InputType? accountInputType,
    InputType? passwordInputType,
    int? maxLengthAccount,
  }) =>
      SignInValidatorState(
        accountErrorLabel: accountErrorLabel ?? this.accountErrorLabel,
        passwordErrorLabel: passwordErrorLabel ?? this.passwordErrorLabel,
        accountInputType: accountInputType ?? this.accountInputType,
        passwordInputType: passwordInputType ?? this.passwordInputType,
        maxLengthAccount: maxLengthAccount ?? this.maxLengthAccount,
      );

  bool get done =>
      accountInputType == InputType.rest && passwordInputType == InputType.rest;

  @override
  List<Object?> get props => [
        accountErrorLabel,
        passwordErrorLabel,
        accountInputType,
        passwordInputType,
        maxLengthAccount,
      ];
}
