import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_auth/generated/l10n.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/model/sign_in_model/user_info_model.dart';
import 'package:vp_design_system/vp_design_system.dart';

part 'sign_in_validator_state.dart';

class SignInValidatorCubit extends Cubit<SignInValidatorState> {
  SignInValidatorCubit() : super(SignInValidatorState());

  void accountValidator(String text) {
    final validatorText = switch (text) {
      '' => S.current.account_enter_username,
      _
          when (!AppValidator.validPhone(text) &&
              !AppValidator.validAccount(text)) =>
        S.current.account_invalid_username,
      _ => '',
    };
    emit(state.copyWith(
      accountInputType:
          validatorText.isEmpty ? InputType.rest : InputType.error,
      accountErrorLabel: validatorText,
    ));
  }

  void passwordValidator(String text) {
    final validatorText = switch (text) {
      '' => S.current.account_enter_password,
      _ => '',
    };
    emit(state.copyWith(
      passwordInputType:
          validatorText.isEmpty ? InputType.rest : InputType.error,
      passwordErrorLabel: validatorText,
    ));
  }

  bool disabledButton(UserInfoModel? userInfo) {
    if (userInfo != null) {
      return (state.passwordErrorLabel ?? '').isNotEmpty;
    } else {
      return (state.accountErrorLabel ?? '').isNotEmpty ||
          (state.passwordErrorLabel ?? '').isNotEmpty;
    }
  }

  void getMaxLength(String val) {
    if (val.isNotEmpty && val.indexOf("+84") == 0) {
      emit(state.copyWith(maxLengthAccount: 12));
    } else {
      emit(state.copyWith(maxLengthAccount: 10));
    }
  }

  int get maxLength => state.maxLengthAccount;
}
