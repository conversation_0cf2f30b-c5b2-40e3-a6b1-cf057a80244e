import 'dart:ui';

import 'package:vp_common/extensions/date_extensions.dart';
import 'package:vp_common/extensions/list_extensions.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_market/screen/chart_market/chart_market_state.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:vp_market/screen/widget/chart/chart_market_adapter.dart';
import 'dart:async';

class ChartMarketBloc extends Cubit<ChartMarketState> {
  ChartMarketBloc(this.trackballBehavior) : super(ChartMarketInitState()) {
    onTick();
  }

  final TrackballBehavior trackballBehavior;

  bool trackballChanged = true;

  ChartMarketAdapter? adapter;

  ChartSeriesController? controller;

  DateTime get currentTime => DateTime.now();

  Timer? _timer;

  void _onTick() {
    if (currentTime.inSession()) {
      emit(ChartMarketUpdateState(currentTime));
    }
  }

  void onTick() {
    _timer = Timer.periodic(const Duration(minutes: 1), (_) => _onTick());
  }

  void showByIndex(int? index) {
    if (index == null) {
      hide();
      return;
    }

    trackballChanged = false;
    trackballBehavior.showByIndex(index);
  }

  void hide() {
    trackballChanged = true;
    trackballBehavior.hide();
  }

  void updateAdapter(ChartMarketAdapter adapter) {
    this.adapter = adapter;
  }

  void updateController(ChartSeriesController controller) {
    this.controller = controller;
  }

  Offset? getOffsetFromIndex(int index) {
    try {
      final item = adapter?.chartData.getElementAt(index);

      if (item == null || controller == null) return null;

      final x = adapter!.getX(item);

      final y = adapter!.getY(item.close);

      if (y == null || y < 0) return null;

      final data = CartesianChartPoint<double>(x: x, y: y);

      return controller!.pointToPixel(data);
    } catch (e, stackTrace) {
      return null;
    }
  }

  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }
}
