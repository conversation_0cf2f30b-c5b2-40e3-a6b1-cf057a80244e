import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/generated/assets.gen.dart';
import 'package:vp_market/generated/l10n.dart';

class NoneTransactionWidget extends StatelessWidget {
  const NoneTransactionWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: themeData.white,
      width: double.infinity,
      child: Column(
        children: [
          VpMarketAssets.images.noTransaction.image(),
          Text(
            VPMarketInfoLocalize.current.noneTransaction,
            style: vpTextStyle.body14?.copyWith(
              color: themeData.gray500,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
