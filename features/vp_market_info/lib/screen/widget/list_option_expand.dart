import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';

class ListOptionExpand extends StatefulWidget {
  const ListOptionExpand({
    Key? key,
    required this.listItem,
    required this.voidCallBack,
    this.enable = true,
    required this.selected,
    this.unselectedStyle,
    this.selectedStyle,
  }) : super(key: key);
  final List<ItemSelect> listItem;
  final Function(ItemSelect) voidCallBack;
  final bool enable;
  final ItemSelect selected;
  final TextStyle? unselectedStyle;
  final TextStyle? selectedStyle;

  @override
  State<ListOptionExpand> createState() => _ListOptionExpandState();
}

class _ListOptionExpandState extends State<ListOptionExpand> {
  ItemSelect? selected;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final _group = AutoSizeGroup();
    ItemSelect? selected =
        widget.listItem
            .where((element) => element.id == widget.selected.id)
            .firstOrNull;
    TextStyle unStyle =
        widget.unselectedStyle ??
        vpTextStyle.captionMedium?.copyWith(color: themeData.black) ??
        TextStyle();
    TextStyle style =
        widget.selectedStyle ??
        vpTextStyle.captionMedium?.copyWith(color: themeData.white) ??
        TextStyle();
    return selected == null
        ? const SizedBox()
        : Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children:
              widget.listItem.map((e) {
                final isSelected = e.id == selected?.id;
                return Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(
                      left: e.id == widget.listItem[0].id ? 0 : 8,
                    ),
                    child: ElevatedButton(
                      onPressed: () {
                        if (widget.enable) {
                          setState(() {
                            e.selected = true;
                            selected = e;
                          });
                          widget.voidCallBack(selected!);
                        }
                      },
                      style: ButtonStyle(
                        shape:
                            MaterialStateProperty.all<RoundedRectangleBorder>(
                              RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                                side: const BorderSide(
                                  width: 1,
                                  color: Colors.transparent,
                                ),
                              ),
                            ),
                        padding: MaterialStateProperty.all<EdgeInsets>(
                          const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                        ),
                        backgroundColor: MaterialStateProperty.all<Color>(
                          isSelected
                              ? themeData.primary
                              : themeData.highlightBg,
                        ),
                      ),
                      child: Container(
                        alignment: Alignment.center,
                        child: AutoSizeText(
                          e.title,
                          style: isSelected ? style : unStyle,
                          textAlign: TextAlign.center,
                          group: _group,
                          maxLines: 1,
                          minFontSize: 2,
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
        );
  }
}
