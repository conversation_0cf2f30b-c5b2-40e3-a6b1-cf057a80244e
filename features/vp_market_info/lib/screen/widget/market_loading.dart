import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';

class MarketLoading extends StatelessWidget {
  const MarketLoading({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _buildLoadingSection(context),
        const SizedBox(
          height: 24,
        ),
        _buildLoadingSection(context)
      ],
    );
  }

  Widget _buildLoadingSection(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: themeData.skeletonBase,
      highlightColor: themeData.skeletonHighLight,
      child: Container(
        height: 343,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: Theme.of(context).scaffoldBackgroundColor,
        ),
      ),
    );
  }
}
