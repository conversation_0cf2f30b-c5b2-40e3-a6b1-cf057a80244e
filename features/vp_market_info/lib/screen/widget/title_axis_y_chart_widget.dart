import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';

class TitleAxisYChartWidget extends StatelessWidget {
  const TitleAxisYChartWidget({
    Key? key,
    required this.buyTitle,
    required this.sellTitle,
  }) : super(key: key);
  final String buyTitle;
  final String sellTitle;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: RotatedBox(
            quarterTurns: -1,
            child: Text(
              buyTitle,
              textAlign: TextAlign.center,
              style: vpTextStyle.captionMedium?.copyWith(
                color: themeData.greenChart,
              ),
            ),
          ),
        ),
        Expanded(
          child: RotatedBox(
            quarterTurns: -1,
            child: Text(
              sellTitle,
              textAlign: TextAlign.center,
              style: vpTextStyle.captionMedium?.copyWith(
                color: themeData.redChart,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
