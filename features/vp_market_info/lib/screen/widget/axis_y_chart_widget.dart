import 'package:flutter/material.dart';
import 'package:vp_market/generated/assets.gen.dart';

class AxisY<PERSON>hartWidget extends StatelessWidget {
  const AxisYChartWidget({Key? key, required this.height}) : super(key: key);
  final double height;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        VpMarketAssets.icons.icArrowUp.svg(height: height * 0.5),
        VpMarketAssets.icons.icArrowDown.svg(height: height * 0.5),
      ],
    );
  }
}
