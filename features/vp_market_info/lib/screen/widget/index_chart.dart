import 'package:flutter/material.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/generated/assets.gen.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/vp_stock_common_navigator.dart';
import 'package:vp_stock_common/widgets/market_index/charts/market_chart.dart';
import 'package:vp_stock_common/widgets/market_index/charts/market_price_chart_extensions.dart';

class IndexChart extends StatefulWidget {
  const IndexChart({required this.market, this.actions, super.key});

  final MarketInfoModel market;

  final List<Widget>? actions;

  @override
  State<IndexChart> createState() => _IndexChartState();
}

class _IndexChartState extends State<IndexChart> {
  MarketInfoModel get marketInfo => widget.market;

  bool showTradingView = false;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 230,
      child: Stack(
        children: [
          if (showTradingView)
            VPTradingView(
              symbol: marketInfo.indexCode.value,
              padding: EdgeInsets.zero,
              showAction: false,
            )
          else
            MarketChart(
              showVolume: true,
              primaryYAxisVisible: true,
              xAxisVisible: true,
              xAxisInterval: 10,
              primaryXAxisFormatter: (value) {
                if (value == 10) return '9h';
                if (value == 120) return '11h';
                if (value == 150) return '13h';
                if (value == 270) return '15h';

                return '';
              },
              indexCode: marketInfo.indexCode,
              referencePrice: marketInfo.reference,
              chartData: [...marketInfo.index.toChartDataList()],
            ),

          Positioned(
            right: 50,
            top: -10,
            child: Row(
              children: [
                Transform.translate(
                  offset: Offset(14, 0),
                  child: CircleIconView(
                    onPressed: () => onToggleChart(),
                    padding: const EdgeInsets.all(6),
                    child:
                        showTradingView
                            ? VpMarketAssets.icons.icPriceChart.svg()
                            : VpMarketAssets.icons.icTradingView.svg(),
                  ),
                ),
                CircleIconView(
                  onPressed:
                      () => stockCommonNavigator.openTradingViewFullScreen(
                        context,
                        args: TradingViewArgs(
                          symbol: marketInfo.indexCode.value,
                        ),
                      ),
                  padding: const EdgeInsets.all(6),
                  child: DesignAssets.icons.icZoom.svg(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void onToggleChart() {
    setState(() => showTradingView = !showTradingView);
  }
}
