import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';

class ChartLoadingWidget extends StatelessWidget {
  const ChartLoadingWidget({Key? key, this.height = 280}) : super(key: key);
  final double height;

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: themeData.skeletonBase,
      highlightColor: themeData.skeletonHighLight,
      child: Container(
        height: height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: Theme.of(context).scaffoldBackgroundColor,
        ),
      ),
    );
  }
}
