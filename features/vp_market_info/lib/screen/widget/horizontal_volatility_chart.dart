import 'dart:async';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/data/market_volatility_entity.dart';
import 'package:vp_market/screen/widget/trackball_behavior.dart';

class MarketVolatilityChartData {
  final String title;
  final int number;
  final Color color;

  MarketVolatilityChartData(this.title, this.number, this.color);
}

class HorizontalVolatilityChart extends StatefulWidget {
  const HorizontalVolatilityChart({
    Key? key,
    required this.data,
    this.isDefault = false,
  }) : super(key: key);

  final MarketVolatilityEntity data;
  final bool? isDefault;

  @override
  State<HorizontalVolatilityChart> createState() =>
      _HorizontalVolatilityChartState();
}

class _HorizontalVolatilityChartState extends State<HorizontalVolatilityChart> {
  int selectedIndex = -1;
  Offset? offset;
  Timer? timer;

  late final TooltipBehavior _tooltipBehavior;

  @override
  void initState() {
    super.initState();
    _tooltipBehavior = TooltipBehavior(
      enable: true,
      color: themeData.gray300,
      shouldAlwaysShow: false,
      duration: 3000,
      builder: (
        dynamic data,
        dynamic point,
        dynamic series,
        int pointIndex,
        int seriesIndex,
      ) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            boxShadow: boxShadowTooltip(),
            color: themeData.white,
          ),
          child: IntrinsicHeight(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  data.title,
                  style: vpTextStyle.captionMedium?.copyWith(
                    color: data.color,
                    fontSize: 13,
                  ),
                ),
                Text(
                  '${data.number} mã',
                  style: vpTextStyle.captionMedium?.copyWith(
                    color: themeData.gray500,
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final isAllZero = _chartData.every((data) => data.number == 0);
    return SfCartesianChart(
      margin: const EdgeInsets.all(0),
      series: <CartesianSeries>[
        ColumnSeries<MarketVolatilityChartData, String>(
          dataSource: _chartData,
          xValueMapper: (MarketVolatilityChartData data, _) => data.title,
          yValueMapper:
              (MarketVolatilityChartData data, _) =>
                  data.number == 0 ? 0.1 : data.number,
          pointColorMapper: (MarketVolatilityChartData data, _) => data.color,
          spacing: 0.25,

          dataLabelSettings: DataLabelSettings(
            builder: ((data, point, series, pointIndex, seriesIndex) {
              return Text(
                '${data.number}',
                style: vpTextStyle.captionMedium?.copyWith(
                  color: themeData.gray700,
                ),
              );
            }),
            labelAlignment: ChartDataLabelAlignment.outer,
            isVisible: true,
          ),

          color: Colors.transparent,
          borderRadius: const BorderRadius.only(
            topRight: Radius.circular(8),
            topLeft: Radius.circular(8),
          ),
          enableTooltip: true,
          // selectionBehavior: SelectionBehavior(enable: true),
          // onCreateRenderer:
          //     (ChartSeries<MarketVolatilityChartData, String> series) {
          //   return _CustomColumnSeriesRenderer(
          //       series as BarSeries<MarketVolatilityChartData, String>,
          //       () => selectedIndex,
          //       () => offset);
          // },
        ),
      ],
      primaryXAxis: const CategoryAxis(isVisible: false),
      primaryYAxis: NumericAxis(
        isVisible: false,
        maximum:
            isAllZero
                ? 1
                : (widget.isDefault ?? false)
                ? 10
                : null,
      ),
      tooltipBehavior: _tooltipBehavior,
      trackballBehavior: TrackballBehavior(
        enable: true,
        markerSettings: const TrackballMarkerSettings(
          markerVisibility: TrackballVisibilityMode.hidden,
        ),
        activationMode: ActivationMode.singleTap,
        tooltipSettings: const InteractiveTooltip(
          color: Colors.transparent,
          arrowWidth: 0,
          connectorLineWidth: 0,
          canShowMarker: false,
        ),
        lineColor: Colors.transparent,
        lineWidth: 0,
        builder: (_, data) {
          return const SizedBox.shrink();
        },
      ),
      onChartTouchInteractionUp: (arg) {
        offset = Offset(arg.position.dx, arg.position.dy);
      },
      onTrackballPositionChanging: (TrackballArgs arg) {
        final index = arg.chartPointInfo.seriesIndex;

        final pointIndex = arg.chartPointInfo.dataPointIndex;

        if (index != null && pointIndex != null) {
          _tooltipBehavior.showByIndex(index, pointIndex);
        }
      },
      plotAreaBorderWidth: 0,
    );
  }

  List<MarketVolatilityChartData> get _chartData {
    return [
      MarketVolatilityChartData(
        widget.data.getTitleLevel(10),
        widget.data.countStockPerchangeLevel10,
        themeData.redChart,
      ),
      MarketVolatilityChartData(
        widget.data.getTitleLevel(9),
        widget.data.countStockPerchangeLevel9,
        themeData.redChart,
      ),
      MarketVolatilityChartData(
        widget.data.getTitleLevel(8),
        widget.data.countStockPerchangeLevel8,
        themeData.redChart,
      ),
      MarketVolatilityChartData(
        widget.data.getTitleLevel(7),
        widget.data.countStockPerchangeLevel7,
        themeData.redChart,
      ),
      MarketVolatilityChartData(
        widget.data.getTitleLevel(6),
        widget.data.countStockPerchangeLevel6,
        themeData.redChart,
      ),
      MarketVolatilityChartData(
        '0%',
        widget.data.countStockPerchangeLevel0,
        themeData.referenceColor,
      ),
      MarketVolatilityChartData(
        widget.data.getTitleLevel(5),
        widget.data.countStockPerchangeLevel5,
        themeData.increaseColor,
      ),
      MarketVolatilityChartData(
        widget.data.getTitleLevel(4),
        widget.data.countStockPerchangeLevel4,
        themeData.increaseColor,
      ),
      MarketVolatilityChartData(
        widget.data.getTitleLevel(3),
        widget.data.countStockPerchangeLevel3,
        themeData.increaseColor,
      ),
      MarketVolatilityChartData(
        widget.data.getTitleLevel(2),
        widget.data.countStockPerchangeLevel2,
        themeData.increaseColor,
      ),
      MarketVolatilityChartData(
        widget.data.getTitleLevel(1),
        widget.data.countStockPerchangeLevel1,
        themeData.increaseColor,
      ),
    ];
  }
}
