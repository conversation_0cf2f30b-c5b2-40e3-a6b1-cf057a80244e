import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/data/market_time_hour.dart';

class MarketTimeChart extends StatelessWidget {
  const MarketTimeChart({Key? key, this.offset}) : super(key: key);
  final double? offset;

  @override
  Widget build(BuildContext context) {
    final widthView = MediaQuery.of(context).size.width - ((offset ?? 0) * 2);
    final width1 = ((widthView / 270) * 120) - 5;
    final width2 = ((widthView / 270) * 30);
    return Row(
      children: [
        Expanded(
          child: Row(
            children: [
              SizedBox(width: width1, child: buildTitleTime(MarketTimeHour.h9)),
              SizedBox(
                width: width2,
                child: buildTitleTime(MarketTimeHour.h11),
              ),
              Expanded(
                child: Container(child: buildTitleTime(MarketTimeHour.h13)),
              ),
              buildTitleTime(MarketTimeHour.h15),
            ],
          ),
        ),
        const SizedBox(width: 10),
      ],
    );
  }

  buildTitleTime(int time) {
    return Text(
      '${time}h',
      style: vpTextStyle.captionMedium?.copyWith(color: themeData.black),
    );
  }
}
