import 'dart:math';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_market/data/chart_price_market_entity.dart';
import 'package:vp_market/screen/chart_volume/base_chart_adapter.dart';

class ChartMarketAdapter extends BaseChartAdapter<ChartPriceMarketEntity> {
  ChartMarketAdapter(this._data, this.referencePrice, this.miniChart);

  final double referencePrice;

  final bool miniChart;

  final List<ChartPriceMarketEntity> _data;

  final _chartData = <ChartPriceMarketEntity>[];

  List<ChartPriceMarketEntity> get chartData => _chartData;

  /// set frequent
  int frequent = AppConstants.frequent;

  ChartMarketAdapter setFrequent(int frequent) {
    this.frequent = frequent;

    return this;
  }

  DateTime get currentTime => DateTime.now();

  /// set max points, will show in chart
  int maxPoints = AppConstants.maxPointInDay;

  ChartMarketAdapter setMaxPoints(int maxPoints) {
    this.maxPoints = maxPoints;

    return this;
  }

  @override
  double get maxX {
    final value =
        isAfter15Hour || _chartData.length > maxPoints
            ? _chartData.length
            : maxPoints;

    return (value >= 1 ? value - 1 : value).toDouble();
  }

  @override
  double get maxY => 120;

  @override
  double get minX => 0;

  @override
  double get minY => -20;

  @override
  List<ChartPriceMarketEntity> build() {
    _chartData.clear();

    _data.removeWhere((e) => e.timeOfDay == null);

    _data.removeWhere((e) {
      final time = e.timeOfDay;

      return time!.inBreakTime() || time.outOfSession();
    });

    if (_data.isNullOrEmpty) {
      _data.add(
        ChartPriceMarketEntity(
          x: 0,
          volume: 0,
          totalVolume: 0,
          close: referencePrice,
          time: const TimeOfDay(hour: 9, minute: 0).toStringTime(),
        ),
      );
    }

    _groupDuplicateData();

    _appendLostData();

    _filterItem();

    _chartData.addAll(_data);

    if (!miniChart) _appendEmptyData();

    for (int i = 0; i < _chartData.length; i++) {
      _chartData[i].x = i;
    }

    return _chartData;
  }

  double get minPrice {
    if (_data.isNullOrEmpty) {
      return referencePrice;
    }

    return min(_data.map((e) => e.close).toList().reduce(min), referencePrice);
  }

  double get maxPrice {
    if (_data.isNullOrEmpty) {
      return referencePrice;
    }

    return max(_data.map((e) => e.close).toList().reduce(max), referencePrice);
  }

  double? getX(ChartPriceMarketEntity? entity) => entity?.x.toDouble();

  double? getY(double? price) {
    final space = maxPrice - minPrice;

    return price == null || price == -1
        ? null
        : space == 0
        ? 50
        : (price - minPrice) * 100 / space;
  }

  bool get isAfter15Hour => currentTime.isAfter15Hour();

  void _appendEmptyData() {
    if (isAfter15Hour) return;

    final remainPoints = maxPoints - chartData.length;

    if (remainPoints <= 0) return;

    final emptyList = List.generate(
      remainPoints,
      (index) => ChartPriceMarketEntity(
        /// no important, random value
        x: index,

        /// no important, random value
        time: '00:00',

        /// important: not change, check ChartPriceMarketEntity is fake
        close: -1,

        volume: -1,
      ),
    );

    chartData.addAll(emptyList);
  }

  void _appendLostData() {
    if (_data.isNullOrEmpty) return;

    final lastChartItem = _data.last;

    var lastTime = lastChartItem.time.hhmmssToTime;

    if (lastTime?.isEqualsOrAfter15Hour() == true) {
      lastTime = const TimeOfDay(hour: 15, minute: 0);
    } else if (currentTime.inSession()) {
      lastTime = currentTime.toTimeOfDay();
    } else if (currentTime.isAfter15Hour()) {
      lastTime = const TimeOfDay(hour: 15, minute: 0);
    }

    if (lastTime == null) return;

    var moveTime = const TimeOfDay(hour: 9, minute: 0);

    /// add time 9:00:00 if not exist
    /// Fix exception if _data don't contain time at 09:00:00
    final exist9Time = _data.any((e) => e.timeOfDay!.isSameTime(moveTime));
    if (!exist9Time) {
      _data.insert(
        0,
        ChartPriceMarketEntity(
          x: 0,
          volume: 0,
          totalVolume: 0,
          close: referencePrice,
          time: moveTime.toStringTime(),
        ),
      );
    }

    while (moveTime.isEqualOrBefore(lastTime)) {
      if (moveTime.isAfter15Hour()) break;

      final existTime = _data.any((e) => e.timeOfDay!.isSameTime(moveTime));

      if (existTime || moveTime.inBreakTime()) {
        moveTime = moveTime.addMinutes(frequent);

        continue;
      }

      final index = _data.lastIndexWhere(
        (e) => e.timeOfDay!.isBefore(moveTime),
      );

      /// break if _data don't contain time at 09:00:00
      if (index == -1) break;

      _data.insert(
        index + 1,
        _data[index].copyWith(
          volume: 0,
          totalVolume: 0,
          time: moveTime.toStringTime(),
        ),
      );

      moveTime = moveTime.addMinutes(frequent);
    }
  }

  void _filterItem() =>
      _data.removeWhere((e) => e.timeOfDay!.minute % frequent != 0);

  void _groupDuplicateData() {
    if (_data.isNullOrEmpty) return;

    final list = <ChartPriceMarketEntity>[];

    for (final item in _data) {
      final itemTime = item.time.hhmmssToTime;

      final index = list.indexWhere((e) => e.timeOfDay!.isSameTime(itemTime));

      if (index != -1) {
        final value = list[index];

        final newVolume = (value.volume ?? 0) + (item.volume ?? 0);

        final newValue = item.copyWith(volume: newVolume);

        list[index] = newValue;
      } else {
        list.add(item);
      }
    }

    _data
      ..clear()
      ..addAll(list);
  }

  List<FlSpot> getBarData() {
    final space = maxPrice - minPrice;
    return chartData.map((e) {
      return FlSpot(
        e.x.toDouble(),
        space == 0 || e.close == -1 ? 50 : (e.close - minPrice) * 100 / space,
      );
    }).toList();
  }
}
