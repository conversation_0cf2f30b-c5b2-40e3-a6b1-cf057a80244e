import 'package:flutter/material.dart';
import 'package:vp_common/extensions/price_exts.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';

class ChartPriceView extends StatelessWidget {
  const ChartPriceView({
    required this.minY,
    required this.maxY,
    required this.chartHeight,
    required this.maxPrice,
    required this.minPrice,
    this.convertToThousand = true,
    this.showAtPercents = const [0, 50, 100],
    Key? key,
  }) : super(key: key);

  final double minY;

  final double maxY;

  final double chartHeight;

  final double maxPrice;

  final double minPrice;

  final List<int> showAtPercents;

  final bool convertToThousand;

  @override
  Widget build(BuildContext context) {
    return buildPriceView();
  }

  double getBottom(int percent) {
    final minYAbs = minY.abs();

    final totalPercent = maxY + minYAbs;

    final value = ((percent + minYAbs) / totalPercent) * chartHeight;

    return value - 8;
  }

  Widget _buildItemPriceView({required double bottom, required double? price}) {
    return Positioned(
      left: 10,
      bottom: bottom,
      child: Text(
        (price ?? 0).getPriceFormatted(
          currency: '',
          convertToThousand: convertToThousand,
        ),
        style: vpTextStyle.captionRegular?.copyWith(color: themeData.gray500),
      ),
    );
  }

  Widget buildPriceView() {
    return Stack(
      children: [
        Positioned(
          left: 0,
          top: 0,
          bottom: 0,
          child: Container(
            width: 1,
            height: double.infinity,
            color: themeData.divider,
          ),
        ),
        SizedBox(
          height: chartHeight,
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              ...showAtPercents
                  .map(
                    (e) => _buildItemPriceView(
                      bottom: getBottom(e),
                      price: getPrice(e),
                    ),
                  )
                  .toList(),
            ],
          ),
        ),
      ],
    );
  }

  double getPrice(int percent) {
    final space = maxPrice - minPrice;

    return minPrice + (percent * space) / 100;
  }
}
