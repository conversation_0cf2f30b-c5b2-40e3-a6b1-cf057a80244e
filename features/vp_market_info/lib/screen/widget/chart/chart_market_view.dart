import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:vp_common/extensions/double_extensions.dart';
import 'package:vp_common/extensions/list_extensions.dart';
import 'package:vp_common/extensions/price_exts.dart';
import 'package:vp_common/extensions/string_extensions.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/common/mixin/chart_style_mixin.dart';
import 'package:vp_market/core/const/market_info_constants.dart';
import 'package:vp_market/data/chart_price_market_entity.dart';
import 'package:vp_market/data/market_entity.dart';
import 'package:vp_market/generated/assets.gen.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/chart_market/chart_market_bloc.dart';
import 'package:vp_market/screen/chart_market/chart_market_state.dart';
import 'package:vp_market/screen/chart_volume/chart_volume_bloc.dart';
import 'package:vp_market/screen/widget/chart/chart_market_adapter.dart';
import 'package:vp_market/screen/widget/chart/chart_price_view.dart';
import 'package:vp_market/screen/widget/chart/chart_volume_view.dart';
import 'package:vp_market/screen/widget/chart/empty_chart_view.dart';
import 'package:vp_market/screen/widget/trackball_behavior.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

class ChartMarketView extends StatefulWidget {
  const ChartMarketView({
    Key? key,
    required this.marketEntity,
    required this.chartData,
  }) : super(key: key);

  final List<ChartPriceMarketEntity> chartData;

  final MarketEntity marketEntity;

  @override
  State<ChartMarketView> createState() => _ChartMarketViewState();
}

class _ChartMarketViewState extends State<ChartMarketView>
    with ChartStyleMixin {
  late ChartMarketBloc bloc;

  late ChartMarketAdapter adapter;

  late TrackballBehavior trackballBehavior;

  ChartVolumeBloc? chartVolumeBloc;

  TrackballMarkerSettings? getMarkerSettings() {
    final price =
        widget.chartData.lastOrNull?.close ?? widget.marketEntity.getReference;

    return getTrackBallMarkerSetting(
      chartColorByPrice(
        reference: widget.marketEntity.getReference,
        current: price,
      ),
    );
  }

  @override
  void initState() {
    super.initState();

    trackballBehavior = TrackballBehavior(
      enable: true,
      tooltipSettings: InteractiveTooltip(
        enable: true,
        color: themeData.gray100,
      ),
      lineDashArray: const [5, 5],
      lineColor: themeData.gray500,
      activationMode: ActivationMode.singleTap,
      markerSettings: getMarkerSettings(),
      tooltipDisplayMode: TrackballDisplayMode.floatAllPoints,
      shouldAlwaysShow: false,
      builder: (_, args) {
        final data = adapter.chartData[args.pointIndex ?? 0];
        final volume =
            '${VPMarketInfoLocalize.current.volume}: ${MarketInfoConstants.formatVol(data.volume)}';
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            boxShadow: boxShadowTooltip(),
            color: themeData.white,
          ),
          child: IntrinsicHeight(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  data.time.hhmmToTime,
                  style: vpTextStyle.captionRegular?.copyWith(
                    color: themeData.gray500,
                  ),
                ),
                Text(
                  '${VPMarketInfoLocalize.current.index}: ${data.close}',
                  style: vpTextStyle.subtitle14?.copyWith(
                    color: chartColorByPrice(
                      reference: widget.marketEntity.getReference,
                      current: data.close,
                    ),
                  ),
                ),
                IntrinsicWidth(
                  child: Row(
                    children: [
                      _getPriceIcon(data.close),
                      SizedBox(
                        width:
                            widget.marketEntity.getReference == data.close
                                ? 0
                                : 4,
                      ),
                      Text(
                        '${_getChangeValue(data.close)} (${_getChangePercent(data.close)})',
                        style: vpTextStyle.captionMedium?.copyWith(
                          color: chartColorByPrice(
                            reference: widget.marketEntity.getReference,
                            current: data.close,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  volume,
                  style: vpTextStyle.captionMedium?.copyWith(
                    color: themeData.gray700,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );

    bloc = ChartMarketBloc(trackballBehavior);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 10),
      child: BlocProvider<ChartMarketBloc>.value(
        value: bloc,
        child: BlocBuilder<ChartMarketBloc, ChartMarketState>(
          builder: (_, __) {
            adapter =
                ChartMarketAdapter(
                    List.of(widget.chartData),
                    widget.marketEntity.getReference,
                    false,
                  )
                  ..setFrequent(1)
                  ..setMaxPoints(270)
                  ..build();

            bloc.updateAdapter(adapter);

            if (adapter.chartData.isNullOrEmpty) {
              return const SizedBox(height: 137, child: EmptyChartView());
            }

            return Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    children: [
                      /// Build chart view
                      buildChartView(),

                      /// chart volume view
                      buildChartVolumeView(),
                    ],
                  ),
                ),
                if (widget.chartData.hasData)
                  SizedBox(
                    width: 50,
                    height: 187,
                    child: ChartPriceView(
                      chartHeight: 137,
                      minY: adapter.minY,
                      maxY: adapter.maxY,
                      minPrice: getMinPrice(),
                      maxPrice: getMaxPrice(),
                      convertToThousand: false,
                    ),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }

  double getMinPrice() {
    final closes =
        adapter.chartData.map((e) => e.close).toSet()
          ..removeWhere((e) => e < 0);

    if (closes.length == 1) {
      final price = closes.first - 10;

      return price < 0 ? 0 : price;
    }

    return adapter.minPrice;
  }

  double getMaxPrice() {
    final closes =
        adapter.chartData.map((e) => e.close).toSet()
          ..removeWhere((e) => e < 0);

    if (closes.length == 1) {
      final price = closes.first + 10;

      return price < 0 ? 0 : price;
    }

    return adapter.maxPrice;
  }

  Widget buildChartView() {
    return SizedBox(
      height: 137,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          SfCartesianChart(
            plotAreaBorderWidth: 0,
            margin: const EdgeInsets.all(0),
            series: [
              LineSeries<ChartPriceMarketEntity, num>(
                dataSource: adapter.chartData,
                dashArray: const [10, 10],
                width: 1,
                color: themeData.gray500,
                enableTooltip: false,
                selectionBehavior: SelectionBehavior(enable: false),
                xValueMapper: (ChartPriceMarketEntity data, _) => data.x,
                yValueMapper:
                    (ChartPriceMarketEntity data, _) =>
                        adapter.getY(widget.marketEntity.getReference),
              ),
            ],
            // tooltipBehavior: _tooltipBehavior,
            primaryXAxis: NumericAxis(
              isVisible: false,
              maximum: 16,
              minimum: 0,
            ),
            primaryYAxis: NumericAxis(
              isVisible: false,
              minimum: adapter.minY,
              maximum: adapter.maxY,
            ),
          ),
          _buildDefaultLineChart(adapter),
        ],
      ),
    );
  }

  Widget buildChartVolumeView() {
    return SizedBox(
      height: 50,
      child: ChartVolumeView(
        data: adapter.chartData.map((e) => e.toChartVolumeModel()).toList(),
        referencePrice: widget.marketEntity.getReference,
        createBloc: () {
          return chartVolumeBloc = ChartVolumeBloc();
        },
        onItemFocusChanged: (index) {
          bloc.showByIndex(index);
        },
        getX: (int index) => bloc.getOffsetFromIndex(index)?.dx,
        typeChart: MarketInfoConstants.chartMarket,
      ),
    );
  }

  /// Get the cartesian chart with default line series
  SfCartesianChart _buildDefaultLineChart(ChartMarketAdapter adapter) {
    return SfCartesianChart(
      plotAreaBorderWidth: 0,
      margin: EdgeInsets.zero,
      series: _getDefaultLineSeries(adapter),
      trackballBehavior: trackballBehavior,
      onChartTouchInteractionUp: (_) {
        chartVolumeBloc?.hide();
      },
      onTrackballPositionChanging: (data) {
        if (bloc.trackballChanged) {
          final index = data.chartPointInfo.dataPointIndex;
          chartVolumeBloc?.showByIndex(index);
        }
      },
      primaryXAxis: NumericAxis(
        isVisible: false,
        maximum: adapter.maxX,
        minimum: adapter.minX,
      ),
      primaryYAxis: NumericAxis(
        isVisible: false,
        minimum: adapter.minY,
        maximum: adapter.maxY,
      ),
    );
  }

  /// The method returns line series to chart.
  List<SplineSeries<ChartPriceMarketEntity, num>> _getDefaultLineSeries(
    ChartMarketAdapter adapter,
  ) {
    return <SplineSeries<ChartPriceMarketEntity, num>>[
      SplineSeries<ChartPriceMarketEntity, num>(
        splineType: SplineType.cardinal,
        animationDuration: 0,
        dataSource: adapter.chartData,
        width: 2,
        xValueMapper: (ChartPriceMarketEntity sales, _) {
          return adapter.getX(sales);
        },
        yValueMapper: (ChartPriceMarketEntity sales, _) {
          return adapter.getY(sales.close);
        },
        onRendererCreated: (controller) {
          bloc.updateController(controller);
        },
        selectionBehavior: SelectionBehavior(
          enable: false,
          selectedBorderWidth: 4,
          unselectedBorderWidth: 2,
        ),
        onCreateShader:
            (ShaderDetails chartShaderDetails) => ui.Gradient.linear(
              chartShaderDetails.rect.centerLeft,
              chartShaderDetails.rect.topRight,
              getChartColors(),
              chartGradientStops,
            ),
      ),
    ];
  }

  List<Color> getChartColors() {
    return chartColors(
      reference: widget.marketEntity.getReference,
      current:
          widget.chartData.lastOrNull?.close ??
          adapter.chartData.lastOrNull?.close ??
          widget.marketEntity.getReference,
    );
  }

  Widget _getPriceIcon(double index) {
    final currentPrice = index.toDouble().toPrecision(2);
    final referencePrice = widget.marketEntity.getReference.toPrecision(2);

    if (referencePrice == currentPrice) {
      return const SizedBox();
    }

    if (currentPrice > referencePrice) {
      return VpMarketAssets.icons.icArrowIncrease.svg();
    }
    return VpMarketAssets.icons.icArrowDecrease.svg();
  }

  String _getChangePercent(double index) {
    double changePercent =
        widget.marketEntity.getReference != 0
            ? (((index - widget.marketEntity.getReference) /
                    widget.marketEntity.getReference) *
                100)
            : 0;

    if (changePercent.toPrecision(2) == 0) {
      return '0%';
    }

    return changePercent.getMarketChangePercentDisplay();
  }

  String _getChangeValue(double index) {
    double changeValue = index - widget.marketEntity.getReference;

    if (changeValue.toPrecision(2) == 0) {
      return '0';
    }

    return changeValue.toStringAsFixed(2);
  }

  @override
  void dispose() {
    bloc.close();
    super.dispose();
  }
}
