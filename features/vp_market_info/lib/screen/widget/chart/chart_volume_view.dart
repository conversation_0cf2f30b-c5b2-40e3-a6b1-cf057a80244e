import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/data/chart_volume_model.dart';
import 'package:vp_market/screen/chart_volume/chart_volume_adapter.dart';
import 'package:vp_market/screen/chart_volume/chart_volume_bloc.dart';
import 'package:vp_market/screen/chart_volume/chart_volume_state.dart';
import 'package:collection/collection.dart';
import 'package:vp_market/screen/widget/dotted_line_view.dart';

double? chartWidth;
const double _widthMaxScreen = 800;

class ChartVolumeView extends StatefulWidget {
  const ChartVolumeView({
    required this.data,
    required this.referencePrice,
    this.ceilingPrice,
    this.floorPrice,
    this.createBloc,
    required this.getX,
    this.onItemFocusChanged,
    this.typeData,
    required this.typeChart,
    this.maxPoints,
    Key? key,
  }) : super(key: key);

  final List<ChartVolumeModel> data;

  final double referencePrice;

  final double? ceilingPrice;

  final double? floorPrice;

  final ChartVolumeBloc Function()? createBloc;

  final double? Function(int index) getX;

  final void Function(int? index)? onItemFocusChanged;

  final String? typeData;

  final String typeChart;

  final int? maxPoints;

  @override
  State<ChartVolumeView> createState() => _ChartVolumeViewState();
}

class _ChartVolumeViewState extends State<ChartVolumeView> {
  late ChartVolumeBloc bloc;

  late ChartVolumeAdapter adapter;

  bool showChart = false;

  @override
  void didUpdateWidget(covariant ChartVolumeView oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.typeData != oldWidget.typeData) {
      size = null;
      delayShowChart();
    }
  }

  void delayShowChart() {
    showChart = false;
    Future.delayed(
      const Duration(milliseconds: 300),
      () => {if (mounted) setState(() => showChart = true)},
    );
  }

  @override
  void initState() {
    super.initState();

    bloc = widget.createBloc?.call() ?? ChartVolumeBloc();

    delayShowChart();
  }

  double? size;

  @override
  Widget build(BuildContext context) {
    adapter = ChartVolumeAdapter(
      List.of(widget.data)..removeWhere((e) => e.volume == -1),
      widget.referencePrice,
      ceilingPrice: widget.ceilingPrice,
      floorPrice: widget.floorPrice,
      typeData: widget.typeData,
      typeChart: widget.typeChart,
    )..build();

    if (showChart) {
      final newSize = getSize();

      if (newSize != 0) size = newSize;
    }

    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        chartWidth ??= constraints.maxWidth;

        return BlocProvider<ChartVolumeBloc>.value(
          value: bloc,
          child:
              showChart
                  ? BlocListener<ChartVolumeBloc, ChartVolumeState>(
                    listener: (_, state) {
                      if (state is ChartVolumeItemFocusChangedState) {
                        if (state.focused) {
                          widget.onItemFocusChanged?.call(state.index);
                        }
                      }
                    },
                    child: GestureDetector(
                      onTapDown:
                          (details) => bloc.checkItemFocus(
                            details.globalPosition,
                            size ?? 0,
                          ),
                      onHorizontalDragEnd: (_) => bloc.hide(focused: true),
                      onTapUp: (_) => bloc.hide(focused: true),
                      onHorizontalDragUpdate:
                          (details) => bloc.checkItemFocus(
                            details.globalPosition,
                            size ?? 0,
                          ),
                      onLongPressEnd: (_) => bloc.hide(focused: true),
                      onLongPressMoveUpdate:
                          (details) => bloc.checkItemFocus(
                            details.globalPosition,
                            size ?? 0,
                          ),
                      child: Container(
                        height: 50,
                        width: double.infinity,
                        color: Colors.transparent,
                        child: Stack(
                          children: [
                            BlocBuilder<ChartVolumeBloc, ChartVolumeState>(
                              builder: (_, state) {
                                final left =
                                    bloc.indexItemFocus == null
                                        ? null
                                        : widget.getX(bloc.indexItemFocus!);

                                if (left != null) {
                                  return Positioned(
                                    height: 50,
                                    left: left,
                                    child: DottedLine(
                                      direction: Axis.vertical,
                                      lineLength: 50,
                                      lineThickness: 1.0,
                                      dashLength: 5.0,
                                      dashGapLength: 5.0,
                                      dashColor: themeData.gray500,
                                    ),
                                  );
                                }

                                return const SizedBox(height: 0);
                              },
                            ),
                            ...adapter.chartData.map((e) {
                              if (size == 0 || size == null) size = getSize();

                              final index = adapter.chartData.indexOf(e);

                              final left = widget.getX(index);

                              if (left == null &&
                                  bloc.positions[index] == null) {
                                return const SizedBox(height: 0);
                              }

                              if (left != null) {
                                bloc.positions[index] =
                                    left - ((size ?? 0) / 2);
                              }

                              return Positioned(
                                bottom: 0,
                                left: bloc.positions[index],
                                child: ContainerAnimation(
                                  width: size ?? 0,
                                  height: 50 * (adapter.getY(e) / 100),
                                  color: adapter.getColor(e),
                                  index: index,
                                ),
                              );
                            }).toList(),
                          ],
                        ),
                      ),
                    ),
                  )
                  : const SizedBox(height: 50, width: double.infinity),
        );
      },
    );
  }

  double getSize() {
    final lastItem = adapter.chartData.lastWhereOrNull((e) => e.volume > 0);

    if (lastItem == null) return 0;

    final lastIndex = adapter.chartData.indexOf(lastItem);

    if (lastIndex == 0) return _widthMaxScreen;

    final firstX = widget.getX(0);

    final lastX = widget.getX(lastIndex);

    if (firstX == null || lastX == null) {
      if (widget.maxPoints != null && chartWidth != null) {
        return chartWidth! / (widget.maxPoints! * 1.5);
      }

      return 0;
    }

    final space = lastX - firstX;

    return space / (lastIndex * 1.5);
  }

  @override
  void dispose() {
    bloc.close();
    super.dispose();
  }
}

class ContainerAnimation extends StatefulWidget {
  const ContainerAnimation({
    required this.width,
    required this.height,
    required this.color,
    required this.index,
    Key? key,
  }) : super(key: key);

  final double width;

  final double height;

  final Color? color;

  final int index;

  @override
  State<ContainerAnimation> createState() => _ContainerAnimationState();
}

class _ContainerAnimationState extends State<ContainerAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController animationController;

  @override
  void initState() {
    super.initState();

    animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    )..forward();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.width == 0 || widget.height == 0) {
      return const SizedBox(height: 0);
    }

    final bloc = context.read<ChartVolumeBloc>();

    return AnimatedBuilder(
      builder: (BuildContext context, Widget? child) {
        return BlocBuilder<ChartVolumeBloc, ChartVolumeState>(
          builder: (_, state) {
            final focused = widget.index == bloc.indexItemFocus;

            return Container(
              width: widget.width,
              height: animationController.value * widget.height,
              alignment: Alignment.bottomCenter,
              decoration: BoxDecoration(
                color: focused ? widget.color : widget.color?.withOpacity(0.4),
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(6),
                ),
              ),
            );
          },
        );
      },
      animation: animationController,
    );
  }

  @override
  void dispose() {
    animationController.dispose();
    super.dispose();
  }
}
