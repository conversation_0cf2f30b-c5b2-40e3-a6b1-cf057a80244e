import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';

class DashedLine extends StatelessWidget {
  const DashedLine({
    this.height = 1,
    this.dashWidth = 10,
    this.color,
    Key? key,
  }) : super(key: key);

  final double height;

  final double dashWidth;

  final Color? color;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        final boxWidth = constraints.constrainWidth();
        final dashHeight = height;
        final dashCount = (boxWidth / (2 * dashWidth)).floor();
        return Flex(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          direction: Axis.horizontal,
          children: List.generate(dashCount, (_) {
            return SizedBox(
              width: dashWidth,
              height: dashHeight,
              child: DecoratedBox(decoration: BoxDecoration(color: color ?? themeData.gray700,)),
            );
          }),
        );
      },
    );
  }
}
