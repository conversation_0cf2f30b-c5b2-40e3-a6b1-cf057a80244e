import 'package:flutter/material.dart';
import 'package:vp_market/screen/widget/chart/dashed_line.dart';

class EmptyChartView extends StatelessWidget {
  const EmptyChartView({
    Key? key,
    this.height = 0.25,
    this.dashWidth = 5,
  }) : super(key: key);

  final double height;

  final double dashWidth;

  @override
  Widget build(BuildContext context) {
    return Center(child: DashedLine(dashWidth: dashWidth, height: height));
  }
}
