class TopForeignTradingDObj {
  List<ForeignNetDObj>? foreignNetBuy;
  List<ForeignNetDObj>? foreignNetSell;
  List<ForeignNetDObj>? topValueBuy;
  List<ForeignNetDObj>? topValueSell;

  TopForeignTradingDObj(
      {this.foreignNetBuy,
      this.foreignNetSell,
      this.topValueBuy,
      this.topValueSell});

  TopForeignTradingDObj.fromJson(Map<String, dynamic> json) {
    if (json['ForeignNetBuy'] != null) {
      foreignNetBuy = <ForeignNetDObj>[];
      json['ForeignNetBuy'].forEach((v) {
        foreignNetBuy!.add(ForeignNetDObj.fromJson(v));
      });
    }
    if (json['ForeignNetSell'] != null) {
      foreignNetSell = <ForeignNetDObj>[];
      json['ForeignNetSell'].forEach((v) {
        foreignNetSell!.add(ForeignNetDObj.fromJson(v));
      });
    }
    if (json['TopValueBuy'] != null) {
      topValueBuy = <ForeignNetDObj>[];
      json['TopValueBuy'].forEach((v) {
        topValueBuy!.add(ForeignNetDObj.fromJson(v));
      });
    }
    if (json['TopValueSell'] != null) {
      topValueSell = <ForeignNetDObj>[];
      json['TopValueSell'].forEach((v) {
        topValueSell!.add(ForeignNetDObj.fromJson(v));
      });
    }
  }
}

class ForeignNetDObj {
  String? symbol;
  String? fullName;
  num? foreignBuyVolume;
  num? foreignBuyValue;
  num? foreignSellVolume;
  num? foreignSellValue;
  num? foreignNetBuy;
  num? foreignNetSell;
  num? reference;
  num? closePrice;
  num? change;
  num? stockPercentChange;

  ForeignNetDObj(
      {this.symbol,
      this.fullName,
      this.foreignBuyVolume,
      this.foreignBuyValue,
      this.foreignSellVolume,
      this.foreignSellValue,
      this.foreignNetBuy,
      this.foreignNetSell,
      this.reference,
      this.closePrice,
      this.change,
      this.stockPercentChange});

  ForeignNetDObj.fromJson(Map<String, dynamic> json) {
    symbol = json['symbol'];
    fullName = json['fullName'];
    foreignBuyVolume = json['foreignBuyVolume'];
    foreignBuyValue = json['foreignBuyValue'];
    foreignSellVolume = json['foreignSellVolume'];
    foreignSellValue = json['foreignSellValue'];
    foreignNetBuy = json['foreignNetBuy'];
    foreignNetSell = json['foreignNetSell'];
    reference = json['reference'];
    closePrice = json['closePrice'];
    change = json['change'];
    stockPercentChange = json['stockPercentChange'];
  }
}
