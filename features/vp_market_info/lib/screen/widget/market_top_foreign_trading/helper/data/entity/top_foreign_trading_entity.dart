import 'package:vp_market/data/top_foreign_trading_d_obj.dart';
import 'package:vp_market/data/top_foreign_trading_obj.dart';
import 'package:vp_market/screen/widget/market_top_foreign_trading/helper/data/entity/top_foreign_entity.dart';

class ListForeignNetEntity {
  TopForeignTradingDObj? objD;
  TopForeignTradingObj? obj;
  List<TopForeignEntity> listForeignNetBuy = [];
  List<TopForeignEntity> listForeignNetSell = [];
  List<TopForeignEntity> listTopValueBuy = [];
  List<TopForeignEntity> listTopValueSell = [];

  //// INIT - OBJ - D
  ListForeignNetEntity.initObjD(this.objD) {
    setListForeignNetBuyD();
    setListForeignNetSellD();
    setListTopValueSellD();
    setListTopValueBuyD();
  }

  void setListForeignNetBuyD() {
    final list = objD?.foreignNetBuy ?? [];
    for (var e in list) {
      listForeignNetBuy.add(
        TopForeignEntity(
          symbol: e.symbol,
          fullName: e.fullName,
          value: e.foreignNetBuy,
        ),
      );
    }
  }

  void setListForeignNetSellD() {
    final list = objD?.foreignNetSell ?? [];
    for (var e in list) {
      listForeignNetSell.add(
        TopForeignEntity(
          symbol: e.symbol,
          fullName: e.fullName,
          value: e.foreignNetSell,
        ),
      );
    }
  }

  void setListTopValueBuyD() {
    final list = objD?.topValueBuy ?? [];
    for (var e in list) {
      listTopValueBuy.add(
        TopForeignEntity(
          symbol: e.symbol,
          fullName: e.fullName,
          value: e.foreignBuyValue,
        ),
      );
    }
  }

  void setListTopValueSellD() {
    final list = objD?.topValueSell ?? [];
    for (var e in list) {
      listTopValueSell.add(
        TopForeignEntity(
          symbol: e.symbol,
          fullName: e.fullName,
          value: e.foreignSellValue,
        ),
      );
    }
  }

  //// INIT - OBJ
  ListForeignNetEntity.initObj(this.obj) {
    setListForeignNetBuy();
    setListForeignNetSell();
    setListTopValueSell();
    setListTopValueBuy();
  }

  void setListForeignNetBuy() {
    final list = obj?.foreignNetBuy ?? [];
    for (var e in list) {
      listForeignNetBuy.add(
        TopForeignEntity(
          symbol: e.symbol,
          fullName: e.fullName,
          value: e.totalForeignNetBuy,
        ),
      );
    }
  }

  void setListForeignNetSell() {
    final list = obj?.foreignNetSell ?? [];
    for (var e in list) {
      listForeignNetSell.add(
        TopForeignEntity(
          symbol: e.symbol,
          fullName: e.fullName,
          value: e.totalForeignNetSell,
        ),
      );
    }
  }

  void setListTopValueBuy() {
    final list = obj?.topValueBuy ?? [];
    for (var e in list) {
      listTopValueBuy.add(
        TopForeignEntity(
          symbol: e.symbol,
          fullName: e.fullName,
          value: e.totalForeignBuyValue,
        ),
      );
    }
  }

  void setListTopValueSell() {
    final list = obj?.topValueSell ?? [];
    for (var e in list) {
      listTopValueSell.add(
        TopForeignEntity(
          symbol: e.symbol,
          fullName: e.fullName,
          value: e.totalForeignSellValue,
        ),
      );
    }
  }
}
