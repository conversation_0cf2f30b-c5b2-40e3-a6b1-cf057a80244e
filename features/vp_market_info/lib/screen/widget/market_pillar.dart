import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/data/market_volatility_entity.dart';
import 'package:vp_market/generated/l10n.dart';

class MarketPillar extends StatelessWidget {
  const MarketPillar({
    Key? key,
    this.advances = 60,
    this.noChanges = 15,
    this.declines = 60,
    this.height,
    this.total,
    this.totalReduce = 0,
    this.totalIncrease = 0,
    this.data,
  }) : super(key: key);

  final int advances;
  final int noChanges;
  final int declines;
  final double? height;
  final int? total;
  final int totalReduce;
  final int totalIncrease;
  final MarketVolatilityEntity? data;

  @override
  Widget build(BuildContext context) {
    late final num increaseRatio;
    late final num referenceRatio;
    late final num decreaseRatio;
    if (total != null && total != 0) {
      increaseRatio = advances * 100 / total!;
      referenceRatio = noChanges * 100 / total!;
      decreaseRatio = declines * 100 / total!;
    } else {
      final int total = advances + noChanges + declines;
      increaseRatio = advances * 100 / total;
      referenceRatio = noChanges * 100 / total;
      decreaseRatio = declines * 100 / total;
    }
    return Column(
      children: [
        Row(children: [
          Expanded(
              flex: decreaseRatio.toInt(),
              child: buildText(data?.getMaxReduce() ?? '',
                  color: themeData.decreaseColor)),
          Expanded(
              flex: referenceRatio.toInt(),
              child: Padding(
                padding: const EdgeInsets.only(left: 6),
                child: buildText('0%',
                    color: themeData.referenceColor, textAlign: TextAlign.center),
              )),
          Expanded(
              flex: increaseRatio.toInt(),
              child: buildText(data?.getMaxIncrease() ?? '',
                  textAlign: TextAlign.end)),
        ]),
        const SizedBox(height: 6,),
        SizedBox(
            height: 4,
            width: (total != null && total != 0)
                ? (100 * total!).toDouble()
                : height,
            child: Row(
              children: [
                Flexible(
                    flex: decreaseRatio.toInt(),
                    child: Container(
                      decoration: BoxDecoration(
                          borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(2),
                              topRight: Radius.circular(2)),
                          color: themeData.redChart),
                    )),
                const SizedBox(height: 1),
                Flexible(
                    flex: referenceRatio.toInt(),
                    child: Container(
                      decoration: BoxDecoration(color: themeData.yellowChart),
                    )),
                const SizedBox(height: 1),
                Flexible(
                    flex: increaseRatio.toInt(),
                    child: Container(
                      decoration: BoxDecoration(
                          borderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(2),
                              bottomRight: Radius.circular(2)),
                          color: themeData.greenChart),
                    )),
              ],
            )),
        const SizedBox(height: 4,),
        Row(children: [
          buildText('${VPMarketInfoLocalize.current.stockReduce}: $totalReduce',
              color: themeData.decreaseColor),
          const Expanded(child: SizedBox.shrink()),
          buildText(
              '${VPMarketInfoLocalize.current.stockIncrease}: $totalIncrease'),
        ]),
      ],
    );
  }

  buildText(String text,
      {Color? color, TextAlign textAlign = TextAlign.start}) {
    return Text(text,
        style: vpTextStyle.captionMedium?.copyWith(color: color ?? themeData.increaseColor,
        fontSize: 13,),
        textAlign: textAlign);
  }
}
