import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';

TrackballBehavior trackballBehavior({
  required ChartTrackballBuilder<dynamic>? builder,
}) {
  return TrackballBehavior(
    builder: builder,
    enable: true,
    lineDashArray: const [5, 5],
    markerSettings: TrackballMarkerSettings(
      markerVisibility: TrackballVisibilityMode.visible,
      height: 10,
      width: 10,
      borderWidth: 5,
      shape: DataMarkerType.circle,
      color: themeData.white,
      borderColor: themeData.primary,
    ),
    lineWidth: 0.5,
    lineColor: themeData.gray500,
    tooltipDisplayMode: TrackballDisplayMode.groupAllPoints,
    activationMode: ActivationMode.singleTap,
    tooltipAlignment: ChartAlignment.near,
  );
}

List<BoxShadow> boxShadowTooltip() {
  return [
    BoxShadow(
      color: themeData.boxShadow.withOpacity(0.2),
      spreadRadius: 2,
      blurRadius: 2,
      offset: const Offset(0, 0.75),
    ),
  ];
}
