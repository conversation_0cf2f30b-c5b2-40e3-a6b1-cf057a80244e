import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_design_system/vp_design_system.dart';

class HomeStockLoading extends StatelessWidget {
  // ignore: prefer_const_constructors_in_immutables
  HomeStockLoading({
    Key? key,
    this.padding = const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
    this.count,
  }) : super(key: key);

  final EdgeInsets padding;
  final int? count;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      itemCount: count ?? 5,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder:
          (context, index) => Shimmer.fromColors(
            baseColor: themeData.skeletonBase,
            highlightColor: themeData.skeletonHighLight,
            child: Container(
              padding: padding,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: const [
                        HighLightLoading(width: 77, height: 20),
                        SizedBox(height: 4),
                        HighLightLoading(width: 164, height: 20),
                      ],
                    ),
                  ),
                  const HighLightLoading(width: 77, height: 44),
                ],
              ),
            ),
          ),
      separatorBuilder: (_, index) => Divider(color: themeData.gray100),
    );
  }
}
