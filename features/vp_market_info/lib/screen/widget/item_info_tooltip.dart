import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';

class ItemInfoTooltip extends StatelessWidget {
  const ItemInfoTooltip({
    Key? key,
    required this.name,
    this.value = '',
    this.colorText,
    this.colorCircle,
    this.isTooltip = true,
  }) : super(key: key);
  final String name;
  final String value;
  final Color? colorText;
  final Color? colorCircle;
  final bool isTooltip;

  @override
  Widget build(BuildContext context) =>
      isTooltip
          ? Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                Icons.circle,
                color: colorCircle ?? themeData.gray700,
                size: 6,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  name,
                  style: vpTextStyle.captionRegular?.copyWith(
                    color: themeData.gray700,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                value,
                style: vpTextStyle.captionMedium?.copyWith(
                  color: colorText ?? themeData.gray900,
                ),
              ),
            ],
          )
          : Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                Icons.circle,
                color: colorCircle ?? themeData.gray700,
                size: 6,
              ),
              const SizedBox(width: 8),
              Text(
                name,
                style: vpTextStyle.captionRegular?.copyWith(
                  color: themeData.gray700,
                ),
              ),
            ],
          );
}
