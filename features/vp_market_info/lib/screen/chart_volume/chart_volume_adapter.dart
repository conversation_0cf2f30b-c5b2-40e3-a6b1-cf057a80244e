import 'package:flutter/animation.dart';
import 'package:vp_common/extensions/list_extensions.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/core/const/market_info_constants.dart';
import 'package:vp_market/data/chart_volume_model.dart';
import 'dart:math';

import 'package:vp_market/screen/chart_volume/base_chart_adapter.dart';

class ChartVolumeAdapter extends BaseChartAdapter<ChartVolumeModel> {
  ChartVolumeAdapter(
    this._data,
    this.referencePrice, {
    this.ceilingPrice,
    this.floorPrice,
    this.typeData,
    required this.typeChart,
  });

  final double referencePrice;

  final double? ceilingPrice;

  final double? floorPrice;

  final List<ChartVolumeModel> _data;

  List<ChartVolumeModel> get chartData => _data;

  final String? typeData;

  final String typeChart;

  @override
  double get maxX => 0;

  @override
  double get maxY => 100;

  @override
  double get minX => 0;

  @override
  double get minY => 0;

  @override
  List<ChartVolumeModel> build() {
    return chartData;
  }

  double get minVolume =>
      min(chartData.map((e) => e.volume).toList().reduce(min), referencePrice);

  double get maxVolume =>
      max(chartData.map((e) => e.volume).toList().reduce(max), 0);

  double getY(ChartVolumeModel item) {
    if (item.volume <= 0) return 0;

    final percent = (item.volume / maxVolume) * 100;

    return percent <= 1 ? 1 : percent;
  }

  Color? getColor(ChartVolumeModel item) {
    final index = chartData.indexOf(item);
    if (typeChart == MarketInfoConstants.chartStock) {
      if (typeData == MarketInfoConstants.oneDay) {
        if (index == 0) {
          return referencePrice > item.price
              ? themeData.redChart
              : themeData.greenChart;
        } else {
          final preItem = chartData.getElementAt(index - 1);

          final prePrice = preItem?.price ?? referencePrice;

          return item.price >= prePrice
              ? themeData.greenChart
              : themeData.redChart;
        }
      } else {
        return item.open > item.price
            ? themeData.redChart
            : themeData.greenChart;
      }
    } else {
      final preItem = chartData.getElementAt(index - 1);

      final preVolume = preItem?.price ?? referencePrice;

      return item.price >= preVolume
          ? themeData.greenChart
          : themeData.redChart;
    }
  }
}
