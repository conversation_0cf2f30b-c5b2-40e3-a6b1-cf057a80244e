import 'package:equatable/equatable.dart';

abstract class ChartVolumeState extends Equatable {
  @override
  List<Object?> get props => [];
}

class ChartVolumeInitState extends ChartVolumeState {}

class ChartVolumeItemFocusChangedState extends ChartVolumeState {
  ChartVolumeItemFocusChangedState(this.index, {this.focused = false});

  final int? index;

  final bool focused;

  @override
  List<Object?> get props => [index, focused];
}