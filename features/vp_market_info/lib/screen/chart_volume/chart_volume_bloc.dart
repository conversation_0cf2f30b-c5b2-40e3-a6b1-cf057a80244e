import 'dart:ui';
import 'package:collection/collection.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_market/screen/chart_volume/chart_volume_state.dart';

class ChartVolumeBloc extends Cubit<ChartVolumeState> {
  ChartVolumeBloc() : super(ChartVolumeInitState());

  /// index: left
  final positions = <int, double>{};

  int? indexItemFocus;

  void showByIndex(int? index, {bool focused = false}) {
    if (indexItemFocus != index) {
      indexItemFocus = index;

      emit(ChartVolumeItemFocusChangedState(index, focused: focused));
    }
  }

  void hide({bool focused = false}) {
    indexItemFocus = null;
    emit(ChartVolumeItemFocusChangedState(indexItemFocus, focused: focused));
  }

  void checkItemFocus(Offset offset, double itemWidth) {
    final index = positions.keys.firstWhereOrNull((e) {
      final localOffset = offset.dx - 16;

      return positions[e] != null &&
          localOffset >= positions[e]! &&
          localOffset <= positions[e]! + itemWidth;
    });

    if (index != null) {
      showByIndex(index, focused: true);
    }
  }
}
