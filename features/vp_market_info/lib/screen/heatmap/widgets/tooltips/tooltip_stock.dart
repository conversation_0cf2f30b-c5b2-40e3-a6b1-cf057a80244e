import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_common/extensions/num_extensions.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/data/heatmap_stock_symbols_model.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/heatmap/bloc/heatmap_bloc.dart';
import 'package:vp_market/screen/heatmap/bloc/heatmap_state.dart';
import 'package:vp_market/screen/heatmap/widgets/tooltips/info_criteria_tooltip_widget.dart';
import 'package:vp_market/screen/widget/item_info_tooltip.dart';

class TooltipStock extends StatelessWidget {
  const TooltipStock({Key? key, required this.symbol}) : super(key: key);
  final String symbol;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HeatmapBloc, HeatmapState>(
      builder: (context, state) {
        final HeatMapStockBySymbolsModel model =
            state.listStock.where((e) => e.symbol == symbol).first;
        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${model.symbol ?? ''} - ${model.fullName ?? ''}',
              style: vpTextStyle.captionMedium?.copyWith(color: themeData.gray900),
            ),
            const SizedBox(height: 4,),
            ItemInfoTooltip(
              name: VPMarketInfoLocalize.current.pricePrice,
              value:
                  '${model.closePrice != null ? model.closePrice!.getPriceFormatted(convertToThousand: true) : '--'} (${model.stockPercentChange != null ? model.stockPercentChange!.getChangePercentDisplay() : '--%'})',
              colorText: model.colorByPrice,
            ),
            const SizedBox(height: 4,),
            ItemInfoTooltip(
              name: VPMarketInfoLocalize.current.highestPrice,
              value: model.high != null
                  ? model.high!.getPriceFormatted(convertToThousand: true)
                  : '--',
              colorText: model.colorLowHigh(model.high),
            ),
            const SizedBox(height: 4,),
            ItemInfoTooltip(
              name: VPMarketInfoLocalize.current.lowestPrice,
              value: model.low != null
                  ? model.low!.getPriceFormatted(convertToThousand: true)
                  : '--',
              colorText: model.colorLowHigh(model.low),
            ),
            const SizedBox(height: 4,),
            InfoCriteriaTooltipWidget(
              criteria: state.criteria.id,
              marketCap: model.marketCap,
              totalTradingValue: model.totalTradingValue,
              foreignNetBuyValue: model.foreignNetBuyValue,
              foreignNetSellValue: model.foreignNetSellValue,
              foreignBuyValue: model.foreignBuyValue,
              foreignSellValue: model.foreignSellValue,
            ),
          ],
        );
      },
    );
  }
}
