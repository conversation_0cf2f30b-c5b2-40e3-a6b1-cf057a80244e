import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_common/extensions/num_extensions.dart';
import 'package:vp_market/data/heatmap_sector_symbols_model.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/heatmap/bloc/heatmap_bloc.dart';
import 'package:vp_market/screen/heatmap/bloc/heatmap_state.dart';
import 'package:vp_market/screen/heatmap/widgets/tooltips/info_criteria_tooltip_widget.dart';
import 'package:vp_market/screen/widget/item_info_tooltip.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class TooltipIndustry extends StatelessWidget {
  const TooltipIndustry({Key? key, required this.model}) : super(key: key);
  final HeatMapSectorBySymbolsModel model;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HeatmapBloc, HeatmapState>(
      builder: (context, state) {
        num? percentChangeIcbLevel2 = context
            .read<HeatmapBloc>()
            .percentChangeIcbLevel2(model.icbCodeLevel2);
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ItemInfoTooltip(
              name: VPMarketInfoLocalize.current.branch,
              value: model.icbNameLevel2 ?? '',
            ),
            const SizedBox(height: 4),
            ItemInfoTooltip(
              name:
                  '% ${VPMarketInfoLocalize.current.fluctuations.toLowerCase()}',
              value:
                  percentChangeIcbLevel2 != null
                      ? percentChangeIcbLevel2.getChangePercentDisplay(
                        addCharacter: true,
                        fractionDigits: 2,
                      )
                      : '--',
              colorText: CommonColorUtils.colorValue(percentChangeIcbLevel2),
            ),
            const SizedBox(height: 4),
            InfoCriteriaTooltipWidget(
              criteria: state.criteria.id,
              marketCap: model.marketCap,
              totalTradingValue: model.totalTradingValue,
              foreignNetBuyValue: model.foreignNetBuyValue,
              foreignNetSellValue: model.foreignNetSellValue,
              foreignBuyValue: model.foreignBuyValue,
              foreignSellValue: model.foreignSellValue,
            ),
          ],
        );
      },
    );
  }
}
