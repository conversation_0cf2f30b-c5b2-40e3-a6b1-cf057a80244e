import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:vp_common/utils/app_number_format_utils.dart';
import 'package:vp_market/common/extension/num_extensions.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/heatmap/bloc/heatmap_bloc.dart';
import '../../../widget/item_info_tooltip.dart';

class InfoCriteriaTooltipWidget extends StatelessWidget {
  const InfoCriteriaTooltipWidget({
    Key? key,
    required this.criteria,
    this.marketCap,
    this.totalTradingValue,
    this.foreignNetBuyValue,
    this.foreignNetSellValue,
    this.foreignBuyValue,
    this.foreignSellValue,
  }) : super(key: key);
  final CriteriaEnum criteria;
  final num? marketCap;
  final num? totalTradingValue;
  final num? foreignNetBuyValue;
  final num? foreignNetSellValue;
  final num? foreignBuyValue;
  final num? foreignSellValue;

  @override
  Widget build(BuildContext context) {
    NumberFormat numberFormat = AppNumberFormatUtils.shared.percentFormatter;
    return criteria == CriteriaEnum.marketCap
        ? ItemInfoTooltip(
          name: VPMarketInfoLocalize.current.capitalization,
          value:
              marketCap != null
                  ? marketCap!.toVolDisplay(numberFormat: numberFormat)
                  : '--',
        )
        : (criteria == CriteriaEnum.totalTradingValue
            ? ItemInfoTooltip(
              name: VPMarketInfoLocalize.current.totalTradingValue2,
              value:
                  totalTradingValue != null
                      ? totalTradingValue!.toVolDisplay(
                        numberFormat: numberFormat,
                      )
                      : '--',
            )
            : Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                criteria == CriteriaEnum.foreignNetBuyValue
                    ? ItemInfoTooltip(
                      name: VPMarketInfoLocalize.current.foreignNetBuyValue,
                      value:
                          foreignNetBuyValue != null
                              ? foreignNetBuyValue!.toVolDisplay(
                                numberFormat: numberFormat,
                              )
                              : '--',
                    )
                    : ItemInfoTooltip(
                      name: VPMarketInfoLocalize.current.foreignNetSellValue,
                      value:
                          foreignNetSellValue != null
                              ? foreignNetSellValue!.toVolDisplay(
                                numberFormat: numberFormat,
                              )
                              : '--',
                    ),
                const SizedBox(height: 4),
                ItemInfoTooltip(
                  name: VPMarketInfoLocalize.current.foreignBuyValue,
                  value:
                      foreignBuyValue != null
                          ? foreignBuyValue!.toVolDisplay(
                            numberFormat: numberFormat,
                          )
                          : '--',
                ),
                const SizedBox(height: 4),
                ItemInfoTooltip(
                  name: VPMarketInfoLocalize.current.foreignSellValue,
                  value:
                      foreignSellValue != null
                          ? foreignSellValue!.toVolDisplay(
                            numberFormat: numberFormat,
                          )
                          : '--',
                ),
              ],
            ));
  }
}
