import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/custom_widget/none_widget.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/data/heatmap_sector_symbols_model.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/heatmap/bloc/heatmap_bloc.dart';
import 'package:vp_market/screen/heatmap/bloc/heatmap_state.dart';
import 'package:syncfusion_flutter_treemap/treemap.dart';
import 'package:vp_market/screen/heatmap/widgets/tooltips/tooltip_industry.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class HeatmapSector extends StatefulWidget {
  const HeatmapSector({Key? key}) : super(key: key);

  @override
  State<HeatmapSector> createState() => _HeatmapSectorState();
}

class _HeatmapSectorState extends State<HeatmapSector> {
  List<HeatMapSectorBySymbolsModel> listSector = [];
  num total = 0;

  void setData(List<HeatMapSectorBySymbolsModel> list, CriteriaEnum criteria) {
    listSector.clear();
    total = 0;
    switch (criteria) {
      case CriteriaEnum.marketCap:
        for (var e in list) {
          total += e.marketCap ?? 0;
        }
        listSector.addAll(list.where((e) => (e.marketCap ?? 0) != 0));
        break;
      case CriteriaEnum.totalTradingValue:
        for (var e in list) {
          total += e.totalTradingValue ?? 0;
        }
        listSector.addAll(list.where((e) => (e.totalTradingValue ?? 0) != 0));
        break;
      case CriteriaEnum.foreignNetBuyValue:
        for (var e in list) {
          total += e.foreignNetBuyValue ?? 0;
        }
        listSector.addAll(list.where((e) => (e.foreignNetBuyValue ?? 0) != 0));
        break;
      case CriteriaEnum.foreignNetSellValue:
        for (var e in list) {
          total += e.foreignNetSellValue ?? 0;
        }
        listSector.addAll(list.where((e) => (e.foreignNetSellValue ?? 0) != 0));
        break;
    }
  }

  HeatMapSectorBySymbolsModel getModelByTile(
    String symbol,
    List<HeatMapSectorBySymbolsModel> listSector,
  ) {
    final HeatMapSectorBySymbolsModel value =
        listSector.where((e) => e.icbNameLevel2 == symbol).first;
    return value;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HeatmapBloc, HeatmapState>(
      builder: (context, state) {
        setData(state.listSector, state.criteria.id);
        return total != 0
            ? SfTreemap(
              dataCount: listSector.length,
              weightValueMapper: (int index) {
                switch (state.criteria.id) {
                  case CriteriaEnum.marketCap:
                    return ((listSector[index].marketCap ?? 0) / total) * 100;
                  case CriteriaEnum.totalTradingValue:
                    return ((listSector[index].totalTradingValue ?? 0) /
                            total) *
                        100;
                  case CriteriaEnum.foreignNetBuyValue:
                    return ((listSector[index].foreignNetBuyValue ?? 0) /
                            total) *
                        100;
                  case CriteriaEnum.foreignNetSellValue:
                    return ((listSector[index].foreignNetSellValue ?? 0) /
                            total) *
                        100;
                  default:
                    return 0;
                }
              },
              tooltipSettings: TreemapTooltipSettings(
                color: themeData.white,
                hideDelay: 5.0,
              ),
              levels: <TreemapLevel>[
                TreemapLevel(
                  groupMapper: (int index) {
                    return listSector[index].icbNameLevel2;
                  },
                  labelBuilder: (BuildContext context, TreemapTile tile) {
                    num? percentChangeIcbLevel2 = context
                        .read<HeatmapBloc>()
                        .percentChangeIcbLevel2(
                          getModelByTile(tile.group, listSector).icbCodeLevel2,
                        );
                    return Container(
                      color: CommonColorUtils.chartColorValue(
                        percentChangeIcbLevel2,
                      ),
                      child: Center(
                        child: Visibility(
                          visible: !(tile.weight < 3 && listSector.length == 2),
                          child: Text(
                            '${tile.group} ${percentChangeIcbLevel2 != null ? '($percentChangeIcbLevel2%)' : ''}',
                            style: vpTextStyle.subtitle14?.copyWith(
                              color: themeData.white,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    );
                  },
                  tooltipBuilder: (BuildContext context, TreemapTile tile) {
                    return Container(
                      width: MediaQuery.of(context).size.width * 2 / 3,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: TooltipIndustry(
                        model: getModelByTile(tile.group, listSector),
                      ),
                    );
                  },
                ),
              ],
            )
            : NoneWidget(
              padding: const EdgeInsets.only(top: 24),
              desc: VPMarketInfoLocalize.current.noData,
            );
      },
    );
  }
}
