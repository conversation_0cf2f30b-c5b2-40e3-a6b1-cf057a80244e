import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/data/icb_level_model.dart';
import 'package:vp_market/generated/l10n.dart';

Future<IcbLevelModel?> showBusinessBottomSheet(
  BuildContext context,
  List<IcbLevelModel> list,
) async {
  return await showModalBottomSheet(
    barrierColor: themeData.overlayBottomSheet,
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) {
      return SafeArea(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 24),
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Flexible(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: themeData.bgPopup,
                  ),
                  padding: const EdgeInsets.only(top: 4),
                  child: Scrollbar(
                    interactive: true,
                    thumbVisibility: true,
                    radius: const Radius.circular(5),
                    child: ListView.builder(
                      itemCount: list.length,
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        return BottomSheetItemWidget(
                          text: list[index].icbName ?? '',
                          onTap: () {
                            Navigator.pop(context, list[index]);
                          },
                          border: index != 0,
                        );
                      },
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              ButtonBottomSheet(
                text: VPMarketInfoLocalize.current.cancel,
                onTap: () => Navigator.pop(context),
              ),
            ],
          ),
        ),
      );
    },
  );
}
