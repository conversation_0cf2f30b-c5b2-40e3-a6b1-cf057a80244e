import 'package:vp_common/vp_common.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/core/const/market_info_constants.dart';
import 'package:vp_market/data/heatmap_industry_index_model.dart';
import 'package:vp_market/data/heatmap_sector_symbols_model.dart';
import 'package:vp_market/data/heatmap_stock_symbols_model.dart';
import 'package:vp_market/data/icb_level_model.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/heatmap/bloc/heatmap_bloc.dart';

class HeatmapState {
  final List<IcbLevelModel> icbLevels;
  final IcbLevelModel icbLevelModel;
  final ItemSelect criteria;
  final ItemSelect floor;
  final StatusEnum status;
  final List<HeatMapSectorBySymbolsModel> listSector;
  final List<HeatMapStockBySymbolsModel> listStock;
  final List<HeatMapIndustryByIndexModel> listIndustry;

  HeatmapState({
    List<IcbLevelModel>? icbLevels,
    IcbLevelModel? icbLevelModel,
    ItemSelect? criteria,
    ItemSelect? floor,
    this.status = StatusEnum.loading,
    List<HeatMapSectorBySymbolsModel>? listSector,
    List<HeatMapStockBySymbolsModel>? listStock,
    List<HeatMapIndustryByIndexModel>? listIndustry,
  }) : icbLevels = icbLevels ?? [],
       icbLevelModel =
           icbLevelModel ??
           IcbLevelModel(
             icbCode: MarketInfoConstants.all,
             icbName: VPMarketInfoLocalize.current.all,
           ),
       criteria =
           criteria ??
           ItemSelect(
             id: CriteriaEnum.marketCap,
             title: VPMarketInfoLocalize.current.capitalization,
             value: CriteriaEnum.marketCap.name,
           ),
       floor =
           floor ??
           ItemSelect(
             id: MarketInfoConstants.all,
             title: VPMarketInfoLocalize.current.all,
           ),
       listSector = listSector ?? [],
       listStock = listStock ?? [],
       listIndustry = listIndustry ?? [];

  HeatmapState copyWith({
    List<IcbLevelModel>? icbLevels,
    IcbLevelModel? icbLevelModel,
    ItemSelect? criteria,
    ItemSelect? floor,
    StatusEnum? status,
    List<HeatMapSectorBySymbolsModel>? listSector,
    List<HeatMapStockBySymbolsModel>? listStock,
    List<HeatMapIndustryByIndexModel>? listIndustry,
  }) {
    return HeatmapState(
      icbLevels: icbLevels ?? this.icbLevels,
      icbLevelModel: icbLevelModel ?? this.icbLevelModel,
      criteria: criteria ?? this.criteria,
      floor: floor ?? this.floor,
      status: status ?? this.status,
      listSector: listSector ?? this.listSector,
      listStock: listStock ?? this.listStock,
      listIndustry: listIndustry ?? this.listIndustry,
    );
  }
}
