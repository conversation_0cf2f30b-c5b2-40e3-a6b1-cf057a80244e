import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/data/supply_demand_model.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/widget/item_info_tooltip.dart';
import 'package:vp_market/screen/widget/trackball_behavior.dart';

class SupplyDemandTrackBallView extends StatelessWidget {
  final SupplyDemandModel model;

  const SupplyDemandTrackBallView({Key? key, required this.model})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width * 2 / 3,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: themeData.gray300),
        boxShadow: boxShadowTooltip(),
        color: themeData.white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppTimeUtils.formatHour(
              AppTimeUtils.format(model.dateTime, AppTimeUtilsFormat.time),
            ),
            style: vpTextStyle.captionMedium?.copyWith(
              color: themeData.gray900,
            ),
          ),
          const SizedBox(height: 4),
          ItemInfoTooltip(
            name: VPMarketInfoLocalize.current.buyProactively,
            value:
                '${model.rateSumBuyUpValue != null ? model.rateSumBuyUpValue!.toStringAsFixed(2) : '--'}%',
            colorText: themeData.greenChart,
          ),
          const SizedBox(height: 4),
          ItemInfoTooltip(
            name: VPMarketInfoLocalize.current.sellProactively,
            value:
                '${model.rateSumSellDownValue != null ? model.rateSumSellDownValue!.toStringAsFixed(2) : '--'}%',
            colorText: themeData.red,
          ),
          const SizedBox(height: 4),
          ItemInfoTooltip(
            name: VPMarketInfoLocalize.current.proactiveRate,
            value:
                model.buyUpOnSellDown != null
                    ? model.buyUpOnSellDown!.toStringAsFixed(2)
                    : '--',
          ),
        ],
      ),
    );
  }
}
