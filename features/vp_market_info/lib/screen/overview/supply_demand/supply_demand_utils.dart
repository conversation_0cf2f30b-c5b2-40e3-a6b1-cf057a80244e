import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/data/supply_demand_model.dart';

class SupplyDemandUtils {
  ///pt đường thẳng y=ax+b
  ///a=(y2-y1)/(x2-x1)
  ///b=y1-ax1
  /// tìm (x,0) => x=-b/a
  num findX(ChartData A, ChartData B) {
    num a = (B.y - A.y) / (B.x - A.x);
    num b = A.y - a * A.x;
    num x = -b / a;
    return x;
  }

  bool getTypeChart(SupplyDemandModel model) {
    return (model.dateTime.hour == 9 && model.dateTime.minute == 15);
  }

  List<ChartData> convertChartData(List<SupplyDemandModel> list) {
    List<ChartData> charts = [];
    if (list.isNotEmpty) {
      int index = getTypeChart(list[0]) ? 3 : 0;
      for (int i = 0; i < list.length; i++) {
        ChartData chart = ChartData(x: i + index, y: list[i].yValue.toDouble());
        charts.add(chart);
      }
    }
    charts = updateColorChart(insertChart(charts));
    return charts;
  }

  ///thêm điểm y=0 giữa 2 điểm có gt y trái dấu để chia color
  List<ChartData> insertChart(List<ChartData> charts) {
    for (int i = 0; i < charts.length - 1; i++) {
      if (charts[i].y * charts[i + 1].y < 0) {
        num x = findX(charts[i], charts[i + 1]);
        ChartData element = ChartData(x: x, y: 0);
        charts.insert(i + 1, element);
      }
    }
    return charts;
  }

  List<ChartData> updateColorChart(List<ChartData> charts) {
    if (charts.isNotEmpty) {
      if (charts.length > 1) {
        for (int i = 0; i < charts.length - 1; i++) {
          if (charts[i].y >= 0 && charts[i + 1].y >= 0) {
            charts[i].color = themeData.greenChart;
          } else if (charts[i].y >= 0 && charts[i + 1].y < 0) {
            charts[i].color = themeData.redChart;
          } else if (charts[i].y < 0 && charts[i + 1].y > 0) {
            charts[i].color = themeData.greenChart;
          } else if (charts[i].y < 0 && charts[i + 1].y <= 0) {
            charts[i].color = themeData.redChart;
          }
        }
      } else {
        charts[0].color =
            charts[0].y >= 0 ? themeData.greenChart : themeData.redChart;
      }
    }
    return charts;
  }
}

class ChartData {
  ChartData({required this.x, required this.y, this.color});

  final num x;
  final num y;
  Color? color;
}
