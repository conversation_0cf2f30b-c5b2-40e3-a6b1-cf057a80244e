import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/data/chart_buy_up_sell_entity.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/overview/transaction_classification/transaction_classification_bloc.dart';
import 'package:vp_market/screen/overview/transaction_classification/transaction_classification_state.dart';
import 'package:vp_market/screen/overview/transaction_classification/transaction_tooltip.dart';
import 'package:vp_market/screen/widget/axis_y_chart_widget.dart';
import 'package:vp_market/screen/widget/title_axis_y_chart_widget.dart';

import 'package:vp_market/screen/widget/trackball_behavior.dart';

class TransactionClassificationChart extends StatefulWidget {
  const TransactionClassificationChart({Key? key}) : super(key: key);

  @override
  State<TransactionClassificationChart> createState() =>
      _TransactionClassificationChartState();
}

class _TransactionClassificationChartState
    extends State<TransactionClassificationChart> {
  @override
  void initState() {
    super.initState();
  }

  TrackballBehavior initTrackBall(ChartBuyUpSellType? typeTransaction) {
    return trackballBehavior(
      builder: (BuildContext context, TrackballDetails trackballDetails) {
        String _xValue = '';
        num _sBuy = 0;
        num _sSell = 0;
        num _mBuy = 0;
        num _mSell = 0;
        num _lBuy = 0;
        num _lSell = 0;
        try {
          if (trackballDetails.groupingModeInfo != null) {
            _xValue =
                trackballDetails.groupingModeInfo?.points[0].x.toString() ?? '';
            num y0 = trackballDetails.groupingModeInfo?.points[0].y ?? 0;
            num y1 = trackballDetails.groupingModeInfo?.points[1].y ?? 0;

            switch (typeTransaction) {
              case ChartBuyUpSellType.small:
                _sBuy = y0;
                _sSell = y1;
                break;
              case ChartBuyUpSellType.medium:
                _mBuy = y0;
                _mSell = y1;
                break;
              case ChartBuyUpSellType.long:
                _lBuy = y0;
                _lSell = y1;
                break;
              default:
                _sBuy = y0;
                _sSell = y1;
                _mBuy = trackballDetails.groupingModeInfo?.points[2].y ?? 0;
                _mSell = trackballDetails.groupingModeInfo?.points[3].y ?? 0;
                _lBuy = trackballDetails.groupingModeInfo?.points[4].y ?? 0;
                _lSell = trackballDetails.groupingModeInfo?.points[5].y ?? 0;
                break;
            }
          }
        } catch (e) {
          dlog(e);
        }

        return TransactionTooltip(
          typeTransaction: typeTransaction,
          xValue: _xValue,
          sBuy: _sBuy,
          sSell: _sSell,
          mBuy: _mBuy,
          mSell: _mSell,
          lBuy: _lBuy,
          lSell: _lSell,
        );
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double height = 280;
    final buyTitle = '${VPMarketInfoLocalize.current.buy} CĐ';
    final sellTitle = '${VPMarketInfoLocalize.current.sell} CĐ';
    return BlocBuilder<
      TransactionClassificationBloc,
      TransactionClassificationState
    >(
      builder: (context, state) {
        double maxMinValue = state.getMaxMin().toDouble();
        double maxValue = maxMinValue.isZero() ? 1 : maxMinValue;
        double minValue = maxMinValue.isZero() ? 1 : (maxMinValue * -1);
        TrackballBehavior _trackballBehavior = initTrackBall(
          state.typeTransaction.id,
        );
        final ChartBuyUpSellEntity obj = state.chartEntity;
        List<CartesianSeries> cartesianSeries = [];
        switch (state.typeTransaction.id) {
          case ChartBuyUpSellType.small:
            cartesianSeries = [
              splineAreaSeriesBuy(obj.sChartDataBuy),
              splineAreaSeriesSell(obj.sChartDataSell),
            ];
            break;
          case ChartBuyUpSellType.medium:
            cartesianSeries = [
              splineAreaSeriesBuy(obj.mChartDataBuy),
              splineAreaSeriesSell(obj.mChartDataSell),
            ];
            break;
          case ChartBuyUpSellType.long:
            cartesianSeries = [
              splineAreaSeriesBuy(obj.lChartDataBuy),
              splineAreaSeriesSell(obj.lChartDataSell),
            ];
            break;
          default:
            cartesianSeries = [
              splineAreaSeriesBuy(obj.sChartDataBuy),
              splineAreaSeriesSell(obj.sChartDataSell),
              splineAreaSeriesBuy(obj.mChartDataBuy),
              splineAreaSeriesSell(obj.mChartDataSell),
              splineAreaSeriesBuy(obj.lChartDataBuy),
              splineAreaSeriesSell(obj.lChartDataSell),
            ];
            break;
        }
        return SizedBox(
          height: height,
          child: Row(
            children: [
              TitleAxisYChartWidget(buyTitle: buyTitle, sellTitle: sellTitle),
              const SizedBox(width: 4),
              Expanded(
                child: Stack(
                  children: [
                    AxisYChartWidget(height: height),
                    Center(child: Divider3Widget(color: themeData.gray300)),
                    SfCartesianChart(
                      trackballBehavior: _trackballBehavior,
                      margin: const EdgeInsets.only(left: 2),
                      series: cartesianSeries,
                      primaryXAxis: const CategoryAxis(
                        labelPlacement: LabelPlacement.onTicks,
                        isVisible: false,
                      ),
                      primaryYAxis: NumericAxis(
                        isVisible: false,
                        minimum: minValue,
                        maximum: maxValue,
                        plotBands: <PlotBand>[
                          PlotBand(
                            isVisible: true,
                            start: 0,
                            end: 0,
                            borderWidth: 1,
                            borderColor: themeData.gray300,
                          ),
                        ],
                      ),
                      plotAreaBorderWidth: 0,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  SplineAreaSeries splineAreaSeriesBuy(List<ChartBuyUpSellData?> charts) {
    return SplineAreaSeries<ChartBuyUpSellData?, String>(
      splineType: SplineType.cardinal,
      dataSource: charts,
      xValueMapper: (ChartBuyUpSellData? data, _) => data?.x,
      yValueMapper: (ChartBuyUpSellData? data, _) => data?.y,
      color: themeData.backgroundPrimary,
      borderColor: themeData.primary,
      borderWidth: 1,
      animationDuration: 0,
    );
  }

  SplineAreaSeries splineAreaSeriesSell(List<ChartBuyUpSellData?> charts) {
    return SplineAreaSeries<ChartBuyUpSellData?, String>(
      splineType: SplineType.cardinal,
      dataSource: charts,
      xValueMapper: (ChartBuyUpSellData? data, _) => data?.x,
      yValueMapper: (ChartBuyUpSellData? data, _) => data?.y,
      color: themeData.redChart.withOpacity(0.16),
      borderColor: themeData.redChart,
      borderWidth: 1,
      animationDuration: 0,
    );
  }
}
