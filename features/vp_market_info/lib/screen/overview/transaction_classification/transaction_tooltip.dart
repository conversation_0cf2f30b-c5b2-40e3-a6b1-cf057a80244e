import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/data/chart_buy_up_sell_entity.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/widget/item_info_tooltip.dart';
import 'package:vp_market/screen/widget/trackball_behavior.dart';

class TransactionTooltip extends StatelessWidget {
  const TransactionTooltip({
    Key? key,
    required this.typeTransaction,
    required this.xValue,
    required this.sBuy,
    required this.sSell,
    required this.mBuy,
    required this.mSell,
    required this.lBuy,
    required this.lSell,
  }) : super(key: key);
  final ChartBuyUpSellType? typeTransaction;
  final String xValue;
  final num sBuy, sSell, mBuy, mSell, lBuy, lSell;

  @override
  Widget build(BuildContext context) {
    final time = AppTimeUtils.formatHour(
      AppTimeUtils.timeFromTimeStamp(
        int.tryParse(xValue) ?? 0,
        format: AppTimeUtilsFormat.time,
      ),
    );
    final bool showS =
        typeTransaction == null || typeTransaction == ChartBuyUpSellType.small;
    final bool showM =
        typeTransaction == null || typeTransaction == ChartBuyUpSellType.medium;
    final bool showL =
        typeTransaction == null || typeTransaction == ChartBuyUpSellType.long;
    return Container(
      width: MediaQuery.of(context).size.width * 2 / 3,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: themeData.gray300),
        boxShadow: boxShadowTooltip(),
        color: themeData.white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            time,
            style: vpTextStyle.captionMedium?.copyWith(
              color: themeData.gray900,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            VPMarketInfoLocalize.current.buyProactively,
            style: vpTextStyle.captionMedium?.copyWith(
              color: themeData.gray900,
            ),
          ),
          Visibility(
            visible: showS,
            child: Padding(
              padding: const EdgeInsets.only(top: 4),
              child: ItemInfoTooltip(
                name: VPMarketInfoLocalize.current.smallOrder,
                value: sBuy.toMoneyTooltip(),
                colorCircle: themeData.primary,
              ),
            ),
          ),
          Visibility(
            visible: showM,
            child: Padding(
              padding: const EdgeInsets.only(top: 4),
              child: ItemInfoTooltip(
                name: VPMarketInfoLocalize.current.mediumOrder,
                value: mBuy.toMoneyTooltip(),
                colorCircle: themeData.primary,
              ),
            ),
          ),
          Visibility(
            visible: showL,
            child: Padding(
              padding: const EdgeInsets.only(top: 4),
              child: ItemInfoTooltip(
                name: VPMarketInfoLocalize.current.largeOrder,
                value: lBuy.toMoneyTooltip(),
                colorCircle: themeData.primary,
              ),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            VPMarketInfoLocalize.current.sellProactively,
            style: vpTextStyle.captionMedium?.copyWith(
              color: themeData.gray900,
            ),
          ),
          const SizedBox(height: 4),
          Visibility(
            visible: showS,
            child: Padding(
              padding: const EdgeInsets.only(top: 4),
              child: ItemInfoTooltip(
                name: VPMarketInfoLocalize.current.smallOrder,
                value: sSell.toMoneyTooltip(),
                colorCircle: themeData.red,
              ),
            ),
          ),
          Visibility(
            visible: showM,
            child: Padding(
              padding: const EdgeInsets.only(top: 4),
              child: ItemInfoTooltip(
                name: VPMarketInfoLocalize.current.mediumOrder,
                value: mSell.toMoneyTooltip(),
                colorCircle: themeData.red,
              ),
            ),
          ),
          Visibility(
            visible: showL,
            child: Padding(
              padding: const EdgeInsets.only(top: 4),
              child: ItemInfoTooltip(
                name: VPMarketInfoLocalize.current.largeOrder,
                value: lSell.toMoneyTooltip(),
                colorCircle: themeData.red,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
