import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_market/screen/overview/liquidity/market_liquidity_bloc.dart';
import 'package:vp_market/screen/overview/liquidity/market_liquidity_state.dart';

class LegendLiquidityWidget extends StatelessWidget {
  const LegendLiquidityWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ItemLegendLiquidity(
            name: VPMarketInfoLocalize.current.today,
            color: themeData.greenChart,
          ),
          const SizedBox(width: 12),
          ItemLegendLiquidity(
            name: VPMarketInfoLocalize.current.yesterday,
            color: themeData.redChart,
          ),
          const SizedBox(width: 12),
          BlocBuilder<MarketLiquidityBloc, MarketLiquidityState>(
            builder:
                (context, state) => ItemLegendLiquidity(
                  name:
                      '${VPMarketInfoLocalize.current.average} ${state.typeSession.value} ${VPMarketInfoLocalize.current.session}',
                  color: themeData.yellowChart,
                ),
          ),
        ],
      ),
    );
  }
}

class ItemLegendLiquidity extends StatelessWidget {
  const ItemLegendLiquidity({Key? key, required this.name, required this.color})
    : super(key: key);
  final String name;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 2),
          child: Icon(Icons.square, size: 10, color: color),
        ),
        const SizedBox(width: 4),
        Text(
          name,
          style: vpTextStyle.captionRegular?.copyWith(color: themeData.gray700),
        ),
      ],
    );
  }
}
