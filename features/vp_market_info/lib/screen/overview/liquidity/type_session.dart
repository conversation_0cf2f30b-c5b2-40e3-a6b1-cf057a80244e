import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/generated/l10n.dart';

enum TypeSessionEnum {
  nowSession,
  preSessions,
  average5sessions,
  average10sessions,
  average20sessions,
  average30sessions,
  average50sessions,
}

List<ItemSelect> listTypeSession = [
  ItemSelect(
    id: TypeSessionEnum.average5sessions.name,
    title: '5 ${VPMarketInfoLocalize.current.session}',
    value: 5,
  ),
  ItemSelect(
    id: TypeSessionEnum.average10sessions.name,
    title: '10 ${VPMarketInfoLocalize.current.session}',
    value: 10,
  ),
  ItemSelect(
    id: TypeSessionEnum.average20sessions.name,
    title: '20 ${VPMarketInfoLocalize.current.session}',
    value: 20,
  ),
  ItemSelect(
    id: TypeSessionEnum.average30sessions.name,
    title: '30 ${VPMarketInfoLocalize.current.session}',
    value: 30,
  ),
  ItemSelect(
    id: TypeSessionEnum.average50sessions.name,
    title: '50 ${VPMarketInfoLocalize.current.session}',
    value: 50,
  ),
];
