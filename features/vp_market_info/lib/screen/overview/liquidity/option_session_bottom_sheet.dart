import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_market/generated/l10n.dart';

Future<ItemSelect?> showOptionSessionBottomSheet(
    BuildContext context, List<ItemSelect> list) async {
  return await showModalBottomSheet(
    barrierColor: themeData.overlayBottomSheet,
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) {
      return SafeArea(
        child: Container(
          padding: const EdgeInsets.symmetric(
              horizontal: 8, vertical: 24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: themeData.bgPopup),
                padding: const EdgeInsets.only(top: 4),
                child: ListView.builder(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    itemCount: list.length,
                    itemBuilder: (_, index) => BottomSheetItemWidget(
                          text: list[index].title,
                          onTap: () {
                            Navigator.pop(context, list[index]);
                          },
                          border: index != 0,
                        )),
              ),
              const SizedBox(
                height: 8,
              ),
              ButtonBottomSheet(
                  text: VPMarketInfoLocalize.current.cancel,
                  onTap: () => Navigator.pop(context)),
            ],
          ),
        ),
      );
    },
  );
}
