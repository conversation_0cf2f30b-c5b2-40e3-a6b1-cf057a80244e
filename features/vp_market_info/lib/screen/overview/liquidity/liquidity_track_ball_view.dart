import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_common/utils/app_number_format_utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/common/extension/num_extensions.dart';
import 'package:vp_market/screen/overview/liquidity/market_liquidity_bloc.dart';
import 'package:vp_market/screen/overview/liquidity/market_liquidity_state.dart';
import 'package:vp_market/screen/widget/item_info_tooltip.dart';
import 'package:vp_market/screen/widget/trackball_behavior.dart';
import 'package:vp_market/generated/l10n.dart';
import 'package:vp_common/extensions/date_extensions.dart';

class LiquidityTrackBallView extends StatelessWidget {
  final DateTime? time;

  const LiquidityTrackBallView({Key? key, required this.time})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width * 2 / 3,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: themeData.gray300),
        boxShadow: boxShadowTooltip(),
        color: themeData.white,
      ),
      child: BlocBuilder<MarketLiquidityBloc, MarketLiquidityState>(
        builder: (context, state) {
          num today =
              (state.listToday
                      .where(
                        (e) =>
                            e.dateTime != null
                                ? e.dateTime!.isSameTime(time)
                                : false,
                      )
                      .firstOrNull)
                  ?.totalValue ??
              0;
          num yesterday =
              (state.listYesterday
                      .where(
                        (e) =>
                            e.dateTime != null
                                ? e.dateTime!.isSameTime(time)
                                : false,
                      )
                      .firstOrNull)
                  ?.totalValue ??
              0;
          num average =
              (state.listSession
                      .where(
                        (e) =>
                            e.dateTime != null
                                ? e.dateTime!.isSameTime(time)
                                : false,
                      )
                      .firstOrNull)
                  ?.totalValue ??
              0;
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppTimeUtils.format(time, AppTimeUtilsFormat.dateWithTime),
                style: vpTextStyle.captionMedium?.copyWith(
                  color: themeData.gray900,
                ),
              ),
              const SizedBox(height: 4),
              ItemInfoTooltip(
                name: VPMarketInfoLocalize.current.today,
                value: getValue(today),
                colorCircle: themeData.greenChart,
              ),
              const SizedBox(height: 4),
              ItemInfoTooltip(
                name: VPMarketInfoLocalize.current.yesterday,
                value: getValue(yesterday),
                colorCircle: themeData.redChart,
              ),
              const SizedBox(height: 4),
              ItemInfoTooltip(
                name:
                    '${VPMarketInfoLocalize.current.average2} ${state.typeSession.value} ${VPMarketInfoLocalize.current.session}',
                value: getValue(average),
                colorCircle: themeData.yellowChart,
              ),
            ],
          );
        },
      ),
    );
  }

  String getValue(num value) {
    final numberFormat = AppNumberFormatUtils.shared.percentFormatter;
    if (value == 0) {
      return '0 ${VPMarketInfoLocalize.current.bil}';
    } else {
      return value.toVolDisplay(numberFormat: numberFormat);
    }
  }
}
