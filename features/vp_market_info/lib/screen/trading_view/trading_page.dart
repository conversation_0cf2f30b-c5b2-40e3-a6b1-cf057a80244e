import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_market/screen/trading_view/trading_view.dart';

class TradingPage extends StatefulWidget {
  const TradingPage({Key? key, this.tradingViewUrlFull}) : super(key: key);
  final String? tradingViewUrlFull;

  @override
  State<TradingPage> createState() => _TradingPageState();
}

class _TradingPageState extends State<TradingPage> {
  bool _handleBackPress() {
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    return true;
  }

  @override
  void initState() {
    super.initState();
    SystemChrome.setPreferredOrientations(
      [DeviceOrientation.landscapeRight],
    );
  }

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(builder: (_, orientation) {
      return WillPopScope(
          onWillPop: () async => _handleBackPress(),
          child: Scaffold(
            body: Safe<PERSON>rea(
              child: TradingView(url: widget.tradingViewUrlFull!),
            ),
          ));
    });
  }
}
