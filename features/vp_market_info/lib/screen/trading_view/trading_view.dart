import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_design_system/vp_design_system.dart';

class TradingView extends StatelessWidget {
  const TradingView({Key? key, required this.url, this.showCloseIcon = true})
    : super(key: key);

  final String url;
  final bool showCloseIcon;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Theme.of(context).colorScheme.surface,
      child: Stack(
        children: [
          /// webview
          VPWebView(
            needAccessToken: true,
            url: url,
            gestureRecognizers: {
              Factory<OneSequenceGestureRecognizer>(
                () => EagerGestureRecognizer(
                  supportedDevices: PointerDeviceKind.values.toSet(),
                ),
              ),
            },
          ),

          if (showCloseIcon)
            Positioned(
              bottom: 60,
              right: 12,
              child: GestureDetector(
                onTap: () {
                  SystemChrome.setPreferredOrientations([
                    DeviceOrientation.portraitUp,
                  ]);
                  context.pop();
                },
                child: Padding(
                  padding: const EdgeInsets.all(0),
                  child: Icon(
                    Icons.close_rounded,
                    size: 32,
                    color: themeData.icon,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
