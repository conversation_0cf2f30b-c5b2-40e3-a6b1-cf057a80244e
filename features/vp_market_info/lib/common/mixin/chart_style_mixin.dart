import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:vp_common/extensions/double_extensions.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

mixin ChartStyleMixin {
  getTrackBallMarkerSetting(Color color) => TrackballMarkerSettings(
    markerVisibility: TrackballVisibilityMode.visible,
    height: 10,
    width: 10,
    borderWidth: 20,
    shape: DataMarkerType.circle,
    color: color,
    borderColor: color.withOpacity(0.24),
  );

  List<Color> get _increaseColors =>
      <Color>[themeData.greenChart, themeData.greenChart];

  List<Color> get _decreaseColors =>
      <Color>[themeData.redChart, themeData.redChart];

  List<Color> get _referenceColors =>
      <Color>[themeData.yellowChart, themeData.yellowChart];

  List<Color> get _ceilingColors =>
      <Color>[themeData.purple, themeData.purpleDark];

  List<Color> get _floorColors =>
      <Color>[themeData.blue, themeData.blueDark];

  Color chartColorByPrice({
    required double reference,
    required num current,
    double? ceiling,
    double? floor,
  }) {
    final ceilingValue = ceiling?.toPrecision(2);
    final floorValue = floor?.toPrecision(2);
    final referenceValue = reference.toPrecision(2);
    final currentValue = current.toDouble().toPrecision(2);

    if (reference == 0 || reference == double.infinity) {
      return themeData.yellowChart;
    }

    if (ceilingValue != null && ceilingValue == currentValue) {
      return themeData.ceilingColor;
    }

    if (floorValue != null && floorValue == currentValue) {
      return themeData.floorColor;
    }

    if (referenceValue < currentValue) {
      return themeData.greenChart;
    }

    if (referenceValue > currentValue) {
      return themeData.redChart;
    }

    return themeData.yellowChart;
  }

  Color colorByPrice({
    required double reference,
    required num current,
    double? ceiling,
    double? floor,
  }) {
    final ceilingValue = ceiling?.toPrecision(2);
    final floorValue = floor?.toPrecision(2);
    final referenceValue = reference.toPrecision(2);
    final currentValue = current.toDouble().toPrecision(2);

    if (reference == 0 || reference == double.infinity) {
      return themeData.referenceColor;
    }

    if (ceilingValue != null && ceilingValue == currentValue) {
      return themeData.ceilingColor;
    }

    if (floorValue != null && floorValue == currentValue) {
      return themeData.floorColor;
    }

    if (referenceValue < currentValue) {
      return themeData.increaseColor;
    }

    if (referenceValue > currentValue) {
      return themeData.decreaseColor;
    }

    return themeData.referenceColor;
  }

  List<Color> chartColors({
    required double reference,
    required double current,
    double? ceiling,
    double? floor,
  }) {
    final ceilingValue = ceiling?.toPrecision(2);
    final floorValue = floor?.toPrecision(2);
    final referenceValue = reference.toPrecision(2);
    final currentValue = current.toDouble().toPrecision(2);

    if (ceilingValue == currentValue) {
      return _ceilingColors;
    }

    if (floorValue == currentValue) {
      return _floorColors;
    }

    if (referenceValue < currentValue) {
      return _increaseColors;
    }

    if (referenceValue > currentValue) {
      return _decreaseColors;
    }

    return _referenceColors;
  }

  List<double> get chartGradientStops => <double>[0.2, 1];

  Shader getChartShader(List<Color> colors, ShaderDetails chartShaderDetails) =>
      ui.Gradient.linear(chartShaderDetails.rect.topRight,
          chartShaderDetails.rect.centerLeft, colors, chartGradientStops);
}
