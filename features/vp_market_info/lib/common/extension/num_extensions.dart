import 'package:vp_common/extensions/num_extensions.dart';
import 'package:intl/intl.dart';
import 'package:vp_market/generated/l10n.dart';

extension NumExtensions on num {
  String toVolDisplay({NumberFormat? numberFormat}) {
    NumberFormat currencyFormat =
        numberFormat ?? NumberFormat("#,###.#", "en_ES");
    double volume = toDouble();
    if (volume >= 100000000) {
      volume = volume / 1000000000;
      return '${currencyFormat.format(volume)} ${VPMarketInfoLocalize.current.bil}';
    }
    if (volume >= 100000) {
      volume = volume / 1000000;
      return '${currencyFormat.format(volume)} ${VPMarketInfoLocalize.current.mil}';
    }
    if (volume >= 100) {
      volume = volume / 1000;
      return '${currencyFormat.format(volume)} ${VPMarketInfoLocalize.current.k}';
    }
    return volume.toInt().toString();
  }

  String formatTotalVolume() {
    if (this == 0.0) {
      return 0.toString();
    }
    if (this >= 1000000000) {
      return '${(this / 1000000000).toFormatStock()} ${VPMarketInfoLocalize.current.bil}';
    } else if (this >= 1000000) {
      return '${(this / 1000000).toFormatStock()} ${VPMarketInfoLocalize.current.mil}';
    } else if (this >= 1000) {
      return '${(this / 1000).toFormatStock()} ${VPMarketInfoLocalize.current.k}';
    } else {
      return toString();
    }
  }
}
