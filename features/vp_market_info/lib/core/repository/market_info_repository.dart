import 'package:vp_market/data/chart_buy_up_sell_obj.dart';
import 'package:vp_market/data/heatmap_industry_index_model.dart';
import 'package:vp_market/data/heatmap_sector_symbols_model.dart';
import 'package:vp_market/data/heatmap_stock_symbols_model.dart';
import 'package:vp_market/data/icb_level_model.dart';
import 'package:vp_market/data/market_entity.dart';
import 'package:vp_market/data/market_liquidity_model.dart';
import 'package:vp_market/data/market_price_chart_entity.dart';
import 'package:vp_market/data/market_top_trading_entity.dart';
import 'package:vp_market/data/market_volatility_entity.dart';
import 'package:vp_market/data/stock_detail_entity.dart';
import 'package:vp_market/data/supply_demand_model.dart';
import 'package:vp_market/data/top_foreign_trading_d_obj.dart';
import 'package:vp_market/data/top_foreign_trading_obj.dart';
import 'package:vp_market/data/transaction_statistic_chart.dart';

abstract class MarketRepository {
  Future<MarketVolatilityEntity> getMarketVolatility(String marketCode);

  Future<MarketPriceChartEntity> getMarketPriceChart(String marketCode);

  Future<MarketTopTradingEntity> getMarketTopTradingDay(String floorCode);

  Future<MarketTopTradingEntity> getMarketTopTradingHistory(
    String floorCode,
    String type,
  );

  Future<ListChartBuyUpSellResponseObj?> getBuyUpSellDownByMarket(
    String indexCode,
  );

  Future<TopForeignTradingDObj?> getTopForeignTradingD();

  Future<TopForeignTradingObj?> getTopForeignTrading(String type);

  Future<List<IcbLevelModel>> getIcbLevel(String icbLevels);

  Future<List<HeatMapSectorBySymbolsModel>> getHeatMapSectorBySymbols({
    required String indexCode,
    required String icbCodeLevel2s,
    required String sortBy,
  });

  Future<List<HeatMapStockBySymbolsModel>> getHeatMapStockBySymbols({
    required String symbols,
    required String sortBy,
  });

  Future<List<HeatMapIndustryByIndexModel>> getHeatMapIndustryByIndex({
    required String icbCodeLevel2s,
    required String sortBy,
  });

  Future<List<TransactionStatisticChart>> getTransactionStatistic({
    required String timeFrame,
    required String marketCode,
  });

  Future<List<MarketLiquidityModel>> getMarketLiquidity({
    required String type,
    required String marketCode,
  });

  Future<List<SupplyDemandModel>> getIntradaySupplyDemandChart({
    required String marketCode,
  });

  Future<List<MarketEntity>> getListMarket();

  Future<List<StockDetailEntity>> getStockList(String symbols);
}
