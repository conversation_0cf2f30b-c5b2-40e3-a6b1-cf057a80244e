import 'package:vp_common/extensions/num_extensions.dart';

class MarketInfoConstants {
  static const String all = "ALL";
  static const String VNINDEX = "VNINDEX";
  static const String HNXINDEX = "HNXINDEX";
  static const String UPCOMINDEX = "UPCOMINDEX";
  static const String hsx = "HOSE";
  static const String hnx = "HNX";
  static const String upCom = "UPCOM";

  ///type timer
  static const String halfHour = "30";
  static const String twoHours = "2H";
  static const String oneDay = "1D";

  ///loại biểu đồ
  static const String chartMarket = 'market';
  static const String chartStock = 'stock';

  static String getExchange(String floorCode) {
    final map = {
      "02": MarketInfoConstants.hnx,
      "04": MarketInfoConstants.upCom,
      "10": MarketInfoConstants.hsx,
    };
    return map[floorCode] ?? '';
  }

  static String formatPercent(num? value) {
    final doubleValue = ((value ?? 0).toDouble() * 100).toStringAsFixed(2);
    return '$doubleValue %';
  }

  static String formatVol(
    num? value, {
    bool excludeZero = true,
    bool formatDerivative = false,
  }) {
    if (value == null) {
      return '-';
    }

    if (value.isZero() && excludeZero) {
      return '-';
    }

    if (value.isOddLot) {
      return value.toInt().toString();
    }

    final volValue = value.toFormat3();
    String data = volValue.trim();
    if (data.length > 1 && !formatDerivative) {
      data = data.substring(0, data.length - 1);
    }

    return data;
  }
}
