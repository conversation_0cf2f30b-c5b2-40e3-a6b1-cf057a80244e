import 'package:vp_market/data/market_entity.dart';
import 'package:vp_market/data/market_volatility_entity.dart';

class MarketDataDefault {
  /// List Market Default
  static List<MarketEntity> getListMarketDefault() {
    List<MarketEntity> result = [];
    result.add(getMarketEntity('HOSE'));
    result.add(getMarketEntity('30'));
    result.add(getMarketEntity('HNX'));
    result.add(getMarketEntity('HNX30'));
    result.add(getMarketEntity('UPCOM'));
    return result;
  }

  /// List Market Default
  static List<MarketEntity> getListErrorMarket() {
    List<MarketEntity> result = [];
    result.add(getErrorMarketEntity('HOSE'));
    result.add(getErrorMarketEntity('30'));
    result.add(getErrorMarketEntity('HNX'));
    result.add(getErrorMarketEntity('HNX30'));
    result.add(getErrorMarketEntity('UPCOM'));
    return result;
  }

  static MarketEntity getMarketEntity(String marketCode) {
    return MarketEntity(
      marketCode: marketCode,
      marketId: '',
      marketIndex: 0,
      indexColor: '',
      indexPercentChange: 0,
      indexChange: 0,
      marketStatus: '',
    );
  }

  static MarketEntity getErrorMarketEntity(String marketCode) {
    return MarketEntity(
      marketCode: marketCode,
      marketId: '',
      marketIndex: -1,
      indexColor: '',
      indexPercentChange: 0,
      indexChange: 0,
      marketStatus: '',
    );
  }

  /// Market VolatilityEntity Default
  static MarketVolatilityEntity getVolatilityEntityDefault() {
    return MarketVolatilityEntity(
      advances: 0,
      declines: 0,
      noChange: 0,
      numberOfCe: 0,
      numberOfFl: 0,
      floorCode: '',
      countStockPerchangeLevel0: 0,
      countStockPerchangeLevel1: 0,
      countStockPerchangeLevel2: 0,
      countStockPerchangeLevel3: 0,
      countStockPerchangeLevel4: 0,
      countStockPerchangeLevel5: 0,
      countStockPerchangeLevel6: 0,
      countStockPerchangeLevel7: 0,
      countStockPerchangeLevel8: 0,
      countStockPerchangeLevel9: 0,
      countStockPerchangeLevel10: 0,
    );
  }
}
