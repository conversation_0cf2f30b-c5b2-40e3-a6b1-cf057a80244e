import 'dart:ui';

import 'package:vp_common/extensions/num_extensions.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_market/generated/l10n.dart';

class MarketEntity {
  String marketId;
  String marketCode;
  double marketIndex;
  num? totalVolume;
  num? totalValue;
  double? indexChange;
  double? indexPercentChange;
  int? advances;
  int? declines;
  int? noChange;
  int? numberOfCe;
  int? numberOfFl;
  String indexColor;
  String? marketStatus;
  double? reference;
  num? totalTrade;
  num? oddLotTotalValue;
  num? oddLotTotalVolume;
  String? indexTime;

  MarketEntity({
    required this.marketId,
    required this.marketCode,
    required this.marketIndex,
    required this.indexColor,
    this.totalVolume,
    this.totalValue,
    this.indexChange,
    this.indexPercentChange,
    this.advances,
    this.declines,
    this.noChange,
    this.numberOfCe,
    this.numberOfFl,
    required this.marketStatus,
    this.reference,
    this.totalTrade,
    this.oddLotTotalValue,
    this.oddLotTotalVolume,
    this.indexTime,
  });

  MarketEntity reset() {
    return MarketEntity(
      marketId: marketId,
      marketCode: marketCode,
      marketIndex: marketIndex,
      indexColor: indexColor,
      indexChange: indexChange,
      indexPercentChange: indexPercentChange,
      totalValue: totalValue,
      totalVolume: totalVolume,
      marketStatus: null,//marketStatus,
      reference: reference,
      totalTrade: totalTrade,
      oddLotTotalValue: oddLotTotalValue,
      oddLotTotalVolume: oddLotTotalVolume,
      indexTime: null,
      advances: null,
      declines: null,
      noChange: null,
      numberOfCe: null,
      numberOfFl: null,
    );
  }

  Color get colorByIndex {
    if (marketIndex.isZero()) {
      return themeData.increaseColor;
    }
    if ((indexChange ?? 0) > 0 || (indexPercentChange ?? 0) > 0) {
      return themeData.increaseColor;
    }
    if ((indexChange ?? 0) < 0 || (indexPercentChange ?? 0) < 0) {
      return themeData.decreaseColor;
    }
    return themeData.referenceColor;
  }

  Color get flashBg {
    if ((indexChange ?? 0) > 0 || (indexPercentChange ?? 0) > 0) {
      return themeData.primary16;
    }
    if ((indexChange ?? 0) < 0 || (indexPercentChange ?? 0) < 0) {
      return themeData.red16;
    }
    return themeData.yellow16;
  }

  String get exchangeName {
    if (marketCode == 'HOSE') {
      return 'VNINDEX';
    }
    if (marketCode == '30') {
      return 'VN30';
    }
    if (marketCode == 'HNX') {
      return 'HNX';
    }
    if (marketCode == 'HNX30') {
      return 'HNX30';
    }
    if (marketCode == 'UPCOM') {
      return 'UPCOM';
    }
    return marketCode;
  }

  //used for load tradingView
  String get marketSymbol {
    if (marketCode == 'HOSE') {
      return 'VNINDEX';
    }
    if (marketCode == '30') {
      return 'VN30';
    }
    if (marketCode == 'HNX') {
      return 'HNXINDEX';
    }
    if (marketCode == 'HNX30') {
      return 'HNX30';
    }
    if (marketCode == 'UPCOM') {
      return 'UPCOMINDEX';
    }
    return marketCode;
  }

  String get marketCodeApi {
    if (marketCode == 'HOSE') {
      return 'HOSE';
    }
    if (marketCode == '30') {
      return 'VN30';
    }
    if (marketCode == 'HNX') {
      return 'HNX';
    }
    if (marketCode == 'HNX30') {
      return 'HNX30';
    }
    if (marketCode == 'UPCOM') {
      return 'UPCOM';
    }
    return marketCode;
  }

  String get marketStatusName {
    final String pbMarketStatusO = VPMarketInfoLocalize.current.pbMarketStatusO;
    final String pbMarketStatusI = VPMarketInfoLocalize.current.pbMarketStatusI;
    final String pbMarketStatusC = VPMarketInfoLocalize.current.pbMarketStatusC;
    final String pbMarketStatusP = VPMarketInfoLocalize.current.pbMarketStatusP;
    final String pbMarketStatusS = VPMarketInfoLocalize.current.pbMarketStatusS;
    final String pbMarketStatus35 = VPMarketInfoLocalize.current.pbMarketStatus35;
    final String pbMarketStatusEND = VPMarketInfoLocalize.current.pbMarketStatusEND;
    final String pbMarketStatus6 = VPMarketInfoLocalize.current.pbMarketStatus6;
    switch (marketStatus) {
      case null:
        return '--';
      case 'C':
        return pbMarketStatusC;
      case 'I':
        return pbMarketStatusI;
      case 'L':
        return pbMarketStatusI;
      case 'O':
        return pbMarketStatusO;
      case 'E':
        return pbMarketStatusO;
      case 'P':
        return pbMarketStatusP;
      case 'A':
        return pbMarketStatusS;
      case 'S':
        return pbMarketStatusS;
      case '5':
        return pbMarketStatusO;
      case '10':
        return pbMarketStatusI;
      case '30':
        return pbMarketStatusS;
      case '35':
        return pbMarketStatus35;
      case '6':
        return pbMarketStatus6;
      default:
        return pbMarketStatusEND;
    }
  }

  double get getReference => marketIndex - (indexChange ?? 0);
}
