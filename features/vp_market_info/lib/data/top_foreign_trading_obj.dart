class TopForeignTradingObj {
  List<ForeignNetObj>? foreignNetBuy;
  List<ForeignNetObj>? foreignNetSell;
  List<ForeignNetObj>? topValueBuy;
  List<ForeignNetObj>? topValueSell;

  TopForeignTradingObj({
    this.foreignNetBuy,
    this.foreignNetSell,
    this.topValueBuy,
    this.topValueSell,
  });

  TopForeignTradingObj.fromJson(Map<String, dynamic> json) {
    if (json['foreignNetBuy'] != null) {
      foreignNetBuy = <ForeignNetObj>[];
      json['foreignNetBuy'].forEach((v) {
        foreignNetBuy!.add(ForeignNetObj.fromJson(v));
      });
    }
    if (json['foreignNetSell'] != null) {
      foreignNetSell = <ForeignNetObj>[];
      json['foreignNetSell'].forEach((v) {
        foreignNetSell!.add(ForeignNetObj.fromJson(v));
      });
    }
    if (json['topValueBuy'] != null) {
      topValueBuy = <ForeignNetObj>[];
      json['topValueBuy'].forEach((v) {
        topValueBuy!.add(ForeignNetObj.fromJson(v));
      });
    }
    if (json['topValueSell'] != null) {
      topValueSell = <ForeignNetObj>[];
      json['topValueSell'].forEach((v) {
        topValueSell!.add(ForeignNetObj.fromJson(v));
      });
    }
  }
}

class ForeignNetObj {
  String? symbol;
  String? fullName;
  num? maxForeignNetSell;
  num? totalForeignSellVolume;
  num? totalForeignBuyVolume;
  num? totalForeignNetBuy;
  num? totalForeignNetSell;
  num? totalForeignSellValue;
  num? totalForeignBuyValue;

  ForeignNetObj({
    this.symbol,
    this.fullName,
    this.maxForeignNetSell,
    this.totalForeignSellVolume,
    this.totalForeignBuyVolume,
    this.totalForeignNetBuy,
    this.totalForeignNetSell,
    this.totalForeignSellValue,
    this.totalForeignBuyValue,
  });

  ForeignNetObj.fromJson(Map<String, dynamic> json) {
    symbol = json['symbol'];
    fullName = json['fullName'];
    maxForeignNetSell = json['maxForeignNetSell'];
    totalForeignSellVolume = json['totalForeignSellVolume'];
    totalForeignBuyVolume = json['totalForeignBuyVolume'];
    totalForeignNetBuy = json['totalForeignNetBuy'];
    totalForeignNetSell = json['totalForeignNetSell'];
    totalForeignSellValue = json['totalForeignSellValue'];
    totalForeignBuyValue = json['totalForeignBuyValue'];
  }
}
