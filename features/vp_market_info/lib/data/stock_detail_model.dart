import 'package:vp_market/core/const/market_info_constants.dart';
import 'package:vp_market/data/stock_detail_entity.dart';

class StockDetailModel {
  String? symbol;
  String? underlyingSymbol;
  String? stockId;
  String? fullName;
  String? tradingdate;
  String? floorCode;
  String? stockType;
  num? ceiling;
  num? floor;
  num? reference;
  num? bidPrice3;
  num? bidVol3;
  num? bidPrice2;
  num? bidVol2;
  String? bidPrice1;
  num? bidVol1;
  num? closePrice;
  num? closeVol;
  num? change;
  double? changePercent;
  String? offerPrice1;
  num? offerVol1;
  num? offerPrice2;
  num? offerVol2;
  num? offerPrice3;
  num? offerVol3;
  num? totalTrading;
  num? totalTradingValue;
  num? averagePrice;
  String? otherName;

  // num? totalBidQTTY;
  // num? totalOfferQTTY;
  // double? averagePrice;
  num? open;
  num? high;
  num? low;
  num? totalBidQTTY; // <PERSON>ư mua
  num? totalOfferQTTY; // <PERSON><PERSON> bán
  num? foreignBuy; //  NN mua
  num? foreignSell; // NN bán
  num? diff; // NN bán

  StockDetailModel({
    this.symbol,
    this.underlyingSymbol,
    this.stockId,
    this.fullName,
    this.tradingdate,
    this.floorCode,
    this.stockType,
    this.ceiling,
    this.floor,
    this.reference,
    this.bidPrice3,
    this.bidVol3,
    this.bidPrice2,
    this.bidVol2,
    this.bidPrice1,
    this.bidVol1,
    this.closePrice,
    this.closeVol,
    this.change,
    this.changePercent,
    this.offerPrice1,
    this.offerVol1,
    this.offerPrice2,
    this.offerVol2,
    this.offerPrice3,
    this.offerVol3,
    this.totalTrading,
    this.totalTradingValue,
    this.averagePrice,
    this.otherName,
    this.open,
    this.high,
    this.low,
    this.foreignBuy,
    this.foreignSell,
    this.totalBidQTTY,
    this.totalOfferQTTY,
    this.diff,
  });

  StockDetailModel.fromJson(Map<String, dynamic> json) {
    symbol = json['symbol'];
    underlyingSymbol = json['underlyingSymbol'];
    stockId = json['stockId'];
    fullName = json['fullName'];
    tradingdate = json['tradingDate'];
    floorCode = json['floorCode'];
    stockType = json['stockType'];
    ceiling = json['ceiling'];
    floor = num.tryParse(json['floor'].toString()) ?? 0;
    reference = json['reference'];
    bidPrice3 = json['bidPrice3'];
    bidVol3 = json['bidVol3'];
    bidPrice2 = json['bidPrice2'];
    bidVol2 = json['bidVol2'];
    bidPrice1 = json['bidPrice1'];
    bidVol1 = json['bidVol1'];
    closePrice = json['closePrice'];
    closeVol = json['closeVol'];
    change = json['change'];
    changePercent =
        (json['stockPercentChange'] == null
                ? 0
                : (json['stockPercentChange'] as num))
            .toDouble();
    offerPrice1 = json['offerPrice1'];
    offerVol1 = json['offerVol1'];
    offerPrice2 = json['offerPrice2'];
    offerVol2 = json['offerVol2'];
    offerPrice3 = json['offerPrice3'];
    offerVol3 = json['offerVol3'];
    totalTrading = json['totalTrading'];
    totalTradingValue = json['totalTradingValue'];
    averagePrice = json['averagePrice'] ?? 0;
    open = json['open'];
    high = json['high'];
    low = json['low'];
    totalBidQTTY = json['totalBidQTTY'];
    totalOfferQTTY = json['totalOfferQTTY'];
    foreignBuy =
        num.tryParse((json['foreignBuyVolume'] ?? '0').toString()) ?? 0;
    foreignSell = json['foreignSellVolume'];
    diff = json['diff'];
    otherName = json['otherName'];
  }
}

List<StockDetailEntity> transformListStockEntities(dynamic json) =>
    (json as List<dynamic>)
        .map((e) => StockDetailModel.fromJson(e).entity)
        .toList();

extension StockDetailMapper on StockDetailModel {
  StockDetailEntity get entity => StockDetailEntity(
    symbol: symbol ?? '',
    underlyingSymbol: underlyingSymbol ?? '',
    companyName: fullName ?? '',
    price: (closePrice ?? reference ?? 0).toDouble(),
    closePrice: (closePrice ?? 0).toDouble(),
    referencePrice: (reference ?? 0).toDouble(),
    ceilingPrice: (ceiling ?? 0).toDouble(),
    floorPrice: (floor ?? 0).toDouble(),
    openPrice: open?.toDouble() ?? 0,
    highestPrice: high?.toDouble() ?? 0,
    lowestPrice: low?.toDouble() ?? 0,
    averagePrice: averagePrice?.toDouble() ?? 0,
    otherName: otherName,
    totalVolume: (totalTrading ?? 0).toDouble(),
    totalTradingValue: (totalTradingValue ?? 0).toDouble(),
    changePercent: (changePercent ?? 0).toDouble(),
    changeValue: (change ?? 0).toDouble(),
    offerPrice1: offerPrice1 ?? '',
    offerPrice2: offerPrice2?.toString(),
    offerPrice3: offerPrice3?.toString(),
    offerVol1: offerVol1 ?? 0,
    offerVol2: offerVol2 ?? 0,
    offerVol3: offerVol3 ?? 0,
    bidPrice1: bidPrice1 ?? '',
    bidPrice2: bidPrice2?.toString(),
    bidPrice3: bidPrice3?.toString(),
    bidVol1: bidVol1 ?? 0,
    bidVol2: bidVol2 ?? 0,
    bidVol3: bidVol3 ?? 0,
    totalBidQTTY: totalBidQTTY,
    totalOfferQTTY: totalOfferQTTY,
    foreignBuy: foreignBuy ?? 0,
    foreignSell: foreignSell ?? 0,
    exchange: MarketInfoConstants.getExchange(floorCode ?? ''),
    stockType: stockType,
    diff: diff,
  );
}
