
import 'package:vp_market/data/market_price_chart_entity.dart';

class MarketPriceChartModel {
  num? reference;
  String? indexColor;
  num? indexChange;
  num? indexPercentChange;
  int? totalTrade;
  int? totalVolume;
  int? totalValue;
  String? marketStatus;
  int? advances;
  int? declines;
  int? noChange;
  String? marketCode;
  int? numberOfCe;
  int? numberOfFl;
  double? marketIndex;
  String? indexTime;
  List<Index>? index;
  num? oddLotTotalValue;
  num? oddLotTotalVolume;

  MarketPriceChartModel({
    this.reference,
    this.indexColor,
    this.indexChange,
    this.indexPercentChange,
    this.totalTrade,
    this.totalVolume,
    this.totalValue,
    this.marketStatus,
    this.advances,
    this.declines,
    this.noChange,
    this.marketCode,
    this.numberOfCe,
    this.numberOfFl,
    this.marketIndex,
    this.index,
    this.indexTime,
    this.oddLotTotalVolume,
    this.oddLotTotalValue,
  });

  MarketPriceChartModel.fromJson(Map<String, dynamic> json) {
    reference = json['reference'];
    indexTime = json['indexTime'];
    indexColor = json['indexColor'];
    indexChange = json['indexChange'];
    indexPercentChange = json['indexPercentChange'];
    totalTrade = json['totalTrade'];
    totalVolume = json['totalVolume'];
    totalValue = json['totalValue'];
    marketStatus = json['marketStatus'];
    advances = json['advances'];
    declines = json['declines'];
    noChange = json['noChange'];
    marketCode = json['marketCode'];
    numberOfCe = json['numberOfCe'];
    numberOfFl = json['numberOfFl'];
    marketIndex = json['marketIndex'];
    oddLotTotalVolume = json['oddLotTotalVolume'];
    oddLotTotalValue = json['oddLotTotalValue'];
    if (json['Index'] != null) {
      index = <Index>[];
      json['Index'].forEach((v) {
        index!.add(Index.fromJson(v));
      });
    }
  }
}

class Index {
  double? marketIndex;

  String? indexTime;

  num? volume;

  num? totalVolume;

  num? totalValue;

  Index({this.marketIndex, this.indexTime});

  Index.fromJson(Map<String, dynamic> json) {
    marketIndex = json['marketIndex'];
    indexTime = json['indexTime'];
    volume = json['volume'];
    totalVolume = json['totalVolume'];
  }
}

extension MarketPriceChartMapper on MarketPriceChartModel {
  MarketPriceChartEntity get entity => MarketPriceChartEntity(
        reference: (reference ?? 0).toDouble(),
        totalVolume: totalVolume,
        indexTime: indexTime,
        indexChange: (indexChange ?? 0).toDouble(),
        indexPercentChange: (indexPercentChange ?? 0).toDouble(),
        indexColor: indexColor ?? 'noChange',
        marketCode: marketCode,
        index: index == null
            ? []
            : index!
                .map(
                  (e) => IndexEntity(
                      marketIndex: e.marketIndex ?? 0,
                      indexTime: e.indexTime ?? '',
                      volume: e.volume,
                      totalVolume: e.totalVolume,
                      totalValue: e.totalValue),
                )
                .toList(),
        marketIndex: marketIndex ?? 0,
        totalValue: totalValue,
        marketStatus: marketStatus,
        advances: advances,
        declines: declines,
        noChange: noChange,
        numOfCeiling: numberOfCe,
        numOfFloor: numberOfFl,
        totalTrade: totalTrade,
        oddLotTotalVolume: oddLotTotalVolume,
        oddLotTotalValue: oddLotTotalValue,
      );
}

MarketPriceChartEntity transformMarketChartEntity(dynamic json) =>
    MarketPriceChartModel.fromJson((json as Map<String, dynamic>)["data"])
        .entity;

List<MarketPriceChartEntity> transformListMarketChartEntity(dynamic json) =>
    (json as List<dynamic>)
        .map((e) => MarketPriceChartModel.fromJson(e).entity)
        .toList();
