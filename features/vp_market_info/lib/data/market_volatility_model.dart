
import 'package:vp_market/data/market_volatility_entity.dart';

class MarketVolatilityModel {
  double? indexPercentChange;
  int? totalTrade;
  int? totalVolume;
  int? totalValue;
  String? marketStatus;
  int? advances;
  int? declines;
  int? noChange;
  int? numberOfCe;
  int? numberOfFl;
  String? floorCode;
  String? indexcode;
  int? countStockPerchangeLevel0;
  int? countStockPerchangeLevel1;
  int? countStockPerchangeLevel2;
  int? countStockPerchangeLevel3;
  int? countStockPerchangeLevel4;
  int? countStockPerchangeLevel5;
  int? countStockPerchangeLevel6;
  int? countStockPerchangeLevel7;
  int? countStockPerchangeLevel8;
  int? countStockPerchangeLevel9;
  int? countStockPerchangeLevel10;

  MarketVolatilityModel({
    this.indexPercentChange,
    this.totalTrade,
    this.totalVolume,
    this.totalValue,
    this.marketStatus,
    this.advances,
    this.declines,
    this.noChange,
    this.numberOfCe,
    this.numberOfFl,
    this.floorCode,
    this.indexcode,
    this.countStockPerchangeLevel0,
    this.countStockPerchangeLevel1,
    this.countStockPerchangeLevel2,
    this.countStockPerchangeLevel3,
    this.countStockPerchangeLevel4,
    this.countStockPerchangeLevel5,
    this.countStockPerchangeLevel6,
    this.countStockPerchangeLevel7,
    this.countStockPerchangeLevel8,
    this.countStockPerchangeLevel9,
    this.countStockPerchangeLevel10,
  });

  MarketVolatilityModel.fromJson(Map<String, dynamic> jsonObj) {
    final json = jsonObj['data'];
    if (json is Map) {
      indexPercentChange = json['indexPercentChange'];
      totalTrade = json['totalTrade'];
      totalVolume = json['totalVolume'];
      totalValue = json['totalValue'];
      marketStatus = json['marketStatus'];
      advances = json['advances'];
      declines = json['declines'];
      noChange = json['noChange'];
      numberOfCe = json['numberOfCe'];
      numberOfFl = json['numberOfFl'];
      floorCode = json['FloorCode'];
      indexcode = json['Indexcode'];

      countStockPerchangeLevel0 = json['CountStockPerchangeLevel0'];
      countStockPerchangeLevel1 = json['CountStockPerchangeLevel1'];
      countStockPerchangeLevel2 = json['CountStockPerchangeLevel2'];
      countStockPerchangeLevel3 = json['CountStockPerchangeLevel3'];
      countStockPerchangeLevel4 = json['CountStockPerchangeLevel4'];
      countStockPerchangeLevel5 = json['CountStockPerchangeLevel5'];
      countStockPerchangeLevel6 = json['CountStockPerchangeLevel6'];
      countStockPerchangeLevel7 = json['CountStockPerchangeLevel7'];
      countStockPerchangeLevel8 = json['CountStockPerchangeLevel8'];
      countStockPerchangeLevel9 = json['CountStockPerchangeLevel9'];
      countStockPerchangeLevel10 = json['CountStockPerchangeLevel10'];
    }
  }
}

extension MarketVolatilityMapper on MarketVolatilityModel {
  MarketVolatilityEntity get entity => MarketVolatilityEntity(
        countStockPerchangeLevel0: countStockPerchangeLevel0 ?? 0,
        countStockPerchangeLevel1: countStockPerchangeLevel1 ?? 0,
        countStockPerchangeLevel2: countStockPerchangeLevel2 ?? 0,
        countStockPerchangeLevel3: countStockPerchangeLevel3 ?? 0,
        countStockPerchangeLevel4: countStockPerchangeLevel4 ?? 0,
        countStockPerchangeLevel5: countStockPerchangeLevel5 ?? 0,
        countStockPerchangeLevel6: countStockPerchangeLevel6 ?? 0,
        countStockPerchangeLevel7: countStockPerchangeLevel7 ?? 0,
        countStockPerchangeLevel8: countStockPerchangeLevel8 ?? 0,
        countStockPerchangeLevel9: countStockPerchangeLevel9 ?? 0,
        countStockPerchangeLevel10: countStockPerchangeLevel10 ?? 0,
        floorCode: floorCode ?? '',
        numberOfFl: numberOfFl ?? 0,
        declines: declines ?? 0,
        numberOfCe: numberOfCe ?? 0,
        noChange: noChange ?? 0,
        advances: advances ?? 0,
      );
}

MarketVolatilityEntity transformMarketVolatilityEntity(dynamic json) =>
    MarketVolatilityModel.fromJson(json as Map<String, dynamic>).entity;
