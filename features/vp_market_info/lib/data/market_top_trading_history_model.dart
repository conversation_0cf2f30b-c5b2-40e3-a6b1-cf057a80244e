
import 'package:vp_market/data/market_top_trading_entity.dart';

class MarketTopTradingHistoryModeL {
  List<TopVolumes>? topVolumes;
  List<TopValues>? topValues;
  List<TopPercentChange>? topPercentUps;
  List<TopPercentChange>? topPercentDowns;

  MarketTopTradingHistoryModeL(
      {
      this.topVolumes,
      this.topValues,
      this.topPercentUps,
      this.topPercentDowns});

  MarketTopTradingHistoryModeL.fromJson(Map<String, dynamic> json) {
    if (json['TopVolumes'] != null) {
      topVolumes = <TopVolumes>[];
      json['TopVolumes'].forEach((v) {
        topVolumes!.add(TopVolumes.fromJson(v));
      });
    }
    if (json['TopValues'] != null) {
      topValues = <TopValues>[];
      json['TopValues'].forEach((v) {
        topValues!.add(TopValues.fromJson(v));
      });
    }
    if (json['TopPercentUps'] != null) {
      topPercentUps = <TopPercentChange>[];
      json['TopPercentUps'].forEach((v) {
        topPercentUps!.add(TopPercentChange.fromJson(v));
      });
    }
    if (json['TopPercentDowns'] != null) {
      topPercentDowns = <TopPercentChange>[];
      json['TopPercentDowns'].forEach((v) {
        topPercentDowns!.add(TopPercentChange.fromJson(v));
      });
    }
  }
}

class TopVolumes {
  String? ticker;
  String? fullName;
  int? highestMatchVolume;
  int? lowestMatchVolume;
  int? averageMatchVolume;

  TopVolumes(
      {this.ticker,
      this.fullName,
      this.highestMatchVolume,
      this.lowestMatchVolume,
      this.averageMatchVolume});

  TopVolumes.fromJson(Map<String, dynamic> json) {
    ticker = json['ticker'];
    fullName = json['fullName'];
    highestMatchVolume = json['highestMatchVolume'];
    lowestMatchVolume = json['lowestMatchVolume'];
    averageMatchVolume = json['averageMatchVolume'];
  }
}

class TopValues {
  String? ticker;
  String? fullName;
  int? highestMatchValue;
  int? lowestMatchValue;
  int? averageMatchValue;

  TopValues(
      {this.ticker,
      this.fullName,
      this.highestMatchValue,
      this.lowestMatchValue,
      this.averageMatchValue});

  TopValues.fromJson(Map<String, dynamic> json) {
    ticker = json['ticker'];
    fullName = json['fullName'];
    highestMatchValue = json['highestMatchValue'];
    lowestMatchValue = json['lowestMatchValue'];
    averageMatchValue = json['averageMatchValue'];
  }
}

class TopPercentChange {
  String? ticker;
  String? fullName;
  double? percentPriceChange;

  TopPercentChange({this.ticker, this.fullName, this.percentPriceChange});

  TopPercentChange.fromJson(Map<String, dynamic> json) {
    ticker = json['ticker'];
    fullName = json['fullName'];
    percentPriceChange = json['percentPriceChange'];
  }
}

extension MarketTopTradingHistoryMapper on MarketTopTradingHistoryModeL {
  MarketTopTradingEntity get entity => MarketTopTradingEntity(
        topPercentDown: topPercentDowns != null
            ? topPercentDowns!.map(mapTopPercent).toList()
            : [],
        topPercentUp: topPercentUps != null
            ? topPercentUps!.map(mapTopPercent).toList()
            : [],
        topValue: topValues != null ? topValues!.map(maTopValue).toList() : [],
        topVolume:
            topVolumes != null ? topVolumes!.map(mapTopVolume).toList() : [],
      );

  StockTopTradingEntity mapTopPercent(TopPercentChange item) =>
      StockTopTradingEntity(
        symbol: item.ticker ?? '',
        fullName: item.fullName ?? '',
        percentChange: item.percentPriceChange,
      );

  StockTopTradingEntity maTopValue(TopValues item) => StockTopTradingEntity(
        symbol: item.ticker ?? '',
        fullName: item.fullName ?? '',
        topTradingValue: item.averageMatchValue,
      );

  StockTopTradingEntity mapTopVolume(TopVolumes item) => StockTopTradingEntity(
        symbol: item.ticker ?? '',
        fullName: item.fullName ?? '',
        topVolume: item.averageMatchVolume,
      );
}

MarketTopTradingEntity transformMarketTopTradingEntity(dynamic json) =>
    MarketTopTradingHistoryModeL.fromJson(json as Map<String, dynamic>).entity;
