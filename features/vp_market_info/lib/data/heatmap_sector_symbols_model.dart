class HeatMapSectorBySymbolsModel {
  String? icbCodeLevel2;
  String? icbNameLevel2;
  List<String>? listStock;
  num? marketCap;
  num? totalTrading;
  num? totalTradingValue;
  num? foreignBuyValue;
  num? foreignSellValue;
  num? foreignNetBuyValue;
  num? foreignNetSellValue;

  HeatMapSectorBySymbolsModel(
      {this.icbCodeLevel2,
      this.icbNameLevel2,
      this.listStock,
      this.marketCap,
      this.totalTrading,
      this.totalTradingValue,
      this.foreignBuyValue,
      this.foreignSellValue,
      this.foreignNetBuyValue,
      this.foreignNetSellValue});

  HeatMapSectorBySymbolsModel.fromJson(Map<String, dynamic> json) {
    icbCodeLevel2 = json['icbCodeLevel2'];
    icbNameLevel2 = json['icbNameLevel2'];
    listStock = json['listStock'].cast<String>();
    marketCap = json['marketCap'];
    totalTrading = json['totalTrading'];
    totalTradingValue = json['totalTradingValue'];
    foreignBuyValue = json['foreignBuyValue'];
    foreignSellValue = json['foreignSellValue'];
    foreignNetBuyValue = json['foreignNetBuyValue'];
    foreignNetSellValue = json['foreignNetSellValue'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['icbCodeLevel2'] = icbCodeLevel2;
    data['icbNameLevel2'] = icbNameLevel2;
    data['listStock'] = listStock;
    data['marketCap'] = marketCap;
    data['totalTrading'] = totalTrading;
    data['totalTradingValue'] = totalTradingValue;
    data['foreignBuyValue'] = foreignBuyValue;
    data['foreignSellValue'] = foreignSellValue;
    data['foreignNetBuyValue'] = foreignNetBuyValue;
    data['foreignNetSellValue'] = foreignNetSellValue;
    return data;
  }
}
