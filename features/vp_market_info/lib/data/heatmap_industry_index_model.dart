class HeatMapIndustryByIndexModel {
  String? icbCodeLevel2;
  String? icbNameLevel2;
  num? totalMarketCap;
  num? totalTradingValue;
  List<String>? listStock;
  List<String>? listStockAdvances;
  num? advances;
  List<String>? listStockDeclines;
  num? declines;
  List<String>? listStockNoChange;
  num? noChange;
  num? percentChangeIcbLevel2;

  HeatMapIndustryByIndexModel(
      {this.icbCodeLevel2,
      this.icbNameLevel2,
      this.totalMarketCap,
      this.totalTradingValue,
      this.listStock,
      this.listStockAdvances,
      this.advances,
      this.listStockDeclines,
      this.declines,
      this.listStockNoChange,
      this.noChange,
      this.percentChangeIcbLevel2});

  HeatMapIndustryByIndexModel.fromJson(Map<String, dynamic> json) {
    icbCodeLevel2 = json['icbCodeLevel2'];
    icbNameLevel2 = json['icbNameLevel2'];
    totalMarketCap = json['totalMarketCap'];
    totalTradingValue = json['totalTradingValue'];
    listStock = json['listStock'].cast<String>();
    listStockAdvances = json['listStockAdvances'].cast<String>();
    advances = json['advances'];
    listStockDeclines = json['listStockDeclines'].cast<String>();
    declines = json['declines'];
    listStockNoChange = json['listStockNoChange'].cast<String>();
    noChange = json['noChange'];
    percentChangeIcbLevel2 = json['percentChangeIcbLevel2'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['icbCodeLevel2'] = icbCodeLevel2;
    data['icbNameLevel2'] = icbNameLevel2;
    data['totalMarketCap'] = totalMarketCap;
    data['totalTradingValue'] = totalTradingValue;
    data['listStock'] = listStock;
    data['listStockAdvances'] = listStockAdvances;
    data['advances'] = advances;
    data['listStockDeclines'] = listStockDeclines;
    data['declines'] = declines;
    data['listStockNoChange'] = listStockNoChange;
    data['noChange'] = noChange;
    data['percentChangeIcbLevel2'] = percentChangeIcbLevel2;
    return data;
  }
}
