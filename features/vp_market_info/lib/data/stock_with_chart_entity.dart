import 'package:vp_market/data/chart_price_entity.dart';
import 'package:vp_market/data/market_top_trading_entity.dart';
import 'package:vp_market/data/stock_detail_entity.dart';

class StockWithChartEntity {
  StockDetailEntity stockDetail;
  StockTopTradingEntity? stockTopTrading;
  List<ChartPriceEntity>? chartPriceData;

  StockWithChartEntity({
    required this.stockDetail,
    this.stockTopTrading,
    this.chartPriceData,
  });
}
