
import 'package:vp_market/data/market_top_trading_entity.dart';

class MarketTopTradingDModel {
  List<TopTradingD>? topVolumeD;
  List<TopTradingD>? topValueD;
  List<TopTradingD>? topPercentupD;
  List<TopTradingD>? topPercentdownD;

  MarketTopTradingDModel(
      {this.topVolumeD,
      this.topValueD,
      this.topPercentupD,
      this.topPercentdownD});

  MarketTopTradingDModel.fromJson(Map<String, dynamic> json) {
    if (json['TopVolumeD'] != null) {
      topVolumeD = <TopTradingD>[];
      json['TopVolumeD'].forEach((v) {
        topVolumeD!.add(TopTradingD.fromJson(v));
      });
    }
    if (json['TopValueD'] != null) {
      topValueD = <TopTradingD>[];
      json['TopValueD'].forEach((v) {
        topValueD!.add(TopTradingD.fromJson(v));
      });
    }
    if (json['TopPercentupD'] != null) {
      topPercentupD = <TopTradingD>[];
      json['TopPercentupD'].forEach((v) {
        topPercentupD!.add(TopTradingD.fromJson(v));
      });
    }
    if (json['TopPercentdownD'] != null) {
      topPercentdownD = <TopTradingD>[];
      json['TopPercentdownD'].forEach((v) {
        topPercentdownD!.add(TopTradingD.fromJson(v));
      });
    }
  }
}

class TopTradingD {
  String? symbol;
  String? fullName;
  int? change;
  double? stockPercentChange;
  int? totalTrading;
  int? totalTradingValue;

  TopTradingD(
      {this.symbol,
      this.fullName,
      this.change,
      this.stockPercentChange,
      this.totalTrading,
      this.totalTradingValue});

  TopTradingD.fromJson(Map<String, dynamic> json) {
    symbol = json['symbol'];
    fullName = json['fullName'];
    change = json['change'];
    stockPercentChange = json['stockPercentChange'];
    totalTrading = json['totalTrading'];
    totalTradingValue = json['totalTradingValue'];
  }
}

extension MarketTopTradingDMapper on MarketTopTradingDModel {
  MarketTopTradingEntity get entity => MarketTopTradingEntity(
      topPercentDown:
          topPercentdownD != null ? topPercentdownD!.map(map).toList() : [],
      topValue: topValueD != null ? topValueD!.map(map).toList() : [],
      topPercentUp:
          topPercentupD != null ? topPercentupD!.map(map).toList() : [],
      topVolume: topVolumeD != null ? topVolumeD!.map(map).toList() : []);

  StockTopTradingEntity map(TopTradingD item) => StockTopTradingEntity(
        symbol: item.symbol ?? '',
        fullName: item.fullName ?? '',
        topVolume: item.totalTrading,
        topTradingValue: item.totalTradingValue,
        percentChange: item.stockPercentChange,
        valueChange: item.change,
      );
}

MarketTopTradingEntity transformMarketTopTradingDEntity(dynamic json) =>
    MarketTopTradingDModel.fromJson(json as Map<String, dynamic>).entity;
