class TransactionStatisticModel {
  String? indexCode;
  String? timeFrame;
  Sum? sum;
  S? s;
  M? m;
  L? l;

  TransactionStatisticModel({
    this.indexCode,
    this.timeFrame,
    this.sum,
    this.s,
    this.m,
    this.l,
  });

  TransactionStatisticModel.fromJson(Map<String, dynamic> json) {
    indexCode = json['indexCode'];
    timeFrame = json['timeFrame'];
    sum = json['sum'] != null ? Sum.fromJson(json['sum']) : null;
    s = json['s'] != null ? S.fromJson(json['s']) : null;
    m = json['m'] != null ? M.fromJson(json['m']) : null;
    l = json['l'] != null ? L.fromJson(json['l']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (sum != null) {
      data['sum'] = sum!.toJson();
    }
    if (s != null) {
      data['s'] = s!.toJson();
    }
    if (m != null) {
      data['m'] = m!.toJson();
    }
    if (l != null) {
      data['l'] = l!.toJson();
    }
    return data;
  }
}

class Sum {
  num? sumBuyUpValue;
  num? sumSellDownValue;
  num? sumMarketValue;
  num? netSumValue;

  Sum(
      {this.sumBuyUpValue,
      this.sumSellDownValue,
      this.sumMarketValue,
      this.netSumValue});

  Sum.fromJson(Map<String, dynamic> json) {
    sumBuyUpValue = json['sumBuyUpValue'];
    sumSellDownValue = json['sumSellDownValue'];
    sumMarketValue = json['sumMarketValue'];
    netSumValue = json['netSumValue'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['sumBuyUpValue'] = sumBuyUpValue;
    data['sumSellDownValue'] = sumSellDownValue;
    data['sumMarketValue'] = sumMarketValue;
    data['netSumValue'] = netSumValue;
    return data;
  }
}

class S {
  num? totalBuyUpValueS;
  num? rateBuyUpValueS;
  num? totalSellDownValueS;
  num? rateSellDownValueS;
  num? netValueS;

  S(
      {this.totalBuyUpValueS,
      this.rateBuyUpValueS,
      this.totalSellDownValueS,
      this.rateSellDownValueS,
      this.netValueS});

  S.fromJson(Map<String, dynamic> json) {
    totalBuyUpValueS = json['totalBuyUpValueS'];
    rateBuyUpValueS = json['rateBuyUpValueS'];
    totalSellDownValueS = json['totalSellDownValueS'];
    rateSellDownValueS = json['rateSellDownValueS'];
    netValueS = json['netValueS'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['totalBuyUpValueS'] = totalBuyUpValueS;
    data['rateBuyUpValueS'] = rateBuyUpValueS;
    data['totalSellDownValueS'] = totalSellDownValueS;
    data['rateSellDownValueS'] = rateSellDownValueS;
    data['netValueS'] = netValueS;
    return data;
  }
}

class M {
  num? totalBuyUpValueM;
  num? rateBuyUpValueM;
  num? totalSellDownValueM;
  num? rateSellDownValueM;
  num? netValueM;

  M(
      {this.totalBuyUpValueM,
      this.rateBuyUpValueM,
      this.totalSellDownValueM,
      this.rateSellDownValueM,
      this.netValueM});

  M.fromJson(Map<String, dynamic> json) {
    totalBuyUpValueM = json['totalBuyUpValueM'];
    rateBuyUpValueM = json['rateBuyUpValueM'];
    totalSellDownValueM = json['totalSellDownValueM'];
    rateSellDownValueM = json['rateSellDownValueM'];
    netValueM = json['netValueM'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['totalBuyUpValueM'] = totalBuyUpValueM;
    data['rateBuyUpValueM'] = rateBuyUpValueM;
    data['totalSellDownValueM'] = totalSellDownValueM;
    data['rateSellDownValueM'] = rateSellDownValueM;
    data['netValueM'] = netValueM;
    return data;
  }
}

class L {
  num? totalBuyUpValueL;
  num? rateBuyUpValueL;
  num? totalSellDownValueL;
  num? rateSellDownValueL;
  num? netValueL;

  L(
      {this.totalBuyUpValueL,
      this.rateBuyUpValueL,
      this.totalSellDownValueL,
      this.rateSellDownValueL,
      this.netValueL});

  L.fromJson(Map<String, dynamic> json) {
    totalBuyUpValueL = json['totalBuyUpValueL'];
    rateBuyUpValueL = json['rateBuyUpValueL'];
    totalSellDownValueL = json['totalSellDownValueL'];
    rateSellDownValueL = json['rateSellDownValueL'];
    netValueL = json['netValueL'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['totalBuyUpValueL'] = totalBuyUpValueL;
    data['rateBuyUpValueL'] = rateBuyUpValueL;
    data['totalSellDownValueL'] = totalSellDownValueL;
    data['rateSellDownValueL'] = rateSellDownValueL;
    data['netValueL'] = netValueL;
    return data;
  }
}
