class SupplyDemandModel {
  num? timeStamp;
  num? sumBuyUpValue;
  num? sumSellDownValue;
  num? sumMarketValue;
  num? rateSumBuyUpValue;
  num? rateSumSellDownValue;
  num? buyUpOnSellDown;

  SupplyDemandModel(
      {this.timeStamp,
      this.sumBuyUpValue,
      this.sumSellDownValue,
      this.sumMarketValue,
      this.rateSumBuyUpValue,
      this.rateSumSellDownValue,
      this.buyUpOnSellDown});

  SupplyDemandModel.fromJson(Map<String, dynamic> json) {
    timeStamp = json['timeStamp'];
    sumBuyUpValue = json['sumBuyUpValue'];
    sumSellDownValue = json['sumSellDownValue'];
    sumMarketValue = json['sumMarketValue'];
    rateSumBuyUpValue = json['rateSumBuyUpValue'];
    rateSumSellDownValue = json['rateSumSellDownValue'];
    buyUpOnSellDown = num.tryParse('${json['buyUpOnSellDown']}');
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['timeStamp'] = timeStamp;
    data['sumBuyUpValue'] = sumBuyUpValue;
    data['sumSellDownValue'] = sumSellDownValue;
    data['sumMarketValue'] = sumMarketValue;
    data['rateSumBuyUpValue'] = rateSumBuyUpValue;
    data['rateSumSellDownValue'] = rateSumSellDownValue;
    data['buyUpOnSellDown'] = buyUpOnSellDown;
    return data;
  }

  num get yValue =>
      ((sumBuyUpValue ?? 0) - (sumSellDownValue ?? 0)) /
      ((sumMarketValue ?? 0) + 1);

  DateTime get dateTime =>
      DateTime.fromMillisecondsSinceEpoch((timeStamp ?? 0).toInt() * 1000);
}
