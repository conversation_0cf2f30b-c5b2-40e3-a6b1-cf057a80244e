import 'package:vp_market/data/stock_detail_entity.dart';

class FeaturedStockModel {
  String? symbol;
  String? fullName;
  int? change;
  double? stockPercentChange;
  int? totalTrading;
  int? totalTradingValue;

  FeaturedStockModel(
      {this.symbol,
        this.fullName,
        this.change,
        this.stockPercentChange,
        this.totalTrading,
        this.totalTradingValue});

  FeaturedStockModel.fromJson(Map<String, dynamic> json) {
    symbol = json['symbol'];
    fullName = json['fullName'];
    change = json['change'];
    stockPercentChange = json['stockPercentChange'];
    totalTrading = json['totalTrading'];
    totalTradingValue = json['totalTradingValue'];
  }
}

extension FeaturedStockMapper on FeaturedStockModel {
  StockDetailEntity get entity => StockDetailEntity(
    symbol: symbol ?? '',
    companyName: fullName ?? '',
    changePercent: stockPercentChange ?? 0,
    changeValue: (change ?? 0).toDouble(),
  );
}