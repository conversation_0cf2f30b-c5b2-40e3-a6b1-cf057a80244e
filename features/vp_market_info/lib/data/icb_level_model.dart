class IcbLevelModel {
  String? icbCode;
  String? icbName;

  IcbLevelModel({this.icbCode, this.icbName});

  IcbLevelModel.fromJson(Map<String, dynamic> json) {
    icbCode = json['icbCode'];
    icbName = json['icbName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['icbCode'] = icbCode;
    data['icbName'] = icbName;
    return data;
  }
}
