import 'package:vp_common/extensions/price_exts.dart';
import 'package:vp_market/common/utils/date_time_utils.dart';
import 'package:vp_market/data/chart_volume_model.dart';

class ChartPriceEntity {
  int x;

  double close;

  double? open;

  double volume;

  DateTime time;

  ChartPriceEntity({
    required this.x,
    required this.close,
    required this.time,
    required this.volume,
    this.open,
  });

  String get tradingDate => _formatDDMMYYYY(time);

  String get formattedTime => _formatHHMinutes(time);

  String get priceDisplay =>
      close.getPriceFormatted(currency: '', convertToThousand: true);

  ChartPriceEntity copyWith({
    int? x,
    double? close,
    double? volume,
    DateTime? time,
  }) {
    return ChartPriceEntity(
      x: x ?? this.x,
      close: close ?? this.close,
      volume: volume ?? this.volume,
      time: time ?? this.time,
    );
  }

  String _formatHHMinutes(DateTime time) {
    return _formatTime(time, DateTimeUtils.hourMinute);
  }

  String _formatDDMMYYYY(DateTime time) {
    return _formatTime(time, DateTimeUtils.ddMMYYYY);
  }

  String _formatTime(DateTime time, String format) {
    return DateTimeUtils.dateToString(dateTime: time, format: format);
  }
}

extension ChartPriceEntityExt on ChartPriceEntity {
  ChartVolumeModel toChartVolumeModel() {
    return ChartVolumeModel(
      volume: volume,
      time: time,
      price: close,
      open: open ?? 0,
    );
  }
}
