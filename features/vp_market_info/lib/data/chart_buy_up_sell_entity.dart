import 'package:vp_market/data/chart_buy_up_sell_obj.dart';
import 'package:collection/collection.dart';

class ChartBuyUpSellEntity {
  static const num offset = 1000000;
  ListChartBuyUpSellResponseObj? listChartBuyUpSellResponseObj;

  List<ChartBuyUpSellData?> sChartDataBuy = [];
  List<ChartBuyUpSellData?> mChartDataBuy = [];
  List<ChartBuyUpSellData?> lChartDataBuy = [];

  List<ChartBuyUpSellData?> sChartDataSell = [];
  List<ChartBuyUpSellData?> mChartDataSell = [];
  List<ChartBuyUpSellData?> lChartDataSell = [];

  ChartBuyUpSellEntity(this.listChartBuyUpSellResponseObj) {
    setSDataChart();
    setMDataChart();
    setLDataChart();
  }

  /// Set S Chart Data
  void setSDataChart() {
    final sData = listChartBuyUpSellResponseObj?.listS ?? [];
    for (var item in sData) {
      sChartDataBuy.add(
        ChartBuyUpSellData('${item.timeStamp}', getYBuy(item.buyUpValue)),
      );
      sChartDataSell.add(
        ChartBuyUpSellData('${item.timeStamp}', getYSell(item.sellDownValue)),
      );
    }
  }

  /// Set M Chart Data
  void setMDataChart() {
    final mData = listChartBuyUpSellResponseObj?.listM ?? [];
    for (var item in mData) {
      mChartDataBuy.add(
        ChartBuyUpSellData('${item.timeStamp}', getYBuy(item.buyUpValue)),
      );
      mChartDataSell.add(
        ChartBuyUpSellData('${item.timeStamp}', getYSell(item.sellDownValue)),
      );
    }
  }

  /// Set L Chart Data
  void setLDataChart() {
    final lData = listChartBuyUpSellResponseObj?.listL ?? [];
    for (var item in lData) {
      lChartDataBuy.add(
        ChartBuyUpSellData('${item.timeStamp}', getYBuy(item.buyUpValue)),
      );
      lChartDataSell.add(
        ChartBuyUpSellData('${item.timeStamp}', getYSell(item.sellDownValue)),
      );
    }
  }

  static num getYBuy(num? value) {
    return (value ?? 0) / offset;
  }

  static num getYSell(num? value) {
    return ((value ?? 0) / offset) * -1;
  }

  /// ChartData Buy
  List<ChartBuyUpSellData?> getListChartDataBuy(ChartBuyUpSellType type) {
    List<ChartBuyUpSellData?> listDataBuy = [];

    final mapBuy = {
      ChartBuyUpSellType.small.name: sChartDataBuy,
      ChartBuyUpSellType.medium.name: mChartDataBuy,
      ChartBuyUpSellType.long.name: lChartDataBuy,
    };

    listDataBuy = mapBuy[type.name] ?? [];
    return listDataBuy;
  }

  /// ChartData Sell
  List<ChartBuyUpSellData?> getListChartDataSell(ChartBuyUpSellType type) {
    List<ChartBuyUpSellData?> listDataSell = [];

    final mapSell = {
      ChartBuyUpSellType.small.name: sChartDataSell,
      ChartBuyUpSellType.medium.name: mChartDataSell,
      ChartBuyUpSellType.long.name: lChartDataSell,
    };

    listDataSell = mapSell[type.name] ?? [];
    return listDataSell;
  }

  /// Check noDAta
  bool isNoData() {
    return sChartDataBuy.isEmpty &&
        sChartDataSell.isEmpty &&
        mChartDataBuy.isEmpty &&
        mChartDataSell.isEmpty &&
        lChartDataBuy.isEmpty &&
        lChartDataSell.isEmpty;
  }

  num getMaxMinList(List<ChartBuyUpSellData?> charts) {
    num max = 0;

    if (charts.isNotEmpty) {
      final maxBuyObj = charts.reduce(
        (a, b) => (a?.y ?? 0).abs() > (b?.y ?? 0).abs() ? a : b,
      );
      max = maxBuyObj?.y ?? 0;
    }

    return max.abs();
  }

  num getMaxMinCharts(List<List<ChartBuyUpSellData?>> list) {
    List<num> data = [];
    list.map((e) => data.add(getMaxMinList(e))).toList();
    final num max = data.max;
    return max * 1.1;
  }
}

class ChartBuyUpSellData extends AppChartData {
  ChartBuyUpSellData(String x, num? y) : super(x, y);
}

enum ChartBuyUpSellType { small, medium, long }

class AppChartData {
  AppChartData(this.x, this.y);
  String x;
  num? y;
}