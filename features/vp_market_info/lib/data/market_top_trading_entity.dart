enum TopTradingDisplayMode {
  tradingValue,
  changePercent,
  volume,
}
class MarketTopTradingEntity {
  List<StockTopTradingEntity> topVolume;
  List<StockTopTradingEntity> topValue;
  List<StockTopTradingEntity> topPercentUp;
  List<StockTopTradingEntity> topPercentDown;

  MarketTopTradingEntity({
    required this.topVolume,
    required this.topValue,
    required this.topPercentUp,
    required this.topPercentDown,
  });
}

class StockTopTradingEntity {
  String symbol;
  String fullName;
  num? topVolume;
  num? topTradingValue;
  double? percentChange;
  int? valueChange;

  StockTopTradingEntity({
    required this.symbol,
    required this.fullName,
    this.topVolume,
    this.topTradingValue,
    this.percentChange,
    this.valueChange,
  });

  TopTradingDisplayMode get displayMode {
    return TopTradingDisplayMode.changePercent;
  }

}
