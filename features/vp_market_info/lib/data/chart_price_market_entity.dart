import 'package:flutter/material.dart';
import 'package:vp_common/extensions/string_extensions.dart';
import 'package:vp_market/data/chart_volume_model.dart';

class ChartPriceMarketEntity {
  int x;

  final double close;

  final String time;

  final num? volume;

  final num? totalVolume;

  final num? totalValue;

  final bool lastValueWithTime;

  ChartPriceMarketEntity({
    required this.x,
    required this.close,
    required this.time,
    this.volume,
    this.totalVolume,
    this.totalValue,
    this.lastValueWithTime = false,
  });

  //Ex: close = 37.52
  //return 37520
  double get closePriceWithoutThousand => close * 1000;

  TimeOfDay? get timeOfDay => time.hhmmssToTime;

  ChartPriceMarketEntity copyWith({
    int? x,
    double? close,
    String? time,
    num? volume,
    num? totalVolume,
    num? totalValue,
    bool? lastValueWithTime,
  }) {
    return ChartPriceMarketEntity(
      x: x ?? this.x,
      close: close ?? this.close,
      time: time ?? this.time,
      volume: volume ?? this.volume,
      totalVolume: totalVolume ?? this.totalVolume,
      totalValue: totalValue ?? this.totalValue,
      lastValueWithTime: lastValueWithTime ?? this.lastValueWithTime,
    );
  }
}

extension ChartPriceMarketEntityExt on ChartPriceMarketEntity {
  ChartVolumeModel toChartVolumeModel() {
    final now = DateTime.now();

    final timeOfDay = this.time.hhmmssToTime;

    final time =
        timeOfDay == null
            ? now
            : DateTime(
              now.year,
              now.month,
              now.day,
              timeOfDay.hour,
              timeOfDay.minute,
            );

    return ChartVolumeModel(
      time: time,
      price: close,
      volume: (volume ?? totalVolume ?? 0).toDouble(),
      open: 0, //TODO FAKE
    );
  }

  ChartPriceMarketEntity copyWith({
    num? volume,
    String? time,
    double? close,
    bool? lastValueWithTime,
  }) {
    return ChartPriceMarketEntity(
      x: x,
      close: close ?? this.close,
      time: time ?? this.time,
      volume: volume ?? this.volume,
      totalVolume: totalVolume,
      totalValue: totalValue,
      // open: open,
      lastValueWithTime: lastValueWithTime ?? this.lastValueWithTime,
    );
  }
}
