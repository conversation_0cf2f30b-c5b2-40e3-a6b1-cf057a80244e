import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class HeatMapStockBySymbolsModel {
  String? symbol;
  String? fullName;
  num? marketCap;
  num? closePrice;
  num? reference;
  num? stockPercentChange;
  num? ceiling;
  num? floor;
  num? open;
  num? high;
  num? low;
  num? totalTrading;
  num? totalTradingValue;
  num? foreignBuyValue;
  num? foreignSellValue;
  num? foreignNetBuyValue;
  num? foreignNetSellValue;

  HeatMapStockBySymbolsModel(
      {this.symbol,
      this.fullName,
      this.marketCap,
      this.closePrice,
      this.reference,
      this.stockPercentChange,
      this.ceiling,
      this.floor,
      this.open,
      this.high,
      this.low,
      this.totalTrading,
      this.totalTradingValue,
      this.foreignBuyValue,
      this.foreignSellValue,
      this.foreignNetBuyValue,
      this.foreignNetSellValue});

  HeatMapStockBySymbolsModel.fromJson(Map<String, dynamic> json) {
    symbol = json['symbol'];
    fullName = json['fullName'];
    marketCap = json['marketCap'];
    closePrice = json['closePrice'];
    reference = json['reference'];
    stockPercentChange = json['stockPercentChange'];
    ceiling = json['ceiling'];
    floor = json['floor'];
    open = json['open'];
    high = json['high'];
    low = json['low'];
    totalTrading = json['totalTrading'];
    totalTradingValue = json['totalTradingValue'];
    foreignBuyValue = json['foreignBuyValue'];
    foreignSellValue = json['foreignSellValue'];
    foreignNetBuyValue = json['foreignNetBuyValue'];
    foreignNetSellValue = json['foreignNetSellValue'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['symbol'] = symbol;
    data['fullName'] = fullName;
    data['marketCap'] = marketCap;
    data['closePrice'] = closePrice;
    data['reference'] = reference;
    data['stockPercentChange'] = stockPercentChange;
    data['ceiling'] = ceiling;
    data['floor'] = floor;
    data['open'] = open;
    data['high'] = high;
    data['low'] = low;
    data['totalTrading'] = totalTrading;
    data['totalTradingValue'] = totalTradingValue;
    data['foreignBuyValue'] = foreignBuyValue;
    data['foreignSellValue'] = foreignSellValue;
    data['foreignNetBuyValue'] = foreignNetBuyValue;
    data['foreignNetSellValue'] = foreignNetSellValue;
    return data;
  }

  Color get colorByPrice => closePrice != null
      ? CommonColorUtils.colorByPrice(
          currentPrice: closePrice ?? 0,
          referencePrice: (reference ?? 0).toDouble(),
          ceilingPrice: (ceiling ?? 0).toDouble(),
          floorPrice: (floor ?? 0).toDouble(),
        )
      : themeData.referenceColor;

  Color colorLowHigh(num? price) => price != null
      ? CommonColorUtils.colorByPrice(
          currentPrice: price,
          referencePrice: (reference ?? 0).toDouble(),
          ceilingPrice: (ceiling ?? 0).toDouble(),
          floorPrice: (floor ?? 0).toDouble(),
        )
      : themeData.gray500;
}
