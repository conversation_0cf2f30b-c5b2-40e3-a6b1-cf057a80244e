
import 'package:vp_market/data/enum/stock_helper.dart';

class MarketVolatilityEntity {
  double? indexPercentChange;
  int? totalTrade;
  int? totalVolume;
  int? totalValue;
  int advances;
  int declines;
  int noChange;
  int numberOfCe;
  int numberOfFl;
  String floorCode;

  int countStockPerchangeLevel0;
  int countStockPerchangeLevel1;
  int countStockPerchangeLevel2;
  int countStockPerchangeLevel3;
  int countStockPerchangeLevel4;
  int countStockPerchangeLevel5;
  int countStockPerchangeLevel6;
  int countStockPerchangeLevel7;
  int countStockPerchangeLevel8;
  int countStockPerchangeLevel9;
  int countStockPerchangeLevel10;

  StockFloor floor = StockFloor.hose;

  MarketVolatilityEntity({
    this.indexPercentChange,
    this.totalTrade,
    this.totalVolume,
    this.totalValue,
    required this.advances,
    required this.declines,
    required this.noChange,
    required this.numberOfCe,
    required this.numberOfFl,
    required this.floorCode,
    required this.countStockPerchangeLevel0,
    required this.countStockPerchangeLevel1,
    required this.countStockPerchangeLevel2,
    required this.countStockPerchangeLevel3,
    required this.countStockPerchangeLevel4,
    required this.countStockPerchangeLevel5,
    required this.countStockPerchangeLevel6,
    required this.countStockPerchangeLevel7,
    required this.countStockPerchangeLevel8,
    required this.countStockPerchangeLevel9,
    required this.countStockPerchangeLevel10,
  }) {
    final map = {
      '10': StockFloor.hose,
      '2': StockFloor.hnx,
      '4': StockFloor.upcom
    };
    floor = map[floorCode] ?? StockFloor.hose;
  }

  String getMaxIncrease() {
    final map = {
      StockFloor.hose.name: '>6%',
      StockFloor.hnx.name: '>9%',
      StockFloor.upcom.name: '>12%'
    };
    return map[floor.name] ?? '';
  }

  String getMaxReduce() {
    final map = {
      StockFloor.hose.name: '<-6%',
      StockFloor.hnx.name: '<-9%',
      StockFloor.upcom.name: '<-12%'
    };
    return map[floor.name] ?? '';
  }

  final listTitleHose = [
    '0%',
    '>6%',
    '>4% → 6%',
    '>2% → 4%',
    '>1% → 2%',
    '>0% → 1%',
    '(-1%) → 0%',
    '(-2%) → (-1%)',
    '(-4%) → (-2%)',
    '(-6%) → (-4%)',
    '<(-6%)'
  ];

  final listTitleHnx = [
    '0%',
    '>9%',
    '>6% → 9%',
    '>4% → 6%',
    '>2% → 4%',
    '>0% → 2%',
    '(-2%) → 0%',
    '(-4%) → (-2%)',
    '(-6%) → (-4%)',
    '(-9%) → (-6%)',
    '<(-9%)'
  ];

  final listTitleUpcom = [
    '0%',
    '>12%',
    '>8% → 12%',
    '>5% → 8%',
    '>2% → 5%',
    '>0% → 2%',
    '(-2%) → 0%',
    '(-5%) → (-2%)',
    '(-8%) → (-5%)',
    '(-12%) → (-8%)',
    '<(-12%)'
  ];

  String getTitleLevel(int index) {
    final map = {
      StockFloor.hose.name: listTitleHose,
      StockFloor.hnx.name: listTitleHnx,
      StockFloor.upcom.name: listTitleUpcom
    };
    final data = map[floor.name] ?? [];
    if (data.length > index) {
      return data[index];
    }
    return '';
  }
}
