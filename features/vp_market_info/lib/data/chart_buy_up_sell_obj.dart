import 'package:vp_market/data/chart_buy_up_sell_tranform_data.dart';

class ListChartBuyUpSellResponseObj {
  List<ChartBuyUpSellObj>? listS; // Lenh nho
  List<ChartBuyUpSellObj>? listM; // Lenh vua
  List<ChartBuyUpSellObj>? listL; // Lenh lon

  late ChartBuyUpSellTranformData? buyUpSellTranformData;

  ListChartBuyUpSellResponseObj({this.listS, this.listM, this.listL});

  ListChartBuyUpSellResponseObj.fromJson(Map<String, dynamic> json) {
    buyUpSellTranformData = ChartBuyUpSellTranformData();
    if (json['s'] != null) {
      List<ChartBuyUpSellObj> listValue = [];
      json['s'].forEach((v) {
        listValue.add(ChartBuyUpSellObj.fromJson(v));
      });
      listS = buyUpSellTranformData?.transfrom(listValue);
    }
    if (json['m'] != null) {
      List<ChartBuyUpSellObj> listValue = [];
      json['m'].forEach((v) {
        listValue.add(ChartBuyUpSellObj.fromJson(v));
      });
      listM = buyUpSellTranformData?.transfrom(listValue);
    }
    if (json['l'] != null) {
      List<ChartBuyUpSellObj> listValue = [];
      json['l'].forEach((v) {
        listValue.add(ChartBuyUpSellObj.fromJson(v));
      });
      listL = buyUpSellTranformData?.transfrom(listValue);
    }
  }
}

class ChartBuyUpSellObj {
  num? timeStamp;
  num? buyUpValue;
  num? sellDownValue;

  ChartBuyUpSellObj({this.timeStamp, this.buyUpValue, this.sellDownValue});

  ChartBuyUpSellObj.fromJson(Map<String, dynamic> json) {
    timeStamp = json['timeStamp'];
    buyUpValue = json['buyUpValue'];
    sellDownValue = json['sellDownValue'];
  }
}
