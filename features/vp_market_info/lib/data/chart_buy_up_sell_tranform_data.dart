import 'package:vp_market/data/chart_buy_up_sell_obj.dart';

class ChartBuyUpSellTranformData {
  List<ChartBuyUpSellObj> transfrom(List<ChartBuyUpSellObj> data) {
    List<ChartBuyUpSellObj> result = getListDefault(data);
    for (var obj in data) {
      final indexData = result.indexWhere(
        (element) => element.timeStamp == obj.timeStamp,
      );
      if (indexData >= 0) {
        result[indexData].buyUpValue = obj.buyUpValue;
        result[indexData].sellDownValue = obj.sellDownValue;
      }
    }
    return result;
  }

  List<ChartBuyUpSellObj> getListDefault(List<ChartBuyUpSellObj> data) {
    List<ChartBuyUpSellObj> listValue = [];
    DateTime currentDateTime = DateTime.now();
    if (data.isNotEmpty) {
      currentDateTime = DateTime.fromMillisecondsSinceEpoch(
        (data[0].timeStamp ?? 0).toInt() * 1000,
      );
    }

    // 9h -> 11h30
    final minTime = getTimeWithHour(currentDateTime, hour: 9);
    final maxTime1 = getTimeWithHour(currentDateTime, hour: 11, minutes: 30);
    listValue.add(
      ChartBuyUpSellObj(buyUpValue: 0, sellDownValue: 0, timeStamp: minTime),
    );
    while ((listValue.last.timeStamp ?? 0) > 0 &&
        (listValue.last.timeStamp ?? 0) < maxTime1) {
      listValue.add(getBuyUpSellObj(listValue.last.timeStamp ?? 0));
      if (listValue.length > 100) {
        break;
      }
    }

    // 13h -> 15h
    final minTime2 = getTimeWithHour(currentDateTime, hour: 13);
    final maxTime2 = getTimeWithHour(currentDateTime, hour: 15);
    listValue.add(
      ChartBuyUpSellObj(buyUpValue: 0, sellDownValue: 0, timeStamp: minTime2),
    );

    while ((listValue.last.timeStamp ?? 0) > 0 &&
        (listValue.last.timeStamp ?? 0) < maxTime2) {
      listValue.add(getBuyUpSellObj(listValue.last.timeStamp ?? 0));
      if (listValue.length > 100) {
        break;
      }
    }
    return listValue;
  }

  int getTimeWithHour(
    DateTime currentDateTime, {
    int hour = 0,
    int minutes = 0,
  }) {
    return DateTime(
          currentDateTime.year,
          currentDateTime.month,
          currentDateTime.day,
          hour,
          minutes,
        ).millisecondsSinceEpoch ~/
        1000;
  }

  ChartBuyUpSellObj getBuyUpSellObj(num value) {
    const offset = 300;
    return ChartBuyUpSellObj(
      buyUpValue: 0,
      sellDownValue: 0,
      timeStamp: value.toInt() + offset,
    );
  }
}
