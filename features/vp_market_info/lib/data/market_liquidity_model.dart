import 'package:vp_common/vp_common.dart';

class MarketLiquidityModel {
  int? time;
  num? totalValue;

  MarketLiquidityModel({this.time, this.totalValue});

  MarketLiquidityModel.fromJson(Map<String, dynamic> json) {
    time = json['time'];
    totalValue = json['totalValue'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['time'] = time;
    data['totalValue'] = totalValue;
    return data;
  }

  DateTime now = DateTime.now();

  DateTime? get dateTime => DateTime(
      now.year,
      now.month,
      now.day,
      dateTimeApi?.hour ?? 0,
      dateTimeApi?.minute ?? 0,
      dateTimeApi?.second ?? 0);

  DateTime? get dateTimeApi => time != null
      ? AppTimeUtils.parseDateTimeString(
          AppTimeUtils.timeFromTimeStamp(time!,
              format: AppTimeUtilsFormat.dateWithFullTime),
          format: AppTimeUtilsFormat.dateWithFullTime)
      : null;

  num get totalValueB => (totalValue ?? 0) / 1000000000;

}
