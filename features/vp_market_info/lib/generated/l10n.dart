// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class VPMarketInfoLocalize {
  VPMarketInfoLocalize();

  static VPMarketInfoLocalize? _current;

  static VPMarketInfoLocalize get current {
    assert(
      _current != null,
      'No instance of VPMarketInfoLocalize was loaded. Try to initialize the VPMarketInfoLocalize delegate before accessing VPMarketInfoLocalize.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<VPMarketInfoLocalize> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = VPMarketInfoLocalize();
      VPMarketInfoLocalize._current = instance;

      return instance;
    });
  }

  static VPMarketInfoLocalize of(BuildContext context) {
    final instance = VPMarketInfoLocalize.maybeOf(context);
    assert(
      instance != null,
      'No instance of VPMarketInfoLocalize present in the widget tree. Did you add VPMarketInfoLocalize.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static VPMarketInfoLocalize? maybeOf(BuildContext context) {
    return Localizations.of<VPMarketInfoLocalize>(
      context,
      VPMarketInfoLocalize,
    );
  }

  /// `Market information`
  String get market_info {
    return Intl.message(
      'Market information',
      name: 'market_info',
      desc: '',
      args: [],
    );
  }

  /// `Continuous`
  String get pbMarketStatusO {
    return Intl.message(
      'Continuous',
      name: 'pbMarketStatusO',
      desc: '',
      args: [],
    );
  }

  /// `Lunch break`
  String get pbMarketStatusI {
    return Intl.message(
      'Lunch break',
      name: 'pbMarketStatusI',
      desc: '',
      args: [],
    );
  }

  /// `Trading session`
  String get pbMarketStatusC {
    return Intl.message(
      'Trading session',
      name: 'pbMarketStatusC',
      desc: '',
      args: [],
    );
  }

  /// `ATO session`
  String get pbMarketStatusP {
    return Intl.message(
      'ATO session',
      name: 'pbMarketStatusP',
      desc: '',
      args: [],
    );
  }

  /// `ATC session`
  String get pbMarketStatusS {
    return Intl.message(
      'ATC session',
      name: 'pbMarketStatusS',
      desc: '',
      args: [],
    );
  }

  /// `PLO session`
  String get pbMarketStatus35 {
    return Intl.message(
      'PLO session',
      name: 'pbMarketStatus35',
      desc: '',
      args: [],
    );
  }

  /// `Closed`
  String get pbMarketStatusEND {
    return Intl.message(
      'Closed',
      name: 'pbMarketStatusEND',
      desc: '',
      args: [],
    );
  }

  /// `End of Session`
  String get pbMarketStatus6 {
    return Intl.message(
      'End of Session',
      name: 'pbMarketStatus6',
      desc: '',
      args: [],
    );
  }

  /// `Big`
  String get big {
    return Intl.message('Big', name: 'big', desc: '', args: []);
  }

  /// `Medium`
  String get medium {
    return Intl.message('Medium', name: 'medium', desc: '', args: []);
  }

  /// `Small`
  String get small {
    return Intl.message('Small', name: 'small', desc: '', args: []);
  }

  /// `All`
  String get all {
    return Intl.message('All', name: 'all', desc: '', args: []);
  }

  /// `Capitalization`
  String get capitalization {
    return Intl.message(
      'Capitalization',
      name: 'capitalization',
      desc: '',
      args: [],
    );
  }

  /// `Total trading value`
  String get totalTradingValue {
    return Intl.message(
      'Total trading value',
      name: 'totalTradingValue',
      desc: '',
      args: [],
    );
  }

  /// `Foreign net buy value`
  String get foreignNetBuyValue {
    return Intl.message(
      'Foreign net buy value',
      name: 'foreignNetBuyValue',
      desc: '',
      args: [],
    );
  }

  /// `Foreign net sell value`
  String get foreignNetSellValue {
    return Intl.message(
      'Foreign net sell value',
      name: 'foreignNetSellValue',
      desc: '',
      args: [],
    );
  }

  /// `Total trading value`
  String get totalTradingValue2 {
    return Intl.message(
      'Total trading value',
      name: 'totalTradingValue2',
      desc: '',
      args: [],
    );
  }

  /// `Foreign buy value`
  String get foreignBuyValue {
    return Intl.message(
      'Foreign buy value',
      name: 'foreignBuyValue',
      desc: '',
      args: [],
    );
  }

  /// `Foreign sell value`
  String get foreignSellValue {
    return Intl.message(
      'Foreign sell value',
      name: 'foreignSellValue',
      desc: '',
      args: [],
    );
  }

  /// `Branch`
  String get branch {
    return Intl.message('Branch', name: 'branch', desc: '', args: []);
  }

  /// `Fluctuations`
  String get fluctuations {
    return Intl.message(
      'Fluctuations',
      name: 'fluctuations',
      desc: '',
      args: [],
    );
  }

  /// `Price price`
  String get pricePrice {
    return Intl.message('Price price', name: 'pricePrice', desc: '', args: []);
  }

  /// `Highest price`
  String get highestPrice {
    return Intl.message(
      'Highest price',
      name: 'highestPrice',
      desc: '',
      args: [],
    );
  }

  /// `Lowest price`
  String get lowestPrice {
    return Intl.message(
      'Lowest price',
      name: 'lowestPrice',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get cancel {
    return Intl.message('Cancel', name: 'cancel', desc: '', args: []);
  }

  /// `No Data`
  String get noData {
    return Intl.message('No Data', name: 'noData', desc: '', args: []);
  }

  /// `Criteria`
  String get criteria {
    return Intl.message('Criteria', name: 'criteria', desc: '', args: []);
  }

  /// `Floor`
  String get priceFloor {
    return Intl.message('Floor', name: 'priceFloor', desc: '', args: []);
  }

  /// `Stock`
  String get stock {
    return Intl.message('Stock', name: 'stock', desc: '', args: []);
  }

  /// `Market info`
  String get marketInfo {
    return Intl.message('Market info', name: 'marketInfo', desc: '', args: []);
  }

  /// `Heatmap`
  String get heatmap2 {
    return Intl.message('Heatmap', name: 'heatmap2', desc: '', args: []);
  }

  /// `Failed to retrieve data, please try again`
  String get error {
    return Intl.message(
      'Failed to retrieve data, please try again',
      name: 'error',
      desc: '',
      args: [],
    );
  }

  /// `Retry`
  String get retry {
    return Intl.message('Retry', name: 'retry', desc: '', args: []);
  }

  /// `Overview`
  String get overview {
    return Intl.message('Overview', name: 'overview', desc: '', args: []);
  }

  /// `Top stocks`
  String get topStocks {
    return Intl.message('Top stocks', name: 'topStocks', desc: '', args: []);
  }

  /// `Heatmap`
  String get heatmap {
    return Intl.message('Heatmap', name: 'heatmap', desc: '', args: []);
  }

  /// `Day`
  String get day {
    return Intl.message('Day', name: 'day', desc: '', args: []);
  }

  /// `Minute`
  String get minute {
    return Intl.message('Minute', name: 'minute', desc: '', args: []);
  }

  /// `Hour`
  String get hour {
    return Intl.message('Hour', name: 'hour', desc: '', args: []);
  }

  /// `Cash flow analysis`
  String get cashFlowAnalysis {
    return Intl.message(
      'Cash flow analysis',
      name: 'cashFlowAnalysis',
      desc: '',
      args: [],
    );
  }

  /// `None tracsaction data`
  String get noneTransaction {
    return Intl.message(
      'None tracsaction data',
      name: 'noneTransaction',
      desc: '',
      args: [],
    );
  }

  /// `Cash flow in`
  String get cashFlowIn {
    return Intl.message('Cash flow in', name: 'cashFlowIn', desc: '', args: []);
  }

  /// `Proactive buy order`
  String get proactiveBuy {
    return Intl.message(
      'Proactive buy order',
      name: 'proactiveBuy',
      desc: '',
      args: [],
    );
  }

  /// `Proactive sell order`
  String get proactiveSell {
    return Intl.message(
      'Proactive sell order',
      name: 'proactiveSell',
      desc: '',
      args: [],
    );
  }

  /// `To day`
  String get today {
    return Intl.message('To day', name: 'today', desc: '', args: []);
  }

  /// `Yesterday`
  String get yesterday {
    return Intl.message('Yesterday', name: 'yesterday', desc: '', args: []);
  }

  /// `Average`
  String get average {
    return Intl.message('Average', name: 'average', desc: '', args: []);
  }

  /// `session`
  String get session {
    return Intl.message('session', name: 'session', desc: '', args: []);
  }

  /// `billion`
  String get bil {
    return Intl.message('billion', name: 'bil', desc: '', args: []);
  }

  /// `Average of`
  String get average2 {
    return Intl.message('Average of', name: 'average2', desc: '', args: []);
  }

  /// `Market liquidity`
  String get marketLiquidity {
    return Intl.message(
      'Market liquidity',
      name: 'marketLiquidity',
      desc: '',
      args: [],
    );
  }

  /// `Buy proactively`
  String get buyProactively {
    return Intl.message(
      'Buy proactively',
      name: 'buyProactively',
      desc: '',
      args: [],
    );
  }

  /// `Sell proactively`
  String get sellProactively {
    return Intl.message(
      'Sell proactively',
      name: 'sellProactively',
      desc: '',
      args: [],
    );
  }

  /// `Active buying/selling ratio`
  String get proactiveRate {
    return Intl.message(
      'Active buying/selling ratio',
      name: 'proactiveRate',
      desc: '',
      args: [],
    );
  }

  /// `Buy`
  String get buy {
    return Intl.message('Buy', name: 'buy', desc: '', args: []);
  }

  /// `Sell`
  String get sell {
    return Intl.message('Sell', name: 'sell', desc: '', args: []);
  }

  /// `Supply and demand during the day`
  String get supplyDemand {
    return Intl.message(
      'Supply and demand during the day',
      name: 'supplyDemand',
      desc: '',
      args: [],
    );
  }

  /// `Large order`
  String get largeOrder {
    return Intl.message('Large order', name: 'largeOrder', desc: '', args: []);
  }

  /// `Medium order`
  String get mediumOrder {
    return Intl.message(
      'Medium order',
      name: 'mediumOrder',
      desc: '',
      args: [],
    );
  }

  /// `Small order`
  String get smallOrder {
    return Intl.message('Small order', name: 'smallOrder', desc: '', args: []);
  }

  /// `Classification of transactions`
  String get classify {
    return Intl.message(
      'Classification of transactions',
      name: 'classify',
      desc: '',
      args: [],
    );
  }

  /// `Allocation according to stock volatility`
  String get marketStockVolatility {
    return Intl.message(
      'Allocation according to stock volatility',
      name: 'marketStockVolatility',
      desc: '',
      args: [],
    );
  }

  /// `Decrease Number`
  String get stockReduce {
    return Intl.message(
      'Decrease Number',
      name: 'stockReduce',
      desc: '',
      args: [],
    );
  }

  /// `Increases`
  String get stockIncrease {
    return Intl.message('Increases', name: 'stockIncrease', desc: '', args: []);
  }

  /// `Featured stocks`
  String get featuredStocks {
    return Intl.message(
      'Featured stocks',
      name: 'featuredStocks',
      desc: '',
      args: [],
    );
  }

  /// `Exchange`
  String get exchange {
    return Intl.message('Exchange', name: 'exchange', desc: '', args: []);
  }

  /// `Time`
  String get time {
    return Intl.message('Time', name: 'time', desc: '', args: []);
  }

  /// `Top transactions for foreign investors`
  String get topForeign {
    return Intl.message(
      'Top transactions for foreign investors',
      name: 'topForeign',
      desc: '',
      args: [],
    );
  }

  /// `Volume`
  String get volume {
    return Intl.message('Volume', name: 'volume', desc: '', args: []);
  }

  /// `Index`
  String get index {
    return Intl.message('Index', name: 'index', desc: '', args: []);
  }

  /// `TT`
  String get tt {
    return Intl.message('TT', name: 'tt', desc: '', args: []);
  }

  /// `TV`
  String get tv {
    return Intl.message('TV', name: 'tv', desc: '', args: []);
  }

  /// `TS`
  String get ts {
    return Intl.message('TS', name: 'ts', desc: '', args: []);
  }

  /// `thousand`
  String get k {
    return Intl.message('thousand', name: 'k', desc: '', args: []);
  }

  /// `million`
  String get mil {
    return Intl.message('million', name: 'mil', desc: '', args: []);
  }
}

class AppLocalizationDelegate
    extends LocalizationsDelegate<VPMarketInfoLocalize> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'vi'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<VPMarketInfoLocalize> load(Locale locale) =>
      VPMarketInfoLocalize.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
