// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "all": MessageLookupByLibrary.simpleMessage("All"),
    "average": MessageLookupByLibrary.simpleMessage("Average"),
    "average2": MessageLookupByLibrary.simpleMessage("Average of"),
    "big": MessageLookupByLibrary.simpleMessage("Big"),
    "bil": MessageLookupByLibrary.simpleMessage("billion"),
    "branch": MessageLookupByLibrary.simpleMessage("Branch"),
    "buy": MessageLookupByLibrary.simpleMessage("Buy"),
    "buyProactively": MessageLookupByLibrary.simpleMessage("Buy proactively"),
    "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "capitalization": MessageLookupByLibrary.simpleMessage("Capitalization"),
    "cashFlowAnalysis": MessageLookupByLibrary.simpleMessage(
      "Cash flow analysis",
    ),
    "cashFlowIn": MessageLookupByLibrary.simpleMessage("Cash flow in"),
    "classify": MessageLookupByLibrary.simpleMessage(
      "Classification of transactions",
    ),
    "criteria": MessageLookupByLibrary.simpleMessage("Criteria"),
    "day": MessageLookupByLibrary.simpleMessage("Day"),
    "error": MessageLookupByLibrary.simpleMessage(
      "Failed to retrieve data, please try again",
    ),
    "exchange": MessageLookupByLibrary.simpleMessage("Exchange"),
    "featuredStocks": MessageLookupByLibrary.simpleMessage("Featured stocks"),
    "fluctuations": MessageLookupByLibrary.simpleMessage("Fluctuations"),
    "foreignBuyValue": MessageLookupByLibrary.simpleMessage(
      "Foreign buy value",
    ),
    "foreignNetBuyValue": MessageLookupByLibrary.simpleMessage(
      "Foreign net buy value",
    ),
    "foreignNetSellValue": MessageLookupByLibrary.simpleMessage(
      "Foreign net sell value",
    ),
    "foreignSellValue": MessageLookupByLibrary.simpleMessage(
      "Foreign sell value",
    ),
    "heatmap": MessageLookupByLibrary.simpleMessage("Heatmap"),
    "heatmap2": MessageLookupByLibrary.simpleMessage("Heatmap"),
    "highestPrice": MessageLookupByLibrary.simpleMessage("Highest price"),
    "hour": MessageLookupByLibrary.simpleMessage("Hour"),
    "index": MessageLookupByLibrary.simpleMessage("Index"),
    "k": MessageLookupByLibrary.simpleMessage("thousand"),
    "largeOrder": MessageLookupByLibrary.simpleMessage("Large order"),
    "lowestPrice": MessageLookupByLibrary.simpleMessage("Lowest price"),
    "marketInfo": MessageLookupByLibrary.simpleMessage("Market info"),
    "marketLiquidity": MessageLookupByLibrary.simpleMessage("Market liquidity"),
    "marketStockVolatility": MessageLookupByLibrary.simpleMessage(
      "Allocation according to stock volatility",
    ),
    "market_info": MessageLookupByLibrary.simpleMessage("Market information"),
    "medium": MessageLookupByLibrary.simpleMessage("Medium"),
    "mediumOrder": MessageLookupByLibrary.simpleMessage("Medium order"),
    "mil": MessageLookupByLibrary.simpleMessage("million"),
    "minute": MessageLookupByLibrary.simpleMessage("Minute"),
    "noData": MessageLookupByLibrary.simpleMessage("No Data"),
    "noneTransaction": MessageLookupByLibrary.simpleMessage(
      "None tracsaction data",
    ),
    "overview": MessageLookupByLibrary.simpleMessage("Overview"),
    "pbMarketStatus35": MessageLookupByLibrary.simpleMessage("PLO session"),
    "pbMarketStatus6": MessageLookupByLibrary.simpleMessage("End of Session"),
    "pbMarketStatusC": MessageLookupByLibrary.simpleMessage("Trading session"),
    "pbMarketStatusEND": MessageLookupByLibrary.simpleMessage("Closed"),
    "pbMarketStatusI": MessageLookupByLibrary.simpleMessage("Lunch break"),
    "pbMarketStatusO": MessageLookupByLibrary.simpleMessage("Continuous"),
    "pbMarketStatusP": MessageLookupByLibrary.simpleMessage("ATO session"),
    "pbMarketStatusS": MessageLookupByLibrary.simpleMessage("ATC session"),
    "priceFloor": MessageLookupByLibrary.simpleMessage("Floor"),
    "pricePrice": MessageLookupByLibrary.simpleMessage("Price price"),
    "proactiveBuy": MessageLookupByLibrary.simpleMessage("Proactive buy order"),
    "proactiveRate": MessageLookupByLibrary.simpleMessage(
      "Active buying/selling ratio",
    ),
    "proactiveSell": MessageLookupByLibrary.simpleMessage(
      "Proactive sell order",
    ),
    "retry": MessageLookupByLibrary.simpleMessage("Retry"),
    "sell": MessageLookupByLibrary.simpleMessage("Sell"),
    "sellProactively": MessageLookupByLibrary.simpleMessage("Sell proactively"),
    "session": MessageLookupByLibrary.simpleMessage("session"),
    "small": MessageLookupByLibrary.simpleMessage("Small"),
    "smallOrder": MessageLookupByLibrary.simpleMessage("Small order"),
    "stock": MessageLookupByLibrary.simpleMessage("Stock"),
    "stockIncrease": MessageLookupByLibrary.simpleMessage("Increases"),
    "stockReduce": MessageLookupByLibrary.simpleMessage("Decrease Number"),
    "supplyDemand": MessageLookupByLibrary.simpleMessage(
      "Supply and demand during the day",
    ),
    "time": MessageLookupByLibrary.simpleMessage("Time"),
    "today": MessageLookupByLibrary.simpleMessage("To day"),
    "topForeign": MessageLookupByLibrary.simpleMessage(
      "Top transactions for foreign investors",
    ),
    "topStocks": MessageLookupByLibrary.simpleMessage("Top stocks"),
    "totalTradingValue": MessageLookupByLibrary.simpleMessage(
      "Total trading value",
    ),
    "totalTradingValue2": MessageLookupByLibrary.simpleMessage(
      "Total trading value",
    ),
    "ts": MessageLookupByLibrary.simpleMessage("TS"),
    "tt": MessageLookupByLibrary.simpleMessage("TT"),
    "tv": MessageLookupByLibrary.simpleMessage("TV"),
    "volume": MessageLookupByLibrary.simpleMessage("Volume"),
    "yesterday": MessageLookupByLibrary.simpleMessage("Yesterday"),
  };
}
