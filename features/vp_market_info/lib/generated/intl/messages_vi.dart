// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a vi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'vi';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "all": MessageLookupByLibrary.simpleMessage("Tất cả"),
    "average": MessageLookupByLibrary.simpleMessage("Trung bình"),
    "average2": MessageLookupByLibrary.simpleMessage("TB"),
    "big": MessageLookupByLibrary.simpleMessage("Lớn"),
    "bil": MessageLookupByLibrary.simpleMessage("tỷ"),
    "branch": MessageLookupByLibrary.simpleMessage("Ngành"),
    "buy": MessageLookupByLibrary.simpleMessage("Mua"),
    "buyProactively": MessageLookupByLibrary.simpleMessage("Mua chủ động"),
    "cancel": MessageLookupByLibrary.simpleMessage("Hủy"),
    "capitalization": MessageLookupByLibrary.simpleMessage("Vốn hoá"),
    "cashFlowAnalysis": MessageLookupByLibrary.simpleMessage(
      "Phân tích dòng tiền",
    ),
    "cashFlowIn": MessageLookupByLibrary.simpleMessage("Dòng tiền trong"),
    "classify": MessageLookupByLibrary.simpleMessage("Phân loại giao dịch"),
    "criteria": MessageLookupByLibrary.simpleMessage("Tiêu chí"),
    "day": MessageLookupByLibrary.simpleMessage("Ngày"),
    "error": MessageLookupByLibrary.simpleMessage(
      "Không lấy được dữ liệu, vui lòng thử lại",
    ),
    "exchange": MessageLookupByLibrary.simpleMessage("Sàn"),
    "featuredStocks": MessageLookupByLibrary.simpleMessage(
      "Top Cổ phiếu nổi bật",
    ),
    "fluctuations": MessageLookupByLibrary.simpleMessage("Biến động"),
    "foreignBuyValue": MessageLookupByLibrary.simpleMessage("Giá trị NN mua"),
    "foreignNetBuyValue": MessageLookupByLibrary.simpleMessage(
      "Giá trị NN mua ròng",
    ),
    "foreignNetSellValue": MessageLookupByLibrary.simpleMessage(
      "Giá trị NN bán ròng",
    ),
    "foreignSellValue": MessageLookupByLibrary.simpleMessage("Giá trị NN bán"),
    "heatmap": MessageLookupByLibrary.simpleMessage("Heatmap"),
    "heatmap2": MessageLookupByLibrary.simpleMessage("Bản đồ nhiệt"),
    "highestPrice": MessageLookupByLibrary.simpleMessage("Giá cao nhất"),
    "hour": MessageLookupByLibrary.simpleMessage("Giờ"),
    "index": MessageLookupByLibrary.simpleMessage("Chỉ số"),
    "k": MessageLookupByLibrary.simpleMessage("nghìn"),
    "largeOrder": MessageLookupByLibrary.simpleMessage("Lệnh lớn"),
    "lowestPrice": MessageLookupByLibrary.simpleMessage("Giá thấp nhất"),
    "marketInfo": MessageLookupByLibrary.simpleMessage("Thông tin thị trường"),
    "marketLiquidity": MessageLookupByLibrary.simpleMessage(
      "Thanh khoản thị trường",
    ),
    "marketStockVolatility": MessageLookupByLibrary.simpleMessage(
      "Phân bổ theo biến động mã cổ phiếu",
    ),
    "market_info": MessageLookupByLibrary.simpleMessage("Thông tin thị trường"),
    "medium": MessageLookupByLibrary.simpleMessage("Trung bình"),
    "mediumOrder": MessageLookupByLibrary.simpleMessage("Lệnh TB"),
    "mil": MessageLookupByLibrary.simpleMessage("triệu"),
    "minute": MessageLookupByLibrary.simpleMessage("Phút"),
    "noData": MessageLookupByLibrary.simpleMessage("Không có dữ liệu"),
    "noneTransaction": MessageLookupByLibrary.simpleMessage(
      "Không có dữ liệu giao dịch",
    ),
    "overview": MessageLookupByLibrary.simpleMessage("Tổng quan"),
    "pbMarketStatus35": MessageLookupByLibrary.simpleMessage("Phiên PLO"),
    "pbMarketStatus6": MessageLookupByLibrary.simpleMessage("Cuối phiên"),
    "pbMarketStatusC": MessageLookupByLibrary.simpleMessage("Phiên GDTT"),
    "pbMarketStatusEND": MessageLookupByLibrary.simpleMessage("Đóng cửa"),
    "pbMarketStatusI": MessageLookupByLibrary.simpleMessage("Nghỉ trưa"),
    "pbMarketStatusO": MessageLookupByLibrary.simpleMessage("Liên tục"),
    "pbMarketStatusP": MessageLookupByLibrary.simpleMessage("Phiên ATO"),
    "pbMarketStatusS": MessageLookupByLibrary.simpleMessage("Phiên ATC"),
    "priceFloor": MessageLookupByLibrary.simpleMessage("Sàn"),
    "pricePrice": MessageLookupByLibrary.simpleMessage("Giá khớp"),
    "proactiveBuy": MessageLookupByLibrary.simpleMessage("Lệnh mua chủ động"),
    "proactiveRate": MessageLookupByLibrary.simpleMessage(
      "Tỷ lệ Mua/Bán chủ động",
    ),
    "proactiveSell": MessageLookupByLibrary.simpleMessage("Lệnh bán chủ động"),
    "retry": MessageLookupByLibrary.simpleMessage("Thử lại"),
    "sell": MessageLookupByLibrary.simpleMessage("Bán"),
    "sellProactively": MessageLookupByLibrary.simpleMessage("Bán chủ động"),
    "session": MessageLookupByLibrary.simpleMessage("phiên"),
    "small": MessageLookupByLibrary.simpleMessage("Nhỏ"),
    "smallOrder": MessageLookupByLibrary.simpleMessage("Lệnh nhỏ"),
    "stock": MessageLookupByLibrary.simpleMessage("Stock"),
    "stockIncrease": MessageLookupByLibrary.simpleMessage("Số mã tăng"),
    "stockReduce": MessageLookupByLibrary.simpleMessage("Số mã giảm"),
    "supplyDemand": MessageLookupByLibrary.simpleMessage("Cung cầu trong ngày"),
    "time": MessageLookupByLibrary.simpleMessage("Thời gian"),
    "today": MessageLookupByLibrary.simpleMessage("Hôm nay"),
    "topForeign": MessageLookupByLibrary.simpleMessage(
      "Top giao dịch nhà đầu tư nước ngoài",
    ),
    "topStocks": MessageLookupByLibrary.simpleMessage("Top cổ phiếu"),
    "totalTradingValue": MessageLookupByLibrary.simpleMessage("Tổng GTGD"),
    "totalTradingValue2": MessageLookupByLibrary.simpleMessage(
      "Tổng giá trị GD",
    ),
    "ts": MessageLookupByLibrary.simpleMessage("Trạng thái GD"),
    "tt": MessageLookupByLibrary.simpleMessage("KLGD"),
    "tv": MessageLookupByLibrary.simpleMessage("GTGD"),
    "volume": MessageLookupByLibrary.simpleMessage("Khối lượng"),
    "yesterday": MessageLookupByLibrary.simpleMessage("Hôm qua"),
  };
}
