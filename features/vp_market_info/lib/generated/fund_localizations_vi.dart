// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'fund_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Vietnamese (`vi`).
class AppLocalizationsVi extends AppLocalizations {
  AppLocalizationsVi([String locale = 'vi']) : super(locale);

  @override
  String get market_info => 'Thông tin thị trường';

  @override
  String get pbMarketStatusO => 'Liên tục';

  @override
  String get pbMarketStatusI => 'Nghỉ trưa';

  @override
  String get pbMarketStatusC => 'Phiên GDTT';

  @override
  String get pbMarketStatusP => 'Phiên ATO';

  @override
  String get pbMarketStatusS => 'Phiên ATC';

  @override
  String get pbMarketStatus35 => 'Phiên PLO';

  @override
  String get pbMarketStatusEND => 'Đóng cửa';

  @override
  String get pbMarketStatus6 => 'Cuối phiên';

  @override
  String get big => 'Lớn';

  @override
  String get medium => 'Trung bình';

  @override
  String get small => 'Nhỏ';

  @override
  String get all => 'Tất cả';

  @override
  String get capitalization => 'Vốn hoá';

  @override
  String get totalTradingValue => 'Tổng GTGD';

  @override
  String get foreignNetBuyValue => 'Giá trị NN mua ròng';

  @override
  String get foreignNetSellValue => 'Giá trị NN bán ròng';

  @override
  String get totalTradingValue2 => 'Tổng giá trị GD';

  @override
  String get foreignBuyValue => 'Giá trị NN mua';

  @override
  String get foreignSellValue => 'Giá trị NN bán';

  @override
  String get branch => 'Ngành';

  @override
  String get fluctuations => 'Biến động';

  @override
  String get pricePrice => 'Giá khớp';

  @override
  String get highestPrice => 'Giá cao nhất';

  @override
  String get lowestPrice => 'Giá thấp nhất';

  @override
  String get cancel => 'Hủy';

  @override
  String get noData => 'Không có dữ liệu';

  @override
  String get criteria => 'Tiêu chí';

  @override
  String get priceFloor => 'Sàn';

  @override
  String get stock => 'Stock';

  @override
  String get marketInfo => 'Thông tin thị trường';

  @override
  String get heatmap2 => 'Bản đồ nhiệt';

  @override
  String get error => 'Không lấy được dữ liệu, vui lòng thử lại';

  @override
  String get retry => 'Thử lại';

  @override
  String get overview => 'Tổng quan';

  @override
  String get topStocks => 'Top cổ phiếu';

  @override
  String get heatmap => 'Heatmap';

  @override
  String get day => 'Ngày';

  @override
  String get minute => 'Phút';

  @override
  String get hour => 'Giờ';

  @override
  String get cashFlowAnalysis => 'Phân tích dòng tiền';

  @override
  String get noneTransaction => 'Không có dữ liệu giao dịch';

  @override
  String get cashFlowIn => 'Dòng tiền trong';

  @override
  String get proactiveBuy => 'Lệnh mua chủ động';

  @override
  String get proactiveSell => 'Lệnh bán chủ động';

  @override
  String get today => 'Hôm nay';

  @override
  String get yesterday => 'Hôm qua';

  @override
  String get average => 'Trung bình';

  @override
  String get session => 'phiên';

  @override
  String get bil => 'tỷ';

  @override
  String get average2 => 'TB';

  @override
  String get marketLiquidity => 'Thanh khoản thị trường';

  @override
  String get buyProactively => 'Mua chủ động';

  @override
  String get sellProactively => 'Bán chủ động';

  @override
  String get proactiveRate => 'Tỷ lệ Mua/Bán chủ động';

  @override
  String get buy => 'Mua';

  @override
  String get sell => 'Bán';

  @override
  String get supplyDemand => 'Cung cầu trong ngày';

  @override
  String get largeOrder => 'Lệnh lớn';

  @override
  String get mediumOrder => 'Lệnh TB';

  @override
  String get smallOrder => 'Lệnh nhỏ';

  @override
  String get classify => 'Phân loại giao dịch';

  @override
  String get marketStockVolatility => 'Phân bổ theo biến động mã cổ phiếu';

  @override
  String get stockReduce => 'Số mã giảm';

  @override
  String get stockIncrease => 'Số mã tăng';

  @override
  String get featuredStocks => 'Top Cổ phiếu nổi bật';

  @override
  String get exchange => 'Sàn';

  @override
  String get time => 'Thời gian';

  @override
  String get topForeign => 'Top giao dịch nhà đầu tư nước ngoài';

  @override
  String get volume => 'Khối lượng';

  @override
  String get index => 'Chỉ số';

  @override
  String get tt => 'KLGD';

  @override
  String get tv => 'GTGD';

  @override
  String get ts => 'Trạng thái GD';

  @override
  String get k => 'nghìn';

  @override
  String get mil => 'triệu';
}
