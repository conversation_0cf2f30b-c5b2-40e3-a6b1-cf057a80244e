// dart format width=80

/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: deprecated_member_use,directives_ordering,implicit_dynamic_list_literal,unnecessary_import

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/arrow_drop.svg
  SvgGenImage get arrowDrop => const SvgGenImage('assets/icons/arrow_drop.svg');

  /// File path: assets/icons/arrow_up.svg
  SvgGenImage get arrowUp => const SvgGenImage('assets/icons/arrow_up.svg');

  /// File path: assets/icons/ic_alt_arrow_up.svg
  SvgGenImage get icAltArrowUp =>
      const SvgGenImage('assets/icons/ic_alt_arrow_up.svg');

  /// File path: assets/icons/ic_arrow_decrease.svg
  SvgGenImage get icArrowDecrease =>
      const SvgGenImage('assets/icons/ic_arrow_decrease.svg');

  /// File path: assets/icons/ic_arrow_down.svg
  SvgGenImage get icArrowDown =>
      const SvgGenImage('assets/icons/ic_arrow_down.svg');

  /// File path: assets/icons/ic_arrow_increase.svg
  SvgGenImage get icArrowIncrease =>
      const SvgGenImage('assets/icons/ic_arrow_increase.svg');

  /// File path: assets/icons/ic_arrow_up.svg
  SvgGenImage get icArrowUp =>
      const SvgGenImage('assets/icons/ic_arrow_up.svg');

  /// File path: assets/icons/ic_circle.svg
  SvgGenImage get icCircle => const SvgGenImage('assets/icons/ic_circle.svg');

  /// File path: assets/icons/ic_dot.svg
  SvgGenImage get icDot => const SvgGenImage('assets/icons/ic_dot.svg');

  /// File path: assets/icons/ic_down.svg
  SvgGenImage get icDown => const SvgGenImage('assets/icons/ic_down.svg');

  /// File path: assets/icons/ic_heatmap.svg
  SvgGenImage get icHeatmap => const SvgGenImage('assets/icons/ic_heatmap.svg');

  /// File path: assets/icons/ic_heatmap2.svg
  SvgGenImage get icHeatmap2 =>
      const SvgGenImage('assets/icons/ic_heatmap2.svg');

  /// File path: assets/icons/ic_open.svg
  SvgGenImage get icOpen => const SvgGenImage('assets/icons/ic_open.svg');

  /// File path: assets/icons/ic_overview.svg
  SvgGenImage get icOverview =>
      const SvgGenImage('assets/icons/ic_overview.svg');

  /// File path: assets/icons/ic_overview2.svg
  SvgGenImage get icOverview2 =>
      const SvgGenImage('assets/icons/ic_overview2.svg');

  /// File path: assets/icons/ic_price_chart.svg
  SvgGenImage get icPriceChart =>
      const SvgGenImage('assets/icons/ic_price_chart.svg');

  /// File path: assets/icons/ic_top_stock.svg
  SvgGenImage get icTopStock =>
      const SvgGenImage('assets/icons/ic_top_stock.svg');

  /// File path: assets/icons/ic_top_stock2.svg
  SvgGenImage get icTopStock2 =>
      const SvgGenImage('assets/icons/ic_top_stock2.svg');

  /// File path: assets/icons/ic_trading_view.svg
  SvgGenImage get icTradingView =>
      const SvgGenImage('assets/icons/ic_trading_view.svg');

  /// File path: assets/icons/ic_up.svg
  SvgGenImage get icUp => const SvgGenImage('assets/icons/ic_up.svg');

  /// File path: assets/icons/im_no_content.svg
  SvgGenImage get imNoContent =>
      const SvgGenImage('assets/icons/im_no_content.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    arrowDrop,
    arrowUp,
    icAltArrowUp,
    icArrowDecrease,
    icArrowDown,
    icArrowIncrease,
    icArrowUp,
    icCircle,
    icDot,
    icDown,
    icHeatmap,
    icHeatmap2,
    icOpen,
    icOverview,
    icOverview2,
    icPriceChart,
    icTopStock,
    icTopStock2,
    icTradingView,
    icUp,
    imNoContent,
  ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/error.png
  AssetGenImage get error => const AssetGenImage('assets/images/error.png');

  /// File path: assets/images/no_transaction.png
  AssetGenImage get noTransaction =>
      const AssetGenImage('assets/images/no_transaction.png');

  /// List of all assets
  List<AssetGenImage> get values => [error, noTransaction];
}

class VpMarketAssets {
  const VpMarketAssets._();

  static const String package = 'vp_market';

  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
    this.animation,
  });

  final String _assetName;

  static const String package = 'vp_market';

  final Size? size;
  final Set<String> flavors;
  final AssetGenImageAnimation? animation;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
  }) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => 'packages/vp_market/$_assetName';
}

class AssetGenImageAnimation {
  const AssetGenImageAnimation({
    required this.isAnimation,
    required this.duration,
    required this.frames,
  });

  final bool isAnimation;
  final Duration duration;
  final int frames;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  static const String package = 'vp_market';

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    _svg.ColorMapper? colorMapper,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
        colorMapper: colorMapper,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter:
          colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => 'packages/vp_market/$_assetName';
}
