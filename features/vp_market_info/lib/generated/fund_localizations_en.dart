// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'fund_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get market_info => 'Market information';

  @override
  String get pbMarketStatusO => 'Continuous';

  @override
  String get pbMarketStatusI => 'Lunch break';

  @override
  String get pbMarketStatusC => 'Trading session';

  @override
  String get pbMarketStatusP => 'ATO session';

  @override
  String get pbMarketStatusS => 'ATC session';

  @override
  String get pbMarketStatus35 => 'PLO session';

  @override
  String get pbMarketStatusEND => 'Closed';

  @override
  String get pbMarketStatus6 => 'End of Session';

  @override
  String get big => 'Big';

  @override
  String get medium => 'Medium';

  @override
  String get small => 'Small';

  @override
  String get all => 'All';

  @override
  String get capitalization => 'Capitalization';

  @override
  String get totalTradingValue => 'Total trading value';

  @override
  String get foreignNetBuyValue => 'Foreign net buy value';

  @override
  String get foreignNetSellValue => 'Foreign net sell value';

  @override
  String get totalTradingValue2 => 'Total trading value';

  @override
  String get foreignBuyValue => 'Foreign buy value';

  @override
  String get foreignSellValue => 'Foreign sell value';

  @override
  String get branch => 'Branch';

  @override
  String get fluctuations => 'Fluctuations';

  @override
  String get pricePrice => 'Price price';

  @override
  String get highestPrice => 'Highest price';

  @override
  String get lowestPrice => 'Lowest price';

  @override
  String get cancel => 'Cancel';

  @override
  String get noData => 'No Data';

  @override
  String get criteria => 'Criteria';

  @override
  String get priceFloor => 'Floor';

  @override
  String get stock => 'Stock';

  @override
  String get marketInfo => 'Market info';

  @override
  String get heatmap2 => 'Heatmap';

  @override
  String get error => 'Failed to retrieve data, please try again';

  @override
  String get retry => 'Retry';

  @override
  String get overview => 'Overview';

  @override
  String get topStocks => 'Top stocks';

  @override
  String get heatmap => 'Heatmap';

  @override
  String get day => 'Day';

  @override
  String get minute => 'Minute';

  @override
  String get hour => 'Hour';

  @override
  String get cashFlowAnalysis => 'Cash flow analysis';

  @override
  String get noneTransaction => 'None tracsaction data';

  @override
  String get cashFlowIn => 'Cash flow in';

  @override
  String get proactiveBuy => 'Proactive buy order';

  @override
  String get proactiveSell => 'Proactive sell order';

  @override
  String get today => 'To day';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get average => 'Average';

  @override
  String get session => 'session';

  @override
  String get bil => 'billion';

  @override
  String get average2 => 'Average of';

  @override
  String get marketLiquidity => 'Market liquidity';

  @override
  String get buyProactively => 'Buy proactively';

  @override
  String get sellProactively => 'Sell proactively';

  @override
  String get proactiveRate => 'Active buying/selling ratio';

  @override
  String get buy => 'Buy';

  @override
  String get sell => 'Sell';

  @override
  String get supplyDemand => 'Supply and demand during the day';

  @override
  String get largeOrder => 'Large order';

  @override
  String get mediumOrder => 'Medium order';

  @override
  String get smallOrder => 'Small order';

  @override
  String get classify => 'Classification of transactions';

  @override
  String get marketStockVolatility => 'Allocation according to stock volatility';

  @override
  String get stockReduce => 'Decrease Number';

  @override
  String get stockIncrease => 'Increases';

  @override
  String get featuredStocks => 'Featured stocks';

  @override
  String get exchange => 'Exchange';

  @override
  String get time => 'Time';

  @override
  String get topForeign => 'Top transactions for foreign investors';

  @override
  String get volume => 'Volume';

  @override
  String get index => 'Index';

  @override
  String get tt => 'TT';

  @override
  String get tv => 'TV';

  @override
  String get ts => 'TS';

  @override
  String get k => 'thousand';

  @override
  String get mil => 'million';
}
