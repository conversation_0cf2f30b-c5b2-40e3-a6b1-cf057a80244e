import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'fund_localizations_en.dart';
import 'fund_localizations_vi.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/fund_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('vi')
  ];

  /// No description provided for @market_info.
  ///
  /// In en, this message translates to:
  /// **'Market information'**
  String get market_info;

  /// No description provided for @pbMarketStatusO.
  ///
  /// In en, this message translates to:
  /// **'Continuous'**
  String get pbMarketStatusO;

  /// No description provided for @pbMarketStatusI.
  ///
  /// In en, this message translates to:
  /// **'Lunch break'**
  String get pbMarketStatusI;

  /// No description provided for @pbMarketStatusC.
  ///
  /// In en, this message translates to:
  /// **'Trading session'**
  String get pbMarketStatusC;

  /// No description provided for @pbMarketStatusP.
  ///
  /// In en, this message translates to:
  /// **'ATO session'**
  String get pbMarketStatusP;

  /// No description provided for @pbMarketStatusS.
  ///
  /// In en, this message translates to:
  /// **'ATC session'**
  String get pbMarketStatusS;

  /// No description provided for @pbMarketStatus35.
  ///
  /// In en, this message translates to:
  /// **'PLO session'**
  String get pbMarketStatus35;

  /// No description provided for @pbMarketStatusEND.
  ///
  /// In en, this message translates to:
  /// **'Closed'**
  String get pbMarketStatusEND;

  /// No description provided for @pbMarketStatus6.
  ///
  /// In en, this message translates to:
  /// **'End of Session'**
  String get pbMarketStatus6;

  /// No description provided for @big.
  ///
  /// In en, this message translates to:
  /// **'Big'**
  String get big;

  /// No description provided for @medium.
  ///
  /// In en, this message translates to:
  /// **'Medium'**
  String get medium;

  /// No description provided for @small.
  ///
  /// In en, this message translates to:
  /// **'Small'**
  String get small;

  /// No description provided for @all.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// No description provided for @capitalization.
  ///
  /// In en, this message translates to:
  /// **'Capitalization'**
  String get capitalization;

  /// No description provided for @totalTradingValue.
  ///
  /// In en, this message translates to:
  /// **'Total trading value'**
  String get totalTradingValue;

  /// No description provided for @foreignNetBuyValue.
  ///
  /// In en, this message translates to:
  /// **'Foreign net buy value'**
  String get foreignNetBuyValue;

  /// No description provided for @foreignNetSellValue.
  ///
  /// In en, this message translates to:
  /// **'Foreign net sell value'**
  String get foreignNetSellValue;

  /// No description provided for @totalTradingValue2.
  ///
  /// In en, this message translates to:
  /// **'Total trading value'**
  String get totalTradingValue2;

  /// No description provided for @foreignBuyValue.
  ///
  /// In en, this message translates to:
  /// **'Foreign buy value'**
  String get foreignBuyValue;

  /// No description provided for @foreignSellValue.
  ///
  /// In en, this message translates to:
  /// **'Foreign sell value'**
  String get foreignSellValue;

  /// No description provided for @branch.
  ///
  /// In en, this message translates to:
  /// **'Branch'**
  String get branch;

  /// No description provided for @fluctuations.
  ///
  /// In en, this message translates to:
  /// **'Fluctuations'**
  String get fluctuations;

  /// No description provided for @pricePrice.
  ///
  /// In en, this message translates to:
  /// **'Price price'**
  String get pricePrice;

  /// No description provided for @highestPrice.
  ///
  /// In en, this message translates to:
  /// **'Highest price'**
  String get highestPrice;

  /// No description provided for @lowestPrice.
  ///
  /// In en, this message translates to:
  /// **'Lowest price'**
  String get lowestPrice;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @noData.
  ///
  /// In en, this message translates to:
  /// **'No Data'**
  String get noData;

  /// No description provided for @criteria.
  ///
  /// In en, this message translates to:
  /// **'Criteria'**
  String get criteria;

  /// No description provided for @priceFloor.
  ///
  /// In en, this message translates to:
  /// **'Floor'**
  String get priceFloor;

  /// No description provided for @stock.
  ///
  /// In en, this message translates to:
  /// **'Stock'**
  String get stock;

  /// No description provided for @marketInfo.
  ///
  /// In en, this message translates to:
  /// **'Market info'**
  String get marketInfo;

  /// No description provided for @heatmap2.
  ///
  /// In en, this message translates to:
  /// **'Heatmap'**
  String get heatmap2;

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'Failed to retrieve data, please try again'**
  String get error;

  /// No description provided for @retry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// No description provided for @overview.
  ///
  /// In en, this message translates to:
  /// **'Overview'**
  String get overview;

  /// No description provided for @topStocks.
  ///
  /// In en, this message translates to:
  /// **'Top stocks'**
  String get topStocks;

  /// No description provided for @heatmap.
  ///
  /// In en, this message translates to:
  /// **'Heatmap'**
  String get heatmap;

  /// No description provided for @day.
  ///
  /// In en, this message translates to:
  /// **'Day'**
  String get day;

  /// No description provided for @minute.
  ///
  /// In en, this message translates to:
  /// **'Minute'**
  String get minute;

  /// No description provided for @hour.
  ///
  /// In en, this message translates to:
  /// **'Hour'**
  String get hour;

  /// No description provided for @cashFlowAnalysis.
  ///
  /// In en, this message translates to:
  /// **'Cash flow analysis'**
  String get cashFlowAnalysis;

  /// No description provided for @noneTransaction.
  ///
  /// In en, this message translates to:
  /// **'None tracsaction data'**
  String get noneTransaction;

  /// No description provided for @cashFlowIn.
  ///
  /// In en, this message translates to:
  /// **'Cash flow in'**
  String get cashFlowIn;

  /// No description provided for @proactiveBuy.
  ///
  /// In en, this message translates to:
  /// **'Proactive buy order'**
  String get proactiveBuy;

  /// No description provided for @proactiveSell.
  ///
  /// In en, this message translates to:
  /// **'Proactive sell order'**
  String get proactiveSell;

  /// No description provided for @today.
  ///
  /// In en, this message translates to:
  /// **'To day'**
  String get today;

  /// No description provided for @yesterday.
  ///
  /// In en, this message translates to:
  /// **'Yesterday'**
  String get yesterday;

  /// No description provided for @average.
  ///
  /// In en, this message translates to:
  /// **'Average'**
  String get average;

  /// No description provided for @session.
  ///
  /// In en, this message translates to:
  /// **'session'**
  String get session;

  /// No description provided for @bil.
  ///
  /// In en, this message translates to:
  /// **'billion'**
  String get bil;

  /// No description provided for @average2.
  ///
  /// In en, this message translates to:
  /// **'Average of'**
  String get average2;

  /// No description provided for @marketLiquidity.
  ///
  /// In en, this message translates to:
  /// **'Market liquidity'**
  String get marketLiquidity;

  /// No description provided for @buyProactively.
  ///
  /// In en, this message translates to:
  /// **'Buy proactively'**
  String get buyProactively;

  /// No description provided for @sellProactively.
  ///
  /// In en, this message translates to:
  /// **'Sell proactively'**
  String get sellProactively;

  /// No description provided for @proactiveRate.
  ///
  /// In en, this message translates to:
  /// **'Active buying/selling ratio'**
  String get proactiveRate;

  /// No description provided for @buy.
  ///
  /// In en, this message translates to:
  /// **'Buy'**
  String get buy;

  /// No description provided for @sell.
  ///
  /// In en, this message translates to:
  /// **'Sell'**
  String get sell;

  /// No description provided for @supplyDemand.
  ///
  /// In en, this message translates to:
  /// **'Supply and demand during the day'**
  String get supplyDemand;

  /// No description provided for @largeOrder.
  ///
  /// In en, this message translates to:
  /// **'Large order'**
  String get largeOrder;

  /// No description provided for @mediumOrder.
  ///
  /// In en, this message translates to:
  /// **'Medium order'**
  String get mediumOrder;

  /// No description provided for @smallOrder.
  ///
  /// In en, this message translates to:
  /// **'Small order'**
  String get smallOrder;

  /// No description provided for @classify.
  ///
  /// In en, this message translates to:
  /// **'Classification of transactions'**
  String get classify;

  /// No description provided for @marketStockVolatility.
  ///
  /// In en, this message translates to:
  /// **'Allocation according to stock volatility'**
  String get marketStockVolatility;

  /// No description provided for @stockReduce.
  ///
  /// In en, this message translates to:
  /// **'Decrease Number'**
  String get stockReduce;

  /// No description provided for @stockIncrease.
  ///
  /// In en, this message translates to:
  /// **'Increases'**
  String get stockIncrease;

  /// No description provided for @featuredStocks.
  ///
  /// In en, this message translates to:
  /// **'Featured stocks'**
  String get featuredStocks;

  /// No description provided for @exchange.
  ///
  /// In en, this message translates to:
  /// **'Exchange'**
  String get exchange;

  /// No description provided for @time.
  ///
  /// In en, this message translates to:
  /// **'Time'**
  String get time;

  /// No description provided for @topForeign.
  ///
  /// In en, this message translates to:
  /// **'Top transactions for foreign investors'**
  String get topForeign;

  /// No description provided for @volume.
  ///
  /// In en, this message translates to:
  /// **'Volume'**
  String get volume;

  /// No description provided for @index.
  ///
  /// In en, this message translates to:
  /// **'Index'**
  String get index;

  /// No description provided for @tt.
  ///
  /// In en, this message translates to:
  /// **'TT'**
  String get tt;

  /// No description provided for @tv.
  ///
  /// In en, this message translates to:
  /// **'TV'**
  String get tv;

  /// No description provided for @ts.
  ///
  /// In en, this message translates to:
  /// **'TS'**
  String get ts;

  /// No description provided for @k.
  ///
  /// In en, this message translates to:
  /// **'thousand'**
  String get k;

  /// No description provided for @mil.
  ///
  /// In en, this message translates to:
  /// **'million'**
  String get mil;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'vi'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return AppLocalizationsEn();
    case 'vi': return AppLocalizationsVi();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
