import 'dart:io';

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:vp_common/constants/app_constants.dart';
import 'package:vp_common/extensions/num_extensions.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/core/constant/stock_value_constans.dart';
import 'package:vp_wealth/data/utils/wealth_constants.dart';
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/lang/stock_localized_values.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/place_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/utils/ext.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/domain/entity/stock_portfolio_entity.dart';

class StockUtils {
  static String getSubAccountName(BuildContext context, String? accountId) {
    int index = GetIt.instance.get<SubAccountCubit>().subAccountsStock.indexWhere(
      (element) => element.id == accountId,
    );
    if (index >= 0) {
      return 'vi' == AppConstants.vi
          ? GetIt.instance.get<SubAccountCubit>().subAccountsStock[index].afAcctNoExt!
          : GetIt.instance.get<SubAccountCubit>().subAccountsStock[index].enafAcctNoext!;
    } else {
      return "";
    }
  }

  static getSubAccount(BuildContext context, String? accountId) {
    int index = GetIt.instance.get<SubAccountCubit>().subAccountsStock.indexWhere(
      (element) => element.id == accountId,
    );
    String name = '';
    if (index >= 0) {
      name =
          'vi' == AppConstants.vi
              ? GetIt.instance.get<SubAccountCubit>().subAccountsStock[index].afAcctNoExt!
              : GetIt.instance.get<SubAccountCubit>().subAccountsStock[index].enafAcctNoext!;
      return 'subAccount ${name.toLowerCase()}';
    } else {
      return "";
    }
  }

  static getTitleSubAccount(
    BuildContext context,
    SubAccountModel? subAccountModel,
  ) {
    String titleFromAPI = (subAccountModel?.afAcctNoExt ?? '').toLowerCase();
    return 'subAccount $titleFromAPI';
  }

  static checkStep({
    required String? exchange,
    required num price,
    String? stockType,
  }) {
    if (stockType.isCW) {
      return price % 10.0 != 0.0;
    }
    if (stockType.isETF) {
      return price % (exchange == StockAppConstants.hsx ? 10.0 : 1.0) != 0.0;
    }
    if (exchange == StockAppConstants.hsx) {
      return price % price.stepHose != 0.0;
    } else {
      return price % 100.0 != 0.0;
    }
  }

  static Order getOrderType(String? orderSide) {
    return (orderSide ?? '').toUpperCase() == Order.sell.name.toUpperCase()
        ? Order.sell
        : Order.buy;
  }

  static String getQuotePrice(num? quotePrice, String? command) {
    if (command != null) {
      if (command.toUpperCase().compareTo(OrderType.lo.name.toUpperCase()) ==
          0) {
        return quotePrice != null
            ? quotePrice.toDouble().getPriceFormatted(
              currency: '',
              convertToThousand: true,
            )
            : '';
      } else {
        return command.toUpperCase();
      }
    } else {
      return '';
    }
  }

  static double getNumber(double input, {int precision = 2}) {
    int idxPoint = '$input'.indexOf('.');
    if (idxPoint >= 0 && '$input'.length - idxPoint > precision) {
      return double.parse('$input'.substring(0, idxPoint + precision + 1));
    } else {
      return input;
    }
  }

  // static String formatStockFilterParam(List<StockFilterAttribute> data) {
  //   String filter = data
  //       .map((e) {
  //         String menuId = '';
  //         if ((e.id ?? []).isNotEmpty) {
  //           menuId = e.id!.first.toString();
  //         }
  //         if (e.hasRange ?? false) {
  //           return '$menuId:${e.fieldName}:${e.min ?? ''}:${e.max ?? ''}';
  //         } else {
  //           final listValue = e.value ?? [];
  //           if (e.mutichoice ?? false) {
  //             return '${e.groupId}M:${e.fieldName}:${listValue.join(',')}';
  //           } else {
  //             return '$menuId:${e.fieldName}:${listValue.join(',')}';
  //           }
  //         }
  //       })
  //       .toList()
  //       .join('|');
  //   return filter;
  // }

  // static String formatFilterRangeValue(StockFilterAttribute attribute) {
  //   final context =
  //       GetIt.instance<NavigationService>().navigatorKey.currentContext;
  //   if (context == null) {
  //     return '';
  //   }
  //   final String key = attribute.shortName ?? '';
  //   final String? unitType = attribute.unitType;
  //   final List<String?> shortNameChild = (attribute.shortNameChild ?? []);
  //   shortNameChild.removeWhere((element) => element == null || element.isEmpty);
  //   final from = getStockLang(StockKeyLang.from);
  //   final to = getStockLang(StockKeyLang.to);
  //   String value = '';
  //   if (shortNameChild.isEmpty) {
  //     final min = attribute.min;
  //     final max = attribute.max;
  //     if (min != null && max != null) {
  //       value =
  //           '$from ${min.formatDoubleCurrencyValue('')} ${to.toLowerCase()} ${max.formatDoubleCurrencyValue(unitType)}';
  //     } else if (min != null) {
  //       value = '$from ${min.formatDoubleCurrencyValue(unitType)}';
  //     } else if (max != null) {
  //       value = '$to ${max.formatDoubleCurrencyValue(unitType)}';
  //     }
  //   } else {
  //     value = shortNameChild.join(', ');
  //   }
  //   return '$key: $value';
  // }

  static Map<String, String> get orderHeaders => {
    "x-device": 'AppData().deviceName',
    "x-devicetype": Platform.isAndroid ? "Android" : "IOS",
    "x-lang": "vi",
    "x-via": "Y",
  };

  static List<StockPortfolioEntity> handleDuplicatedStockHolding(
    List<StockPortfolioEntity> holdingList,
  ) {
    if (holdingList.isEmpty) return [];

    List<StockPortfolioEntity> uniqueList = [];

    for (StockPortfolioEntity entity in holdingList) {
      final existedItem = uniqueList.firstWhereOrNull(
        (e) => e.symbol == entity.symbol,
      );

      if (existedItem == null) {
        uniqueList.add(entity.clone());
      } else {
        final totalVol = existedItem.totalVol + entity.totalVol;

        final newCostPrice = entity.costPrice * entity.totalVol;

        final preCostPrice = existedItem.costPrice * existedItem.totalVol;

        existedItem.costPrice = (preCostPrice + newCostPrice) / totalVol;

        existedItem.totalVol += entity.totalVol;

        existedItem.availableToTradeVol += entity.availableToTradeVol;
      }
    }

    return uniqueList;
  }

  static String mapAccountTypeToText(String? accountType) {
    if (accountType?.isEmpty ?? true) {
      return '';
    }
    switch (accountType) {
      case WealthConstants.margin:
        return 'margin';
      case WealthConstants.bond:
        return 'bond';
      default:
        // normal
        return 'normal';
    }
  }

  static getStep(num price, String? exchange) {
    if (exchange != StockAppConstants.hsx) {
      return 100;
    } else {
      if (price < 10000) {
        return 10;
      } else if (price >= 10000 && price < 50000) {
        return 50;
      } else {
        return 100;
      }
    }
  }

  static String getStatusGtc({
    required String? statusCode,
    required String status,
  }) {
    if (StockAppConstants.completeGtc.contains(statusCode)) {
      //hoàn thành
      return getStockLang(StockKeyLang.complete);
    } else if (StockAppConstants.waitingGtc.contains(statusCode)) {
      //chờ khớp
      return getStockLang(StockKeyLang.waiting);
    } else if (StockAppConstants.editedGtc.contains(statusCode)) {
      //đã sửa
      return getStockLang(StockKeyLang.edited);
    } else if (StockAppConstants.cancelledGtc.contains(statusCode)) {
      //đã hủy
      return getStockLang(StockKeyLang.cancelled);
    } else if (StockAppConstants.expireGtc.contains(statusCode)) {
      //hết hạn
      return getStockLang(StockKeyLang.expire);
    } else {
      // khớp 1 phần
      return getStockLang(StockKeyLang.matched);
    }
  }

  static String getStatus({
    required String? statusCode,
    required String status,
  }) {
    if (StockAppConstants.waitingToSend.contains(statusCode)) {
      //chờ gửi
      return getStockLang(StockKeyLang.waitingSend);
    } else if (StockAppConstants.sent.contains(statusCode)) {
      //đã gửi
      return getStockLang(StockKeyLang.sent);
    } else if (StockAppConstants.waiting.contains(statusCode)) {
      //đang chờ
      return getStockLang(StockKeyLang.waiting);
    } else if (StockAppConstants.matched.contains(statusCode)) {
      //khớp 1 phần
      return getStockLang(StockKeyLang.matched);
    } else if (StockAppConstants.complete.contains(statusCode)) {
      //khớp hết
      return getStockLang(StockKeyLang.matchAll);
    } else if (StockAppConstants.edited.contains(statusCode)) {
      //đã sửa
      return getStockLang(StockKeyLang.edited);
    } else if (StockAppConstants.cancelled.contains(statusCode)) {
      //đã hủy
      return getStockLang(StockKeyLang.cancelled);
    } else if (StockAppConstants.reject.contains(statusCode)) {
      //từ chối
      return getStockLang(StockKeyLang.rejected);
    } else if (StockAppConstants.expired.contains(statusCode)) {
      //hết hạn
      return getStockLang(StockKeyLang.expire);
    } else {
      // hết hiệu lực
      return getStockLang(StockKeyLang.expired);
    }
  }

  static String getStatusForWealth({
    required String? statusCode,
    required String status,
  }) {
    if (StockAppConstants.waitingToSend.contains(statusCode)) {
      //chờ gửi
      return getStockLang(StockKeyLang.waitingSend);
    } else if (StockAppConstants.sent.contains(statusCode)) {
      //đã gửi
      return getStockLang(StockKeyLang.sent);
    } else if (StockAppConstants.waiting.contains(statusCode)) {
      //đang chờ
      return getStockLang(StockKeyLang.waiting);
    } else if (StockAppConstants.matched.contains(statusCode)) {
      //khớp 1 phần
      return getStockLang(StockKeyLang.matched);
    } else if (StockAppConstants.complete.contains(statusCode)) {
      //khớp hết
      return getStockLang(StockKeyLang.matchAll);
    } else if (StockAppConstants.edited.contains(statusCode)) {
      //đã sửa
      return getStockLang(StockKeyLang.edited);
    } else if (StockAppConstants.cancelled.contains(statusCode)) {
      //đã hủy
      return getStockLang(StockKeyLang.cancelled);
    } else if (StockAppConstants.rejectForWealth.contains(statusCode)) {
      //từ chối
      return getStockLang(StockKeyLang.rejected);
    } else if (StockAppConstants.expired.contains(statusCode)) {
      //hết hạn
      return getStockLang(StockKeyLang.expire);
    } else if (StockAppConstants.pendingProcessingForWealth.contains(
      statusCode,
    )) {
      //chờ xử lý
      return 'Chờ xử lý';
    } else {
      // hết hiệu lực
      return getStockLang(StockKeyLang.expired);
    }
  }

  static String getStatusNotIncluded({
    required String? statusCode,
    required String status,
  }) {
    if (statusCode == "2") {
      return "Chờ khớp";
    } else if (statusCode == "4") {
      return "Khớp 1 phần";
    }
    return status;
  }

  static Color statusCommandGtc(String? stt) {
    if (StockAppConstants.completeGtc.contains(stt)) {
      //hoàn thành
      return themeData.primary;
    } else if (StockAppConstants.waitingGtc.contains(stt)) {
      //chờ khớp
      return themeData.blueChart;
    } else if (StockAppConstants.editedGtc.contains(stt)) {
      //đã sửa
      return themeData.blueChart;
    } else if (StockAppConstants.cancelledGtc.contains(stt)) {
      //đã hủy
      return themeData.red;
    } else if (StockAppConstants.expireGtc.contains(stt)) {
      //hết hạn
      return themeData.red;
    } else {
      // khớp 1 phần
      return themeData.blueChart;
    }
  }

  static Color statusCommand(String? stt) {
    if (StockAppConstants.complete.contains(stt)) {
      //khớp hết
      return themeData.primary;
    } else if (StockAppConstants.waitingToSend.contains(stt) ||
        StockAppConstants.sent.contains(stt) ||
        StockAppConstants.waiting.contains(stt) ||
        StockAppConstants.matched.contains(stt)) {
      //chờ gửi, đã gửi, đang chờ, khớp 1 phần
      return themeData.blueChart;
    } else {
      // đã sửa, đã hủy, từ chối, hết hạn, hết hiệu lực
      return themeData.red;
    }
  }

  static bool validateAndSave(GlobalKey<FormState> key) {
    FormState? form = key.currentState;
    if (form?.validate() ?? false) {
      form?.save();
      return true;
    }
    return false;
  }
}
