import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/data/model/assets/item_assets_model.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/place_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/model/rc_arguments.dart';

class PlaceOrderArguments {
  final Order? order;
  final SubAccountType? subAccountType;
  final String? symbol;
  final bool? recommendDetailsOrder;
  final RcArguments? recommendationDetailModel;
  final ItemAssetsModel? wealthModel;

  PlaceOrderArguments({
    this.order,
    this.subAccountType,
    this.symbol,
    this.recommendDetailsOrder,
    this.recommendationDetailModel,
    this.wealthModel,
  });
}
