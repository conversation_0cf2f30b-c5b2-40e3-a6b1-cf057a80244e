import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';

import '../bloc/bloc.dart';

class ValueLabel extends StatelessWidget {
  final bool center;

  const ValueLabel({Key? key, this.center = false}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment:
          center ? CrossAxisAlignment.center : CrossAxisAlignment.start,
      children: [
        NewOrderSelector(
          (newOrder) => Text(
            newOrder ? 'Giá trị lệnh' : 'Gi<PERSON> trị',
            style: vpTextStyle.captionRegular?.copyWith(
              color: vpColor.textTertiary,
            ),
          ),
        ),
        Expanded(
          child: ValueSelector(
            (value) => AutoSizeText(
              value.label,
              style: vpTextStyle.body14?.copyWith(color: value.color),
              minFontSize: 2,
            ),
          ),
        ),
      ],
    );
  }
}
