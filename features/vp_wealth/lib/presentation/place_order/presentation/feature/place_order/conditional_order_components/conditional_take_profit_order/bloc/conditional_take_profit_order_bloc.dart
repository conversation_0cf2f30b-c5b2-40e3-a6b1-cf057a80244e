import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/extensions/list_extensions.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/data/utils/loading_utils.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/presentation/place_order/domain/conditional_order_repository.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/conditional_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/exchange.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/place_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/domain/model/effective_time_model.dart';
import 'package:vp_wealth/presentation/place_order/model/securities_portfolio_responses_model.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_focus_keyboard.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_take_profit_order/states/slippage_profit_margin_state.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_take_profit_order/states/take_profit_input_state.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_take_profit_order/widgets/take_profit_inputs/take_profit_rate_profit_input_cubit.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_take_profit_order/widgets/take_profit_slippage_input/take_profit_slippage_input_cubit.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_take_profit_trigger_condition_enum.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/utils/bloc_base/bloc_state_conditional_have_focus.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/utils/extensions/take_profit_num_extension.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/utils/get_params_request_utils/take_profit_get_params_request_utils.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/widgets/inputs/conditional_volume_input/conditional_volume_cubit.dart';
import 'package:vp_wealth/presentation/place_order/utils/place_order_utils.dart';

import '../../../bloc/bloc.dart';

part 'conditional_take_profit_order_event.dart';
part 'conditional_take_profit_order_state.dart';

class ConditionalTakeProfitOrderBloc extends Bloc<
    ConditionalTakeProfitOrderEvent, ConditionalTakeProfitOrderState> {
  ConditionalTakeProfitOrderBloc(this.repository)
      : super(ConditionalTakeProfitOrderState.init()) {
    on<ConditionalTakeProfitChangeDilutionTypeEvent>(_onChangeDilutionType);
    on<ConditionalTakeProfitChangeEffectiveTimeEvent>(
        _changeEffectiveTimeEvent);
    on<ChangeRateProfitMarginEvent>(_changeRateProfit);
    on<TapIncreaseRateProfitEvent>(_tapIncreaseRateProfit);
    on<TapDecreaseRateProfitEvent>(_tapDecreaseRateProfit);
    on<RateProfitFocusKeyboardEvent>(_focusKeyboardEvent);

    on<ChangeSlippageProfitMarginEvent>(_changeSlippageMargin);
    on<TapIncreaseSlippageProfitMarginEvent>(_tapIncreaseSlippageProfit);
    on<TapDecreaseSlippageProfitMarginEvent>(_tapDecreaseSlippageProfit);
    on<SlippageProfitMarginFocusKeyboardEvent>(_focusSlippageKeyboardEvent);

    on<TakeProfitFocusKeyboardEvent>(_onFocusKeyboardEvent);

    on<UpdateOrderNavigateStatusEvent>(_onUpdateOrderNavigateStatus);
    on<TakeProfitUpdateOrderNavigateStatusOtherInputEvent>(
        _onUpdateNavigateStatusOtherInput);
    on<TakeProfitUnFocusAllInput>(_takeProfitUnfocusAllInput);

    on<UpdateTriggerConditionEvent>(_updateTriggerCondition);

    on<TakeProfitSubmitEvent>(_onSubmitOrderEvent);

    on<TakeProfitUpdateOrderTypeEvent>(_onUpdateOrderType);
    on<TakeProfitUpdateExchangeEvent>(_onUpdateExchange);
    on<TakeProfitStockTypeChangeEvent>(_onTakeProfitStockTypeChangeEvent);

    on<TakeProfitClearStateEvent>(_onTakeProfitClearState);

    on<TakeProfitInitialDataEvent>(_initData);

    on<TakeProfitGetPortfolioResponseModel>(getPortfolioResponseModel);

    on<OnTapFocusVolumeInput>(_onTapFocusVolumeInput);
    on<UpdateTakeProfitVolumeEvent>(_onUpdateConditionalVolume);

    on<UpdateSlippageInputStateEvent>(_onUpdateSlippageInputState);
    on<UpdateRateProfitInputStateEvent>(_onUpdateRateProfitInputState);
    on<TakeProfitTapNextButtonEvent>(_handleTapNextButtonEvent);

    on<OnTapFocusSlippageInput>(_onTapFocusSlippageInput);
    on<OnTapFocusRateProfitInput>(_onTapFocusRateProfitInput);

    on<TakeProfitUpdateSubAccountEvent>(_onTakeProfitUpdateSubAccount);
    on<TakeProfitOnUpdateSymbol>(_onUpdateSymbol);
    on<TakeProfitOnUpdateMaxVolume>(_onUpdateMaxVolume);
    on<TakeProfitOnTapVolumeSuggest>(_onTapVolumeSuggest);
    on<TakeProfitUpdateOrderEvent>(_onUpdateOrder);
    on<OnTapCloseKeyboardEvent>(_onTapCloseKeyboardEvent);
  }

  final ConditionalOrderRepository repository;
  final TakeProfitGetParamsRequestUtils takeProfitGetParamsRequestUtils =
      TakeProfitGetParamsRequestUtils();

  @override
  void onTransition(
      Transition<ConditionalTakeProfitOrderEvent,
              ConditionalTakeProfitOrderState>
          transition) {
    super.onTransition(transition);
  }

  final mainStockRepo = GetIt.instance.get<WealthRepository>();
  List<SecuritiesPortfolioResponsesModel> _listData =
      <SecuritiesPortfolioResponsesModel>[];

  void _initData(_, emit) {
    // getPortfolioResponseModel(emit);
    add(TakeProfitGetPortfolioResponseModel());
  }

  late PlaceOrderBloc _placeOrderBloc;

  void onChangePlaceOrderBloc(PlaceOrderBloc bloc) {
    _placeOrderBloc = bloc;

    add(TakeProfitOnUpdateSymbol(_placeOrderBloc.state.symbolText));
  }

  void _onTapCloseKeyboardEvent(_, emit) {
    emit(state.copyWith(
        conditionalFocusKeyboard: ConditionalFocusKeyBoard.none));
  }

  void _onUpdateMaxVolume(TakeProfitOnUpdateMaxVolume event, emit) {
    emit(state.copyWith(maxVolume: event.value));
  }

  void _onTapVolumeSuggest(TakeProfitOnTapVolumeSuggest event, emit) {
    final volumeText = event.value;
    emit(state.copyWith(
        volumeFromSuggestion: volumeText.volume,
        conditionalVolumeInputState: state.conditionalVolumeInputState
            ?.copyWith(
                volume: volumeText.volume, clearVolume: volumeText.isEmpty)));
  }

  void _onUpdateOrder(TakeProfitUpdateOrderEvent event, emit) {
    emit(state.copyWith(order: event.order));
  }

  void _onUpdateSymbol(TakeProfitOnUpdateSymbol event, emit) {
    emit(state.copyWith(
      symbol: event.symbol,
      order: _placeOrderBloc.state.order,
      maxVolume: _placeOrderBloc.state.inputVolumeState.maxVolume,
    ));
    add(TakeProfitGetPortfolioResponseModel());
  }

  Future<void> _onTakeProfitUpdateSubAccount(
      TakeProfitUpdateSubAccountEvent event, emit) async {
    String userId = GetIt.instance.get<SubAccountCubit>().getSubAccount(event.subAccountType)?.id ?? '';
    final data = await getSecuritiesPortfolioSubAccount(userId);
    if (data.isNotEmpty) {
      _listData.clear();
      _listData.addAll(data);
      add(TakeProfitGetPortfolioResponseModel());
    }
  }

  void getPortfolioResponseModel(_, emit) async {
    final securitiesPortfolioResponsesModel = _listData
        .firstWhere((element) => element.symbol == state.symbol, orElse: () {
      return SecuritiesPortfolioResponsesModel(
        trade: -1,
      );
    });
    /// merge costPrice with WFT
    final wftPortfolioResponsesModel = _listData
        .firstWhereOrNull((element) => element.symbol == state.symbol + '_WFT');
    if (wftPortfolioResponsesModel != null) {
      securitiesPortfolioResponsesModel
          .mixCostPriceWithWFT(wftPortfolioResponsesModel);
    }

    emit(state.copyWith(
      securitiesPortfolioResponsesModel: securitiesPortfolioResponsesModel,
      costPrice: securitiesPortfolioResponsesModel.costPrice ?? 0,
      trade: securitiesPortfolioResponsesModel.trade ?? 0,
    ));
  }

  Future<List<SecuritiesPortfolioResponsesModel>>
      getSecuritiesPortfolioSubAccount(String accountId) async {
    List<SecuritiesPortfolioResponsesModel> dataResponse =
        <SecuritiesPortfolioResponsesModel>[];
    try {
      final value = await mainStockRepo.getSecuritiesPortfolio(accountId);

      if (value.isNotEmpty) {
        // value.mixSymbolWFT();

        final listSymbols = value.map((e) => e.symbol ?? '').toSet().toList();

        /// get stock detail because securities portfolio return cached value
        /// (basic price, referencePrice, floorPrice...)
        final details =
            await mainStockRepo.getStocks(listSymbols.symbolsFormat);

        final data = value
            .map(
              (e) => e.copyWith(details
                  .firstWhereOrNull((stock) => e.symbol == stock.symbol)),
            )
            .toList();
        dataResponse.addAll(data);
      }
    } catch (e, stackTrace) {
      final response = e is ResponseError && e.code == 'IVBERR02' ? null : e;
      showError(response);
    }
    return dataResponse;
  }

  void _onUpdateSlippageInputState(UpdateSlippageInputStateEvent event, emit) {
    emit(
        state.copyWith(takeProfitSlippageInputState: event.slippageInputState));
  }

  void _onUpdateRateProfitInputState(
      UpdateRateProfitInputStateEvent event, emit) {
    emit(state.copyWith(takeProfitRateProfitInputState: event.rateProfitInput));
  }

  void _onTapFocusSlippageInput(_, emit) {
    emit(state.copyWith(
        conditionalFocusKeyboard: ConditionalFocusKeyBoard.slippage));
  }

  void _onTapFocusRateProfitInput(_, emit) {
    emit(state.copyWith(
        conditionalFocusKeyboard: ConditionalFocusKeyBoard.rateProfit));
  }

  void _onChangeDilutionType(
      ConditionalTakeProfitChangeDilutionTypeEvent event, emit) {
    emit(state.copyWith(dilutionType: event.dilutionType));
  }

  void _changeEffectiveTimeEvent(
      ConditionalTakeProfitChangeEffectiveTimeEvent event, emit) {
    emit(
      state.copyWith(
        effectiveTime: EffectiveTimeModel(
          start: event.value.first,
          end: event.value.last,
        ),
      ),
    );
  }

  /// ************************ RATE PROFIT EVENTS *************************

  void _changeRateProfit(ChangeRateProfitMarginEvent event, emit) {
    final rateProfit = event.value;
    emit(state.copyWith(
        rateProfit: rateProfit.volume, clearRateProfit: rateProfit.isEmpty));
  }

  void _tapDecreaseRateProfit(_, emit) {
    final decreaseVolume = state.rateProfitState.decreaseVolume();
    if (decreaseVolume.toMoney(showSymbol: false).length >
        state.rateProfitState.maxLength) {
      return;
    }
    emit(state.copyWith(rateProfit: decreaseVolume));
  }

  void _tapIncreaseRateProfit(_, emit) {
    final increaseVolume = state.rateProfitState.increaseVolume();
    if (increaseVolume.toMoney(showSymbol: false).length >
        state.rateProfitState.maxLength) {
      return;
    }
    emit(state.copyWith(rateProfit: increaseVolume));
  }

  void _focusKeyboardEvent(RateProfitFocusKeyboardEvent event, emit) {
    emit(state.copyWith(focusKeyboard: event.conditionalFocusKeyBoard));
  }

  /// ************************ SLIPPAGE PROFIT EVENTS *************************

  void _changeSlippageMargin(ChangeSlippageProfitMarginEvent event, emit) {
    final slippageText = event.value;
    if (slippageText.isEmpty) {
      emit(state.copyWith(clearSlippageMargin: true));
    }
    emit(state.changePriceNotFormat(priceText: slippageText));
    // if (state.slippageMarginProfitState.isDone) {
    //   add(CallAvailableTradeWithErrorCatchEvent());
    // }
  }

  void _tapDecreaseSlippageProfit(_, emit) {
    final decreaseSlippage =
        state.slippageMarginProfitState.decreasePrice().toDouble();

    emit(state.changePriceWithFormat(slippageMargin: decreaseSlippage));
  }

  void _tapIncreaseSlippageProfit(_, emit) {
    final increaseSlippage =
        state.slippageMarginProfitState.increasePrice().toDouble();

    emit(state.changePriceWithFormat(slippageMargin: increaseSlippage));
  }

  void _focusSlippageKeyboardEvent(
      SlippageProfitMarginFocusKeyboardEvent event, emit) {
    emit(state.copyWith(focusKeyboard: event.conditionalFocusKeyBoard));
  }

  void _onFocusKeyboardEvent(TakeProfitFocusKeyboardEvent event, emit) {
    emit(state.copyWith(takeProfitFocusKeyboard: event.value));
  }

  void _onUpdateOrderNavigateStatus(
      UpdateOrderNavigateStatusEvent event, emit) {
    emit(state.copyWith(
        orderNavigateStatusOtherInput: event.orderNavigateStatus));
  }

  void _onUpdateNavigateStatusOtherInput(
      TakeProfitUpdateOrderNavigateStatusOtherInputEvent event, emit) {
    emit(state.copyWith(
        orderNavigateStatusOtherInput: event.orderNavigateStatus));
  }

  void _handleTapNextButtonEvent(_, emit) {
    switch (state.conditionalFocusKeyboard) {
      case ConditionalFocusKeyBoard.rateProfit:
        emit(state.copyWith(
            conditionalFocusKeyboard: ConditionalFocusKeyBoard.slippage));
        return;
      case ConditionalFocusKeyBoard.slippage:
        emit(state.copyWith(
            conditionalFocusKeyboard: ConditionalFocusKeyBoard.volume));
        return;
      case ConditionalFocusKeyBoard.volume:
        emit(state.copyWith(
            conditionalFocusKeyboard: ConditionalFocusKeyBoard.rateProfit));
        return;
      default:
        emit(state.copyWith(
            conditionalFocusKeyboard: ConditionalFocusKeyBoard.none));
    }
  }

  void _takeProfitUnfocusAllInput(_, emit) {
    emit(state.copyWith(
        takeProfitFocusKeyboard: ConditionalTakeProfitFocusKeyboard.none));
  }

  void _updateTriggerCondition(UpdateTriggerConditionEvent event, emit) {
    if (event.triggerCondition != state.triggerCondition) {
      emit(state.copyWith(
          triggerCondition: event.triggerCondition,
          takeProfitRateProfitInputState: state.takeProfitRateProfitInputState
              ?.copyWith(clearPrice: true)));
    }
  }

  void _onUpdateOrderType(TakeProfitUpdateOrderTypeEvent event, emit) {
    emit(state.copyWith(orderType: event.orderType));
    add(TakeProfitGetPortfolioResponseModel());
  }

  void _onUpdateExchange(TakeProfitUpdateExchangeEvent event, emit) {
    emit(state.copyWith(exchange: event.exchange));
    add(TakeProfitGetPortfolioResponseModel());
  }

  void _onTakeProfitStockTypeChangeEvent(
      TakeProfitStockTypeChangeEvent event, emit) {
    emit(state.copyWith(stockType: event.stockType));
  }

  void _onSubmitOrderEvent(TakeProfitSubmitEvent event, emit) async {
    final placeOrderState = event.placeOrderState;
    final params = takeProfitGetParamsRequestUtils.getParamsSubmitTakeProfit(
        placeOrderState: placeOrderState, state: state, userId: event.userId);
    try {
      LoadingUtil.showLoading();
      final result = await repository.conditionalOrderRequest(params);
      LoadingUtil.hideLoading();
      if (result.isSuccess()) {
        event.onSuccess();
      } else {
        showMessageError(result.message ?? '');
      }
    } catch (e) {
      LoadingUtil.hideLoading();
      showError(e);
    }
  }

  void _onTakeProfitClearState(_, emit) {
    emit(state.resetValueAfterChangeMethod());
  }

  /// VOlume
  void _onTapFocusVolumeInput(_, emit) {
    emit(state.copyWith(
        conditionalFocusKeyboard: ConditionalFocusKeyBoard.volume));
  }

  void _onUpdateConditionalVolume(UpdateTakeProfitVolumeEvent event, emit) {
    emit(state.copyWith(conditionalVolumeInputState: event.volumeInputState));
  }
}
