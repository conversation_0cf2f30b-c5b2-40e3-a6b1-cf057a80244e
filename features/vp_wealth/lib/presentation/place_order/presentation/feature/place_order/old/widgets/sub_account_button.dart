import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';

import '../../../../../utils/place_order_utils.dart';
import '../../bloc/bloc.dart';

class SubAccountButton extends StatelessWidget {
  const SubAccountButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<PlaceOrderBloc>();
    return TypeItemButton(
      title: SubAccountLabelSelector(
        (subAccountLabel) => Text(
          'TK tích sản',
          style: vpTextStyle.body14?.copyWith(color: vpColor.textPrimary),
        ),
      ),
      canChange: bloc.canChangeSubAccountType,
      bottomSheetBuilder: (_) => const DialogSelectSubAccount(),
      onDataReturn:
          (type) => bloc.add(ChangeSubAccountTypeEvent(subAccountType: type)),
    );
  }
}
