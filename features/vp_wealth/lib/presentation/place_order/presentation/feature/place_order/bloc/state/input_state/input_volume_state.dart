import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/services.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/lang/stock_localized_values.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/place_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/gen/locale_keys.g.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/state/input_field/bloc/input_field_cubit.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/condition/condition_command_util.dart';
import 'package:vp_wealth/presentation/place_order/utils/ext.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/data/stock_helper.dart';

part 'input_volume_state.freezed.dart';

enum Error {
  invalid,
  invalidMax,
  invalidBuy,
  invalidSell,
}

@freezed
abstract class InputVolumeState extends InputFieldState with _$InputVolumeState {
  InputVolumeState._();

  factory InputVolumeState({
    required String text,
    required num maxVolume,
    required num trade,
    required OrderType orderType,
    SessionType? sessionType,
    required Order order,
    required FocusKeyboard focusKeyboard,
  }) = _InputVolumeState;

  @override
  String get hint => 'KL';

  @override
  int get maxLength => 12;

  bool _invalidLot(num volume) =>
      volume.isOddLot && !(orderType.isLo && (sessionType?.isAtoAtc ?? true));

  @override
  Error? get error {
    if (text == '0' ||
        (value >= 100 && value % 100.0 != 0.0) ||
        _invalidLot(value)) {
      return Error.invalid;
    }
    if (maxVolume == 0 && order == Order.buy) return Error.invalidBuy;
    if (maxVolume == 0 && order == Order.sell) return Error.invalidSell;
    if (value > maxVolume && !orderType.isGtc) return Error.invalidMax;
    return null;
  }

  @override
  Map<dynamic, String> get errorToText => {
        Error.invalid: getStockLang(StockKeyLang.validateVolume),
        Error.invalidBuy: 'Vượt quá sức mua của tiểu khoản',
        Error.invalidSell: 'Bạn không nắm giữ cổ phiếu này',
        Error.invalidMax:
            'Vượt khối lượng tối đa là ${maxVolume.volumeString}',
      };

  @override
  List<TextInputFormatter> get inputFormatter =>
      [...removeZeroStartInputFormatter, ...volumeInputFormatter];

  @override
  bool get focus => focusKeyboard == FocusKeyboard.volume;

  @override
  String tap({bool increase = true}) {
    if (value == 0.0) {
      return maxVolume > 0.0 && maxVolume < 100.0
          ? maxVolume.volumeString
          : (increase ? 100.0 : 0.0).volumeString;
    }
    final step = value.isOddLot && !_invalidLot(value) ? 1.0 : 100.0;
    final newText =
        ConditionCommandUtil.updateValue(increase, value, step).volumeString;
    if (newText.length > maxLength) return text;
    return newText;
  }

  @override
  double get value => text.volume;

  String get maxVolumeText => maxVolume.volumeString;

  String get tradeText => trade.volumeString;

  int _roundVolume(int index, double volume) {
    if (volume < 100.0) {
      return volume.round().toInt();
    }
    final evenDownRound = (volume / 100.0).floor() * 100.0;
    return evenDownRound.toInt();
  }

  @override
  List<String> get suggests => {
        _roundVolume(1, maxVolume * 0.2): '(20%)',
        _roundVolume(2, maxVolume * 0.5): '(50%)',
        _roundVolume(3, maxVolume * 1.0): '(100%)',
      }.toList(
        (entry) => entry.key <= 0 || _invalidLot(entry.key)
            ? '-'
            : '${StockHelper.formatSuggestVol(entry.key)} ${entry.value}',
      )..removeWhere((suggest) => suggest.contains('-'));

  String tapSuggest(String suggest) {
    var volumeText =
        RegExp(r'.+(?= \(\d+%\)$)').firstMatch(suggest.trim())?.group(0);
    if (volumeText == null) return text;
    if (volumeText.contains(',')) {
      volumeText += '0';
    }
    if (volumeText == text) return text;
    return volumeText;
  }
}
