import 'dart:ui';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/core/constant/stock_value_constans.dart';
import 'package:vp_wealth/data/utils/loading_utils.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/presentation/place_order/data/data.dart';
import 'package:vp_wealth/presentation/place_order/domain/conditional_order_repository.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/conditional_market_command_enum.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/conditional_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/exchange.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/place_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/domain/model/effective_time_model.dart';
import 'package:vp_wealth/presentation/place_order/domain/place_order_repository.dart';
import 'package:vp_wealth/presentation/place_order/model/securities_portfolio_responses_model.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_pending_buy_order/state/conditional_pending_order_activation_price_state.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/utils/bloc_base/bloc_state_conditional_have_focus.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/utils/bloc_base/bloc_state_have_conditional_price_input.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/utils/extensions/take_profit_num_extension.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/utils/get_params_request_utils/pending_order_get_params_request_utils.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/utils/get_port_folio_response_model/get_port_folio_response.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/widgets/inputs/conditional_price_input/conditional_price_input_cubit.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/widgets/inputs/conditional_volume_input/conditional_volume_cubit.dart';
import 'package:vp_wealth/presentation/place_order/utils/ext.dart';
import '../../../../../../../wealths/assets/details/tabs/money/money_cash_in_v2/cubit/sub_account_model.dart' as money_sub_account;
import '../../../bloc/bloc.dart';
import '../../conditional_focus_keyboard.dart';

part 'conditional_pending_order_event.dart';
part 'conditional_pending_order_state.dart';

class ConditionalPendingOrderBloc
    extends Bloc<ConditionalPendingOrderEvent, ConditionalPendingOrderState> {
  ConditionalPendingOrderBloc({required this.repository})
    : super(ConditionalPendingOrderState.init()) {
    on<IInitialInputValue>(_initialInputState);

    on<OnChangePriceType>(_onChangePriceType);
    on<ConditionalOrderChangeExchangeEvent>(_onchangeExchange);
    on<ConditionalOrderStockTypeChangeEvent>(
      _onConditionalOrderStockTypeChangeEvent,
    );

    on<ChangeEffectiveTimeEvent>(_changeGtcEffectiveTimeEvent);

    on<UpdateOtherInputEvent>(_updateOtherInputStatus);
    on<ConditionalOrderSubmitOrderEvent>(_onSubmitOrderEvent);
    on<ConditionalOrderChangeDilutionTypeEvent>(_onChangeDilutionType);
    on<ActivationPriceFocusKeyboardEvent>(_onActivationPriceFocus);
    on<UpdateOrderNavigateStatusOtherInputEvent>(
      _onUpdateOrderNavigateStatusOtherInputEvent,
    );

    on<UpdateConditionalPriceEvent>(_onUpdateConditionalPrice);
    on<UpdateConditionalActivationPriceEvent>(
      _onUpdateConditionalActivationPrice,
    );
    on<UpdateConditionalVolumeEvent>(_onUpdateConditionalVolume);
    on<TapNextButtonEvent>(_handleTapNextButtonEvent);

    on<OnTapFocusPriceInput>(_onTapFocusPriceInput);
    on<OnTapFocusActivationPriceInput>(_onTapFocusActivationPriceInput);
    on<OnTapFocusVolumeInput>(_onTapFocusVolumeInput);
    on<ConditionalUpdateOrderEvent>(_onUpdateOrder);
    on<OnTapVolumeSuggest>(_onTapVolumeSuggest);
    on<UpdateMaxVolumeToSell>(_onUpdateMaxVolume);
    on<PendingOrderUpdateSubAccountEvent>(_onPendingUpdateSubAccount);
    on<OnTapCloseKeyboardEvent>(_onTapCloseKeyboardEvent);
    on<UpdateMaxVolumeToBuy>(_onUpdateAvailableTrade);
    on<PendingOrderUpdateSymbolEvent>(_onUpdateSymbol);
    on<PendingOrderResetState>(_pendingOrderResetState);
    on<CallAvailableTradeWithErrorCatch>(_callAvailableTradeWithErrorCatch);
  }

  final ConditionalOrderRepository repository;
  final PlaceOrderRepository placeOrderRepository =
      GetIt.instance.get<PlaceOrderRepository>();
  late PlaceOrderBloc _placeOrderBloc;

  String get _userId => '';

  void onChangePlaceOrderBloc(PlaceOrderBloc bloc) {
    _placeOrderBloc = bloc;
  }

  Future _callAvailableTradeWithErrorCatch(_, emit) async {
    try {
      final availableTrade = await placeOrderRepository.getAvailableTrade(
        _userId,
        AvailableTradeParams(
          symbol: _placeOrderBloc.state.symbol,
          quotePrice: state.conditionalPriceInputState?.priceNotNull ?? 0,
        ),
      );
      emit(
        state.copyWith(
          maxVolumeToBuy: availableTrade.maxVolumeBuy,
          maxVolumeToSell: availableTrade.maxVolumeSell,
        ),
      );
    } catch (e) {
      showError(e);
    }
  }

  void _pendingOrderResetState(_, emit) {
    emit(state.resetValueAfterChangeMethod());
  }

  void _onUpdateSymbol(_, emit) async {
    final securityPortfolio = await getSecurityPortfolio();
    emit(state.copyWith(securitiesPortfolioResponsesModel: securityPortfolio));
  }

  void _onUpdateAvailableTrade(UpdateMaxVolumeToBuy event, emit) {
    emit(state.copyWith(maxVolumeToBuy: event.value));
  }

  void _onTapCloseKeyboardEvent(_, emit) {
    emit(
      state.copyWith(conditionalFocusKeyboard: ConditionalFocusKeyBoard.none),
    );
  }

  Future<void> _onPendingUpdateSubAccount(
    PendingOrderUpdateSubAccountEvent event,
    emit,
  ) async {
    final GetPortFolioResponse _getPortFolioResponse = GetPortFolioResponse();
    final symbol = _placeOrderBloc.state.symbolText;
    final securityPortfolio = await _getPortFolioResponse.bySymbolAndSubAccount(
      symbol: symbol,
      subAccountType: event.subAccountType,
    );
    emit(
      state.copyWith(
        securitiesPortfolioResponsesModel: securityPortfolio,
        subAccountType: event.subAccountType,
      ),
    );
    add(CallAvailableTradeWithErrorCatch());
  }

  Future<SecuritiesPortfolioResponsesModel> getSecurityPortfolio() async {
    final GetPortFolioResponse _getPortFolioResponse = GetPortFolioResponse();
    final symbol = _placeOrderBloc.state.symbolText;
    final securityPortfolio = await _getPortFolioResponse.bySymbolAndSubAccount(
      symbol: symbol,
      subAccountType: state.subAccountType ?? SubAccountType.all,
    );
    return securityPortfolio;
  }

  void _onUpdateMaxVolume(UpdateMaxVolumeToSell event, emit) {
    emit(state.copyWith(maxVolumeToSell: event.value));
  }

  void _onTapVolumeSuggest(OnTapVolumeSuggest event, emit) {
    final volumeText = event.value;
    if (state.conditionalFocusKeyboard == ConditionalFocusKeyBoard.volume) {
      emit(
        state.copyWith(
          volumeFromSuggestion: volumeText.volume,
          conditionalVolumeInputState: state.conditionalVolumeInputState
              ?.copyWith(
                volume: volumeText.volume,
                clearVolume: volumeText.isEmpty,
              ),
        ),
      );
    } else if (state.conditionalFocusKeyboard ==
        ConditionalFocusKeyBoard.price) {
      emit(
        state.copyWith(
          marketCommand: MarketCommandEnum.values.firstWhere(
            (element) => element.paramsRequest() == event.value,
            orElse: () => MarketCommandEnum.none,
          ),
        ),
      );
    }
  }

  void _onUpdateOrder(ConditionalUpdateOrderEvent event, emit) {
    if (event.order != state.order) {
      emit(state.copyWith(order: event.order));
      add(CallAvailableTradeWithErrorCatch());
    }
  }

  void _onTapFocusPriceInput(_, emit) {
    emit(
      state.copyWith(conditionalFocusKeyboard: ConditionalFocusKeyBoard.price),
    );
  }

  void _onUpdateConditionalActivationPrice(
    UpdateConditionalActivationPriceEvent event,
    emit,
  ) {
    emit(state.copyWith(activationPriceInputState: event.activationPrice));
  }

  void _onTapFocusVolumeInput(_, emit) {
    emit(
      state.copyWith(conditionalFocusKeyboard: ConditionalFocusKeyBoard.volume),
    );
  }

  void _onUpdateConditionalVolume(UpdateConditionalVolumeEvent event, emit) {
    emit(state.copyWith(conditionalVolumeInputState: event.volumeInputState));
  }

  void _handleTapNextButtonEvent(_, emit) {
    switch (state.conditionalFocusKeyboard) {
      case ConditionalFocusKeyBoard.activationPrice:
        if (state.isNormalPrice) {
          emit(
            state.copyWith(
              conditionalFocusKeyboard: ConditionalFocusKeyBoard.price,
            ),
          );
        } else {
          emit(
            state.copyWith(
              conditionalFocusKeyboard: ConditionalFocusKeyBoard.volume,
            ),
          );
        }
        return;
      case ConditionalFocusKeyBoard.price:
        emit(
          state.copyWith(
            conditionalFocusKeyboard: ConditionalFocusKeyBoard.volume,
          ),
        );
        return;
      case ConditionalFocusKeyBoard.volume:
        emit(
          state.copyWith(
            conditionalFocusKeyboard: ConditionalFocusKeyBoard.activationPrice,
          ),
        );

        return;
      default:
        emit(
          state.copyWith(
            conditionalFocusKeyboard: ConditionalFocusKeyBoard.none,
          ),
        );
    }
    // emit(state.copyWith(conditionalPriceInputState: event.priceInputState));
  }

  @override
  void onEvent(ConditionalPendingOrderEvent event) {
    print('Event: $event From Buy Order');
    super.onEvent(event);
  }

  void initialData() {}

  void _initialInputState(IInitialInputValue event, emit) {
    final placeOrderState = event.placeOrderState;
    emit(
      state.copyWith(
        maxVolumeToSell: placeOrderState.inputVolumeState.maxVolume,
        volume: placeOrderState.inputVolumeState.value,
        order: placeOrderState.order,
        exchange: placeOrderState.inputPriceState.exchange,
      ),
    );
  }

  void _onchangeExchange(ConditionalOrderChangeExchangeEvent event, emit) {
    final exchange = event.exchange;
    emit(state.copyWith(exchange: exchange, clearPrice: true));
  }

  void _onConditionalOrderStockTypeChangeEvent(
    ConditionalOrderStockTypeChangeEvent event,
    emit,
  ) {
    emit(state.copyWith(stockType: event.stockType));
  }

  /// ************************ PRICE EVENTS *************************
  void _onUpdateConditionalPrice(UpdateConditionalPriceEvent event, emit) {
    emit(
      state.copyWith(
        conditionalPriceInputState: event.priceInputState,
        marketCommand: event.priceInputState.marketCommand,
      ),
    );
    add(CallAvailableTradeWithErrorCatch());
  }

  void _onTapFocusActivationPriceInput(_, emit) {
    emit(
      state.copyWith(
        conditionalFocusKeyboard: ConditionalFocusKeyBoard.activationPrice,
      ),
    );
  }

  /// ************************ ACTIVATION PRICE EVENTS *************************

  void _onChangePriceType(OnChangePriceType event, emit) {
    var priceType = event.timeType;
    emit(state.copyWith(activationPriceType: priceType));
  }

  void _changeGtcEffectiveTimeEvent(ChangeEffectiveTimeEvent event, emit) {
    emit(
      state.copyWith(
        effectiveTime: EffectiveTimeModel(
          start: event.value.first,
          end: event.value.last,
        ),
      ),
    );
  }

  /// ************************ ACTIVATION PRICE EVENTS *************************

  void _onActivationPriceFocus(ActivationPriceFocusKeyboardEvent event, emit) {
    emit(
      state.copyWith(conditionalFocusKeyboard: event.conditionalFocusKeyBoard),
    );
  }

  void _updateOtherInputStatus(UpdateOtherInputEvent event, emit) {
    emit(state.copyWith(orderNavigateStatusOtherInput: event.status));
  }

  void _onChangeDilutionType(
    ConditionalOrderChangeDilutionTypeEvent event,
    emit,
  ) {
    emit(state.copyWith(dilutionType: event.dilutionType));
  }

  String getPriceForRequest() {
    final priceState = state.conditionalPriceInputState;
    if (priceState?.isStringCommand ?? false) {
      return priceState?.marketCommand?.paramsRequest() ?? '';
    } else {
      final price = priceState?.price ?? 0;
      return price
          .formatConditionalPriceToRequest(state.stockType, state.exchange)
          .toString();
    }
  }

  void _onSubmitOrderEvent(ConditionalOrderSubmitOrderEvent event, emit) async {
    final placeOrderState = event.placeOrderState;

    final PendingOrderGetParamsRequestUtils _pendingOrderGetParamsUtils =
        PendingOrderGetParamsRequestUtils(state);

    final params = _pendingOrderGetParamsUtils.getParams(
      placeOrderState: placeOrderState,
      accountId: event.userId,
    );
    try {
      LoadingUtil.showLoading();
      final result = await repository.conditionalOrderRequest(params);
      LoadingUtil.hideLoading();
      if (result.isSuccess()) {
        event.onSuccess();
      } else {
        showMessageError(result.message ?? '');
      }
    } catch (e) {
      LoadingUtil.hideLoading();
      showError(e);
    }
  }

  void _onUpdateOrderNavigateStatusOtherInputEvent(
    UpdateOrderNavigateStatusOtherInputEvent event,
    emit,
  ) {
    emit(
      state.copyWith(orderNavigateStatusOtherInput: event.orderNavigateStatus),
    );
  }
}
