import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/order_navigate_status.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/place_order_enum.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/cubit/sub_account_model.dart';

import 'bloc.dart';
import 'state/input_state/input_price_state.dart';
import 'state/input_state/input_volume_state.dart';

class PlaceOrderBuyStateSelector<T>
    extends BlocSelector<PlaceOrderBloc, PlaceOrderState, T> {
  PlaceOrderBuyStateSelector({
    Key? key,
    required T Function(PlaceOrderState) selector,
    required dynamic Function(T) builder,
  }) : super(
          key: key,
          selector: selector,
          builder: (_, value) => builder(value),
        );
}

class NewOrderSelector extends PlaceOrderBuyStateSelector<bool> {
  NewOrderSelector(Widget Function(bool newOrder) builder, {Key? key})
      : super(
          key: key,
          selector: (state) => state.newOrder,
          builder: builder,
        );
}

class SubAccountLabelSelector extends PlaceOrderBuyStateSelector<LabelState> {
  SubAccountLabelSelector(
    Widget Function(LabelState subAccountLabel) builder, {
    Key? key,
  }) : super(
          key: key,
          selector: (state) => state.subAccountLabel,
          builder: builder,
        );
}

class SubAccountSelector extends PlaceOrderBuyStateSelector<SubAccountType> {
  SubAccountSelector(
    Widget Function(SubAccountType type) builder, {
    Key? key,
  }) : super(
          key: key,
          selector: (state) => state.subAccount,
          builder: builder,
        );
}

class SymbolSelector extends PlaceOrderBuyStateSelector<String?> {
  SymbolSelector(Widget Function(String? symbol) builder, {Key? key})
      : super(
          key: key,
          selector: (state) => state.symbol,
          builder: builder,
        );
}

class OrderTypeSelector extends PlaceOrderBuyStateSelector<String> {
  OrderTypeSelector(Widget Function(String orderTypeText) builder, {Key? key})
      : super(
          key: key,
          selector: (state) => state.orderTypeText,
          builder: builder,
        );
}

class InputPriceSelector extends PlaceOrderBuyStateSelector<InputPriceState> {
  InputPriceSelector(Widget Function(InputPriceState inputPriceState) builder,
      {Key? key})
      : super(
          key: key,
          selector: (state) => state.inputPriceState,
          builder: builder,
        );
}

// class ValidatorPriceInputSelector extends PlaceOrderBuyStateSelector<bool> {
//   ValidatorPriceInputSelector(Widget Function(bool validator) builder,
//       {Key? key})
//       : super(
//           key: key,
//           selector: (state) => state.inputPriceState.isDoneOrEmpty,
//           builder: builder,
//         );
// }

class InputVolumeSelector extends PlaceOrderBuyStateSelector<InputVolumeState> {
  InputVolumeSelector(
      Widget Function(InputVolumeState inputVolumeState) builder,
      {Key? key})
      : super(
          key: key,
          selector: (state) => state.inputVolumeState,
          builder: builder,
        );
}

// class ValidatorVolumeInputSelector extends PlaceOrderBuyStateSelector<bool> {
//   ValidatorVolumeInputSelector(Widget Function(bool validator) builder,
//       {Key? key})
//       : super(
//           key: key,
//           selector: (state) => state.inputVolumeState.isDoneOrEmpty,
//           builder: builder,
//         );
// }

class AvailableTradeSelector extends PlaceOrderBuyStateSelector<TitleState> {
  AvailableTradeSelector(
      Widget Function(TitleState availableTradeState) builder,
      {Key? key})
      : super(
          key: key,
          selector: (state) => state.availableTradeState,
          builder: builder,
        );
}

class MarginRateSelector extends PlaceOrderBuyStateSelector<MarginRateState> {
  MarginRateSelector(Widget Function(MarginRateState marginRateState) builder,
      {Key? key})
      : super(
          key: key,
          selector: (state) => state.marginRateState,
          builder: builder,
        );
}

class VisibleKeyboardSelector extends PlaceOrderBuyStateSelector<bool> {
  VisibleKeyboardSelector(Widget Function(bool visibleKeyboard) builder,
      {Key? key})
      : super(
          key: key,
          selector: (state) => state.visibleKeyboard,
          builder: builder,
        );
}

class ValueSelector extends PlaceOrderBuyStateSelector<ValueLabelState> {
  ValueSelector(Widget Function(ValueLabelState value) builder, {Key? key})
      : super(
          key: key,
          selector: (state) => state.valueLabel,
          builder: builder,
        );
}

class OrderNavigateStatusSelector
    extends PlaceOrderBuyStateSelector<OrderNavigateStatus> {
  OrderNavigateStatusSelector(
      Widget Function(OrderNavigateStatus status) builder,
      {Key? key})
      : super(
          key: key,
          selector: (state) => state.orderNavigateStatus,
          builder: builder,
        );
}

class SuggestSelector extends PlaceOrderBuyStateSelector<List<String>> {
  SuggestSelector(Widget Function(List<String> suggest) builder, {Key? key})
      : super(
          key: key,
          selector: (state) => state.inputPriceState.focus
              ? state.inputPriceState.suggests
              : state.inputVolumeState.focus
                  ? state.inputVolumeState.suggests
                  : [],
          builder: builder,
        );
}

class GtcEffectiveTimeSelector
    extends PlaceOrderBuyStateSelector<GtcEffectiveTimeState> {
  GtcEffectiveTimeSelector(Widget Function(GtcEffectiveTimeState time) builder,
      {Key? key})
      : super(
          key: key,
          selector: (state) => state.gtcEffectiveTimeState,
          builder: builder,
        );
}

class OrderSelector extends PlaceOrderBuyStateSelector<OrderState> {
  OrderSelector(Widget Function(OrderState orderState) builder, {Key? key})
      : super(
          key: key,
          selector: (state) => state.orderState,
          builder: builder,
        );
}

class HeaderLabelSelector extends PlaceOrderBuyStateSelector<HeaderLabelState> {
  HeaderLabelSelector(
      Widget Function(HeaderLabelState headerLabelState) builder,
      {Key? key})
      : super(
          key: key,
          selector: (state) => state.headerLabelState,
          builder: builder,
        );
}

class LotOfChartSelector extends PlaceOrderBuyStateSelector<Lot> {
  LotOfChartSelector(Widget Function(Lot lot) builder, {Key? key})
      : super(
          key: key,
          selector: (state) => state.lotOfChart,
          builder: builder,
        );
}

class MaxBuyVolumeSelector extends PlaceOrderBuyStateSelector<TitleState> {
  MaxBuyVolumeSelector(Widget Function(TitleState maxBuyVolumeState) builder,
      {Key? key})
      : super(
          key: key,
          selector: (state) => state.maxBuyVolumeState,
          builder: builder,
        );
}
