import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_wealth/data/model/stock_detail_entity.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/place_order_bloc.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/stock_info/stock_info_cubit.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/property_item/property_item_cubit.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/data/stock_helper.dart';

class StockInfoTop3 extends StatelessWidget {
  final ValueChanged<String> onTap;

  const StockInfoTop3({Key? key, required this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<PlaceOrderBloc>();
    return BlocListener<StockInfoCubit, PropertyItemState<StockDetailEntity?>>(
      listenWhen:
          (previous, state) =>
              previous.bidPrice != state.bidPrice ||
              previous.offerPrice != state.offerPrice,
      listener:
          (context, state) => bloc.add(
            Top3ChangeEvent(
              bidPriceList: state.bidPrice,
              offerPriceList: state.offerPrice,
            ),
          ),
      child: Column(
        children: [
          const _LineChart(),
          kSpacingHeight8,
          _Top3(onTap: onTap, oddLot: bloc.state.isOddLot),
        ],
      ),
    );
  }
}

class _Top3 extends StatelessWidget {
  final ValueChanged<String> onTap;
  final bool oddLot;

  const _Top3({Key? key, required this.onTap, required this.oddLot})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<StockInfoCubit, PropertyItemState<StockDetailEntity?>>(
      builder: (context, state) {
        return Column(
          children: [
            _RowChart(
              bidPrice: state.bidPrice[0],
              offerPrice: state.offerPrice[0],
              colorBid: state.colorBid1,
              colorOffer: state.colorOffer1,
              bidVolume: StockHelper.formatTop3Vol(state.item?.bidVol1, oddLot),
              offerVolume: StockHelper.formatTop3Vol(
                state.item?.offerVol1,
                oddLot,
              ),
              percentBid: state.percentBid1,
              percentOffer: state.percentOffer1,
              onTapBid: (value) => onTap(value),
              onTapOffer: (value) => onTap(value),
            ),
            kSpacingHeight8,
            _RowChart(
              bidPrice: state.bidPrice[1],
              offerPrice: state.offerPrice[1],
              colorBid: state.colorBid2,
              colorOffer: state.colorOffer2,
              bidVolume: StockHelper.formatTop3Vol(state.item?.bidVol2, oddLot),
              offerVolume: StockHelper.formatTop3Vol(
                state.item?.offerVol2,
                oddLot,
              ),
              percentBid: state.percentBid2,
              percentOffer: state.percentOffer2,
              onTapBid: (value) => onTap(value),
              onTapOffer: (value) => onTap(value),
            ),
            kSpacingHeight8,
            _RowChart(
              bidPrice: state.bidPrice[2],
              offerPrice: state.offerPrice[2],
              colorBid: state.colorBid3,
              colorOffer: state.colorOffer3,
              bidVolume: StockHelper.formatTop3Vol(state.item?.bidVol3, oddLot),
              offerVolume: StockHelper.formatTop3Vol(
                state.item?.offerVol3,
                oddLot,
              ),
              percentBid: state.percentBid3,
              percentOffer: state.percentOffer3,
              onTapBid: (value) => onTap(value),
              onTapOffer: (value) => onTap(value),
            ),
          ],
        );
      },
    );
  }
}

class _RowChart extends StatelessWidget {
  final String bidPrice;
  final String bidVolume;
  final double percentBid;
  final Color colorBid;
  final String offerPrice;
  final String offerVolume;
  final double percentOffer;
  final Color colorOffer;
  final ValueChanged<String> onTapBid;
  final ValueChanged<String> onTapOffer;

  const _RowChart({
    Key? key,
    required this.bidPrice,
    required this.bidVolume,
    required this.percentBid,
    required this.colorBid,
    required this.offerPrice,
    required this.offerVolume,
    required this.percentOffer,
    required this.colorOffer,
    required this.onTapBid,
    required this.onTapOffer,
  }) : super(key: key);

  Duration get _animationDuration => const Duration(milliseconds: 300);

  Widget _buildVolContainer({
    required double percent,
    required Color color,
    bool isLeft = true,
  }) {
    return LayoutBuilder(
      builder: (_, constraints) {
        return ClipRRect(
          borderRadius:
              isLeft
                  ? const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    bottomLeft: Radius.circular(16),
                  )
                  : const BorderRadius.only(
                    topRight: Radius.circular(16),
                    bottomRight: Radius.circular(16),
                  ),
          child: AnimatedContainer(
            height: 24,
            width: constraints.maxWidth * percent,
            duration: _animationDuration,
            color: color.withOpacity(0.16),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Flexible(
          flex: 1,
          child: GestureDetector(
            onTap:
                bidPrice == StockInfoCubit.nullText
                    ? null
                    : () => onTapBid(bidPrice),
            behavior: HitTestBehavior.opaque,
            child: Stack(
              children: [
                Align(
                  alignment: Alignment.centerRight,
                  child: _buildVolContainer(
                    color: colorBid,
                    percent: percentBid,
                  ),
                ),
                Positioned.fill(
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(right: 4),
                    child: AnimatedDefaultTextStyle(
                      style: vpTextStyle.captionRegular!.copyWith(
                        color: colorBid,
                      ),
                      duration: _animationDuration,
                      child: Text(bidPrice),
                    ),
                  ),
                ),
                Positioned.fill(
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      bidVolume,
                      style: vpTextStyle.captionRegular?.copyWith(
                        color: vpColor.textPrimary,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        kSpacingWidth8,
        Flexible(
          flex: 1,
          child: GestureDetector(
            onTap:
                offerPrice == StockInfoCubit.nullText
                    ? null
                    : () => onTapOffer(offerPrice),
            behavior: HitTestBehavior.opaque,
            child: Stack(
              children: [
                Align(
                  alignment: Alignment.centerLeft,
                  child: _buildVolContainer(
                    color: colorOffer,
                    percent: percentOffer,
                    isLeft: false,
                  ),
                ),
                Positioned.fill(
                  child: Container(
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.only(left: 4),
                    child: AnimatedDefaultTextStyle(
                      style: vpTextStyle.captionRegular!.copyWith(
                        color: colorOffer,
                      ),
                      duration: _animationDuration,
                      child: Text(offerPrice),
                    ),
                  ),
                ),
                Positioned.fill(
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      offerVolume,
                      style: vpTextStyle.captionRegular!.copyWith(
                        color: vpColor.textPrimary,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class _LineChart extends StatelessWidget {
  const _LineChart({Key? key}) : super(key: key);

  Duration get _animationDuration => const Duration(milliseconds: 300);

  @override
  Widget build(BuildContext context) {
    return ChangeTotalBestBidOfferVolSelector(
      (bidToOfferPercent) => LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          final bidPercent = bidToOfferPercent.item1;
          final offerPercent = bidToOfferPercent.item2;
          final isTwoPart = bidPercent != 0 && offerPercent != 0;
          final space = isTwoPart ? 2.0 : 0;
          return ClipRRect(
            borderRadius: BorderRadius.circular(2),
            child: SizedBox(
              height: 4,
              child: Stack(
                children: [
                  Positioned.fill(
                    child: AnimatedContainer(
                      color: isTwoPart ? themeData.bgPopup : themeData.gray300,
                      duration: _animationDuration,
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      AnimatedContainer(
                        width: constraints.maxWidth * bidPercent - space,
                        color: themeData.primary,
                        duration: _animationDuration,
                      ),
                      AnimatedContainer(
                        width: constraints.maxWidth * offerPercent - space,
                        color: themeData.red,
                        duration: _animationDuration,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
