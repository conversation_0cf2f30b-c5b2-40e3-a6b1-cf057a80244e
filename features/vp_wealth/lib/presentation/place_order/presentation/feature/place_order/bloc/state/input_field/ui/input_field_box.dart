import 'package:auto_size_text_field/auto_size_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/utils/wealth_constants.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/presentation/place_order/assets/stock_key_assets.dart';
import '../bloc/input_field_cubit.dart';
import '../utils/finder_keys.dart';

class InputFieldBox extends StatelessWidget {
  final InputFieldState state;
  final ValueChanged<String> onChange;
  final void Function(bool increase)? onTap;
  final FocusNode? focusNode;
  final bool onlyInput;
  final double width;
  final double height;
  final String? hintText;
  final bool enable;
  final double scrollPadding;
  final Color? errorBorderColor;
  final Color? errorBackgroundColor;
  final Color? errorTextColor;

  const InputFieldBox({
    super.key,
    required this.state,
    required this.onChange,
    this.onTap,
    this.focusNode,
    this.onlyInput = false,
    this.width = double.infinity,
    this.height = SizeUtils.kSize40,
    this.hintText,
    this.enable = true,
    this.scrollPadding = 20.0,
    this.errorBorderColor,
    this.errorBackgroundColor,
    this.errorTextColor,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(SizeUtils.kRadius4),
            color:
                state.isDoneOrEmpty
                    ? Colors.transparent
                    : errorBackgroundColor ?? themeData.red16,
            border: Border.all(
              width: SizeUtils.kSize1,
              color:
                  state.isDoneOrEmpty
                      ? Colors.transparent
                      : errorBorderColor ?? themeData.red,
            ),
          ),
          child: Row(
            children: [
              onlyInput
                  ? const SizedBox.shrink()
                  : _IconButton(
                    assetKey: Assets.icons.icSubtract.path,
                    onTap: () => onTap?.call(false),
                    key: FinderKeys.inputFieldDecreaseButton,
                  ),
              Expanded(
                child: AutoSizeTextField(
                  key: FinderKeys.inputFieldBox,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    disabledBorder: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                    isDense: true,
                    hintText: hintText,
                    hintStyle: vpTextStyle.body14?.copyWith(
                      color: themeData.gray500,
                    ),
                  ),
                  inputFormatters: [
                    ...state.inputFormatter,
                    LengthLimitingTextInputFormatter(state.maxLength),
                  ],
                  keyboardType: state.inputType,
                  textAlign: TextAlign.center,
                  enableInteractiveSelection: false,
                  onChanged: onChange,
                  controller: TextEditingController.fromValue(
                    TextEditingValue(
                      text: state.text,
                      selection: TextSelection.collapsed(
                        offset: state.text.length,
                      ),
                    ),
                  ),
                  style: vpTextStyle.body14?.copyWith(
                    color:
                        state.isDoneOrEmpty
                            ? themeData.black
                            : errorTextColor ?? themeData.black,
                  ),
                  focusNode: focusNode,
                  maxLines: null,
                  fullwidth: false,
                  minFontSize: 2,
                  scrollPadding: EdgeInsets.all(scrollPadding),
                ),
              ),
              onlyInput
                  ? const SizedBox.shrink()
                  : _IconButton(
                    assetKey: Assets.icons.icAdd.path,
                    onTap: () => onTap?.call(true),
                    key: FinderKeys.inputFieldIncreaseButton,
                  ),
            ],
          ),
        ),
        Positioned.fill(
          child:
              enable
                  ? const SizedBox.shrink()
                  : DecoratedBox(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(SizeUtils.kRadius4),
                      color: themeData.highlightBg.withOpacity(0.7),
                    ),
                  ),
        ),
      ],
    );
  }
}

class _IconButton extends StatelessWidget {
  final String assetKey;
  final VoidCallback? onTap;
  final Key? finderKey;

  const _IconButton({
    Key? key,
    required this.assetKey,
    required this.onTap,
    this.finderKey,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      key: finderKey,
      child: Container(
        padding: const EdgeInsets.symmetric(
          vertical: SizeUtils.kSize12,
          horizontal: SizeUtils.kSize16,
        ),
        color: Colors.transparent,
        child: CircleAvatar(
          backgroundColor: Colors.transparent,
          radius: 6,
          child: SvgPicture.asset(
            assetKey,
            color: vpColor.iconPrimary,
            package: WealthConstants.packageName,
          ),
        ),
      ),
    );
  }
}
