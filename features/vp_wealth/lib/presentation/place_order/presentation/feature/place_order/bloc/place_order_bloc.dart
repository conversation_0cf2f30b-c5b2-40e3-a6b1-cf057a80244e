import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:tuple/tuple.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/extensions/price_exts.dart';
import 'package:vp_common/utils/app_time_utils.dart';
import 'package:vp_core/vp_core.dart' as core;
import 'package:vp_wealth/data/model/assets/item_assets_model.dart';
import 'package:vp_wealth/data/model/stock_detail_entity.dart';
import 'package:vp_wealth/domain/request/order_request.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/presentation/place_order/data/model/params/available_trade_params.dart';
import 'package:vp_wealth/presentation/place_order/data/model/params/quotes_param.dart';
import 'package:vp_wealth/presentation/place_order/data/model/params/request_id_params.dart';
import 'package:vp_wealth/presentation/place_order/data/model/request/gtc_request.dart';
import 'package:vp_wealth/presentation/place_order/data/model/request/order_recommedation_request.dart';
import 'package:vp_wealth/presentation/place_order/data/model/request/order_request.dart';
import 'package:vp_wealth/presentation/place_order/data/model/response/available_trade_response.dart';
import 'package:vp_wealth/presentation/place_order/domain/domain.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/exchange.dart';
import 'package:vp_wealth/presentation/place_order/gen/locale_keys.g.dart';
import 'package:vp_wealth/presentation/place_order/model/app_auth_obj.dart';
import 'package:vp_wealth/presentation/place_order/model/check_model.dart';
import 'package:vp_wealth/presentation/place_order/model/rc_arguments.dart';
import 'package:vp_wealth/presentation/place_order/model/request/pre_check_order_request.dart';
import 'package:vp_wealth/presentation/wealths/socket/model/socket_order_data.dart';
import 'package:vp_wealth/presentation/wealths/socket/socket_account_connect.dart';

import './place_order_bloc_map_import.dart';
import 'place_order_state.dart';
import 'state/input_state/input_price_state.dart';
import 'state/input_state/input_volume_state.dart';
import 'state/place_order_message.dart';

part 'place_order_event.dart';

class PlaceOrderBloc extends Bloc<PlaceOrderEvent, PlaceOrderState> {
  PlaceOrderBloc({
    String? symbol,
    Order order = Order.buy,
    core.SubAccountType? subAccount,
    ItemAssetsModel? wealthModel,
    required this.repository,
    bool? recommendDetailsOrder,
    this.recommendationDetailModel,
    FocusKeyboard focusKeyboard = FocusKeyboard.none,
    OrderType orderType = OrderType.lo,
    SessionType? sessionType,
    bool isTest = false,
  }) : recommendDetailsOrder = recommendDetailsOrder ?? false,
       super(
         PlaceOrderState(
           symbol: symbol,
           wealthModel: wealthModel,
           subAccount:
               subAccount == core.SubAccountType.normal ||
                       subAccount == core.SubAccountType.margin
                   ? subAccount!
                   : core.GetIt.instance<core.SubAccountCubit>()
                       .defaultSubAccount
                       .toSubAccountType,
           fillInitRecommendDetailsOrder: recommendDetailsOrder ?? false,
           inputPriceState: InputPriceState(
             text: '',
             ceilingPrice: 0.0,
             floorPrice: 0.0,
             orderType: orderType,
             order: order,
             focusKeyboard: focusKeyboard,
           ),
           inputVolumeState: InputVolumeState(
             text: '',
             maxVolume: 0,
             trade: 0,
             orderType: orderType,
             focusKeyboard: focusKeyboard,
             order: order,
           ),
           marginRate: 0,
           gtcEffectiveTime: const GtcEffectiveTime(),
           isTest: isTest,
         ),
       ) {
    on<InitEvent>(_initEvent);
    on<ChangeNewOrderEvent>(_changeNewOrderEvent);
    on<ChangeSubAccountTypeEvent>(_changeSubAccountTypeEvent);
    on<ChangeTypeEvent<OrderType>>(_changeOrderType);
    on<ChangeInputPriceEvent>(_changeInputPriceEvent);
    on<TapPriceButtonEvent>(_tapPriceButtonEvent);
    on<ChangeVolumeEvent>(_changeVolumeEvent);
    on<TapVolumeButtonEvent>(_tapVolumeButtonEvent);
    on<OrderSubmitEvent>(_orderSubmitEvent);
    on<FocusKeyboardEvent>(_focusKeyboardEvent);
    on<TapNextEvent>(_tapNextEvent);
    on<TapSuggestEvent>(_tapSuggestEvent);
    on<ChangeGtcEffectiveTimeEvent>(_changeGtcEffectiveTimeEvent);
    on<ChangeSymbolEvent>(_changeSymbolEvent);
    on<ChangeOrderEvent>(_changeOrderEvent);
    on<Top3ChangeEvent>(_top3ChangeEvent);
    on<TapPriceEvent>(_tapPriceEvent);
    on<EndHighlightErrorAnimationEvent>(_endHighlightErrorAnimationEvent);
    on<CallAvailableTrade>(_callAvailableTrade);
    on<CallQuotes>(_callQuotes);
    on<CallAllSymbol>(_callAllSymbol);
    on<CallStockInfo>(_callStockInfo);
    on<CallKrx>(_callKrx);
    on<RefreshEvent>(_refreshEvent);
    on<FillInitRecommendDetailsOrder>(_fillInitRecommendDetailsOrder);
    on<ClearPriceEvent>(_clearPriceEvent);
    on<ClearVolumeEvent>(_clearVolumeEvent);
    on<ScrollPaddingChangeEvent>(_paddingKeyboardChangeEvent);
    on<TapOrderLabelEvent>(_tapOrderLabelEvent);
    on<PushMessageEvent>(_pushMessageEvent);
    on<ChangeLotOfChartEvent>(_changeLotOfChartEvent);
    on<OnSuccessOrderEvent>(_onSuccessOrderEvent);
    // on<OnGetPriceBoardBuyInStatus>(_getPriceBoardBuyInStatus);
  }

  final PlaceOrderRepository repository;
  final bool recommendDetailsOrder;
  final RcArguments? recommendationDetailModel;
  final ScrollController scrollController = ScrollController();

  List<core.SubAccountType> get subAccountTypes =>
      core.GetIt.instance<core.SubAccountCubit>().subAccountsStock
          .map((subAccountsStock) => subAccountsStock.toSubAccountType)
          .toList();

  bool get canChangeSubAccountType =>
      state.wealthModel != null
          ? false
          : core.GetIt.instance
                  .get<core.SubAccountCubit>()
                  .subAccountsStock
                  .length >
              1;

  bool get canChangeOrderType =>
      state.wealthModel != null
          ? false
          : !(recommendDetailsOrder && state.exchange == Exchange.upCom);

  bool get canChangeSymbol =>
      state.wealthModel != null ? false : !recommendDetailsOrder;

  String get _requestId => String.fromCharCodes(
    List.generate(20, (index) => Random().nextInt(33) + 89),
  );

  double get _maxVolumeGtc => state.exchange.isHose ? 500000.0 : 1000000.0;

  String get _userId =>
      state.wealthModel != null
          ? (state.wealthModel?.accountNo ?? '')
          : core.GetIt.instance
                  .get<core.SubAccountCubit>()
                  .getSubAccount(state.subAccount)
                  ?.id ??
              '';

  String get userId => _userId;

  core.SubAccountModel get subAccountModel =>
      core.GetIt.instance<core.SubAccountCubit>().subAccountsStock[state
          .subAccount
          .index];

  StreamSubscription? _orderHistorySubscription;

  @override
  void onChange(Change<PlaceOrderState> change) {
    super.onChange(change);
    final current = change.currentState;
    final next = change.nextState;
    if (next.fillInitRecommendDetailsOrder) {
      add(FillInitRecommendDetailsOrder());
    }
    if ((current.exchange != next.exchange && next.exchange != null) ||
        current.orderType.isGtc != next.orderType.isGtc) {
      add(
        PushMessageEvent(
          PlaceOrderMessage(
            PlaceOrderMessageType.updateOrderSuggest,
            Tuple2(next.exchange, next.orderType),
          ),
        ),
      );
    }
    if (current.isOddLot != next.isOddLot) {
      add(CallStockInfo());
    }
    if (current.showConfirmOrder != next.showConfirmOrder) {
      add(
        PushMessageEvent(
          PlaceOrderMessage(
            PlaceOrderMessageType.changeConfirmOrder,
            next.showConfirmOrder,
          ),
        ),
      );
    }
    // if (current.inputPriceState.stockType != next.inputPriceState.stockType) {
    //   add(PushMessageEvent(PlaceOrderMessage(
    //       PlaceOrderMessageType.changeStockType,
    //       next.inputPriceState.stockType)));
    // }
    if (current.subAccount != next.subAccount) {
      add(
        PushMessageEvent(
          PlaceOrderMessage(
            PlaceOrderMessageType.changeSubAccount,
            core.GetIt.instance
                .get<core.SubAccountCubit>()
                .subAccountsStock[next.subAccount.index],
          ),
        ),
      );
    }
    if (current.inputPriceState != next.inputPriceState ||
        current.inputVolumeState != next.inputVolumeState) {
      add(ScrollPaddingChangeEvent());
    }
  }

  num validAvailableTrade(double availableTrade) =>
      availableTrade < 0 && state.isNormalSubAccount ? 0 : availableTrade;

  num getMaxVolumeByAvailableTradeResponse(
    AvailableTradeResponse availableTrade,
  ) {
    return state.orderType.isGtc
        ? _maxVolumeGtc
        : state.order.isBuy
        ? availableTrade.maxVolumeBuy
        : availableTrade.maxVolumeSell;
  }

  void _pushMessageEvent(PushMessageEvent event, emit) {
    state.send(emit, event.message);
  }

  void _listenChangeOrderHistoryStream() {
    _orderHistorySubscription?.cancel();
    _orderHistorySubscription = SocketAccountConnect().orderStream.listen((
      SocketAccountData data,
    ) {
      if (core.GetIt.instance.get<core.SubAccountCubit>().subAccountTypeFromId(
            data.accountId,
          ) ==
          state.subAccount) {
        add(CallAvailableTrade(isLoading: true));
      }
    });
  }

  void _initEvent(_, emit) {
    _listenChangeOrderHistoryStream();
    emit(state.copyWith(newOrder: true, showConfirmOrder: false));
    add(CallQuotes(isLoading: true));
    add(CallAllSymbol(isLoading: true));
    add(CallAvailableTrade(isLoading: true));
    add(CallStockInfo(isLoading: true));
  }

  void _changeNewOrderEvent(ChangeNewOrderEvent event, emit) {
    emit(state.copyWith(newOrder: event.value));
  }

  Future<void> _callQuotes(CallQuotes event, emit) async {
    if (state.symbol == null) return;
    emit(state.call(ApiStatus.callQuotes, event.isLoading));
    try {
      final stockOrderEntity = await repository.getQuotes(
        QuotesParam(symbols: state.symbolText),
      );
      emit(
        state.copyWith
            .inputPriceState(
              ceilingPrice: stockOrderEntity.ceilingPrice,
              floorPrice: stockOrderEntity.floorPrice,
              stockType: stockOrderEntity.stockType,
              exchange: stockOrderEntity.exchange,
            )
            .copyWith(
              symbol: stockOrderEntity.symbol,
              fullName: stockOrderEntity.fullName,
              referencePrice: stockOrderEntity.referencePrice,
            ),
      );
    } catch (e, stackTrace) {
      print('Error At: $stackTrace');
      emit(state.error);
      add(
        PushMessageEvent(
          PlaceOrderMessage(PlaceOrderMessageType.errorCallApi, e),
        ),
      );
    } finally {
      emit(state.end(ApiStatus.callQuotes));
    }
  }

  void _callAllSymbol(CallAllSymbol event, emit) async {
    emit(state.call(ApiStatus.callAllSymbol, event.isLoading));
    try {
      final allSymbolList = await repository.getQuotesAll();
      emit(state.copyWith(allSymbolList: allSymbolList));
    } catch (e) {
      emit(state.error);
      add(
        PushMessageEvent(
          PlaceOrderMessage(PlaceOrderMessageType.errorCallApi, e),
        ),
      );
    } finally {
      emit(state.end(ApiStatus.callAllSymbol));
    }
  }

  void _callStockInfo(CallStockInfo event, emit) async {
    if (state.symbol == null) return;
    emit(state.call(ApiStatus.callStockInfo, event.isLoading));
    StockDetailEntity? stock;
    StockDetailEntity? oddLotStock;
    try {
      if (state.isBuyIn) {
        stock = await repository
            .getStockInfoByListMultipleBoard(state.symbol!)
            .then((stockList) => stockList.first);
        return;
      }
      stock = await core.GetIt.instance
          .get<WealthRepository>()
          .getStocks(state.symbol!)
          .then((oddLotStockList) => oddLotStockList.first);
      //  final details =
      //       await mainStockRepo.getStocks(listSymbols.symbolsFormat);
      // stock = await homeRepository
      //     .getStockList(state.symbol!)
      //     .then((stockList) => stockList.first);
      // if (state.isOddLot) {
      //   oddLotStock = await homeRepository
      //       .getOLStockInfo(state.symbol!)
      //       .then((oddLotStockList) => oddLotStockList.first);
      // }
    } catch (e) {
      /// code IVBERR02 is case not data record of stock from investment tool api
      /// no show error message with this case
      final response = e is ResponseError && e.code == 'IVBERR02' ? null : e;
      add(
        PushMessageEvent(
          PlaceOrderMessage(PlaceOrderMessageType.errorCallApi, response),
        ),
      );
    } finally {
      if (state.isOddLot &&
          !state.isBuyIn &&
          stock != null &&
          oddLotStock != null) {
        stock
          ..totalVolume = oddLotStock.totalVolume
          ..totalTradingValue = oddLotStock.totalTradingValue
          ..bidPrice1 = oddLotStock.bidPrice1
          ..bidPrice2 = oddLotStock.bidPrice2
          ..bidPrice3 = oddLotStock.bidPrice3
          ..bidVol1 = oddLotStock.bidVol1
          ..bidVol2 = oddLotStock.bidVol2
          ..bidVol3 = oddLotStock.bidVol3
          ..offerPrice1 = oddLotStock.offerPrice1
          ..offerPrice2 = oddLotStock.offerPrice2
          ..offerPrice3 = oddLotStock.offerPrice3
          ..offerVol1 = oddLotStock.offerVol1
          ..offerVol2 = oddLotStock.offerVol2
          ..offerVol3 = oddLotStock.offerVol3;
      }
      emit(state.copyWith(stockEntity: stock).end(ApiStatus.callStockInfo));
    }
  }

  void _refreshEvent(_, __) async {
    add(CallQuotes(isLoading: true));
    add(CallAvailableTrade(isLoading: true));
    add(CallStockInfo(isLoading: true));
  }

  void _changeOrderEvent(ChangeOrderEvent event, emit) async {
    if (event.value == state.order || recommendDetailsOrder) return;
    final newState = state.changeOrder(event.value);
    if (state.isBuyIn && event.value == Order.buy) {
      newState.changeOrderType(OrderType.lo);
    }
    emit(newState);
    add(ClearVolumeEvent());
    add(ClearPriceEvent());
    add(CallAvailableTrade());
  }

  void _callKrx(CallKrx event, emit) async {
    emit(state.call(ApiStatus.callKrx, event.isLoading));
    try {
      final listKrx = await repository.getMultiBoardStockInfoByList();
      final listSymbol = listKrx.map((e) => e.symbol);
      if (listSymbol.contains(state.symbolText) &&
          state.inputVolumeState.trade > 0) {
        add(CallAvailableTrade());
      } else {
        emit(state.copyWith(symbol: null));
      }
    } catch (e) {
      emit(state.error);
      add(
        PushMessageEvent(
          PlaceOrderMessage(PlaceOrderMessageType.errorCallApi, e),
        ),
      );
    } finally {
      emit(state.end(ApiStatus.callKrx));
    }
  }

  void _changeSubAccountTypeEvent(ChangeSubAccountTypeEvent event, emit) async {
    if (event.value == state.subAccount) return;
    emit(state.copyWith(subAccount: event.value));
    add(CallAvailableTrade());
  }

  void _changeSymbolEvent(ChangeSymbolEvent event, emit) async {
    if (event.value == state.symbol) return;
    emit(
      state.copyWith(symbol: event.value, fullName: null).changeExchange(null),
    );
    add(ClearVolumeEvent());
    add(ClearPriceEvent());
    add(CallQuotes());
    add(CallAvailableTrade());
    add(CallStockInfo());
  }

  void _changeOrderType(ChangeTypeEvent<OrderType> event, emit) async {
    if (event.value == state.orderType) return;
    if (state.inputVolumeState.value.isOddLot) {
      add(ClearVolumeEvent());
    }
    add(ClearPriceEvent());
    emit(state.changeOrderType(event.value));
    add(CallAvailableTrade());
  }

  void _changeInputPriceEvent(ChangeInputPriceEvent event, emit) async {
    emit(state.copyWith.inputPriceState(text: event.value));
    add(CallAvailableTrade());
  }

  void _tapPriceEvent(TapPriceEvent event, emit) async {
    final priceText = event.value;
    if (priceText == state.inputPriceState.text) return;
    add(PushMessageEvent(PlaceOrderMessage(PlaceOrderMessageType.tapPrice)));
    if (state.isChoiceConditionalOrder) {
      _handleTapPriceEventHaveConditionalOrder(event, emit);
    } else {
      _handleTapPriceEventOldOptions(event, emit);
    }
  }

  void _clearPriceEvent(_, emit) =>
      emit(state.copyWith.inputPriceState(text: '').changeSessionType(null));

  void _clearVolumeEvent(_, emit) =>
      emit(state.copyWith.inputVolumeState(text: ''));

  void _paddingKeyboardChangeEvent(_, __) {
    if (state.visibleKeyboard && scrollController.hasClients) {
      WidgetsBinding.instance.addPostFrameCallback(
        (_) => scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 100),
          curve: Curves.ease,
        ),
      );
    }
  }

  void _handleTapPriceEventHaveConditionalOrder(TapPriceEvent event, emit) {
    final priceText = event.value;
    if (priceText == state.inputPriceState.text) return;
    emit(
      state.copyWith
          .inputPriceState(text: priceText)
          .copyWith(isPriceTapping: true),
    );
    add(CallAvailableTrade());
  }

  void _handleTapPriceEventOldOptions(TapPriceEvent event, emit) {
    final priceText = event.value;
    if (priceText == state.inputPriceState.text) return;
    if (state.newOrder ||
        state.inputPriceState.focus &&
            SessionType.values.any(
              (e) => e.name.toLowerCase() == priceText.toLowerCase(),
            )) {
      add(const FocusKeyboardEvent(FocusKeyboard.volume));
    }
    final sessionType = SessionType.parseString(priceText);
    emit(
      state.copyWith
          .inputPriceState(text: priceText)
          .changeSessionType(sessionType),
    );
    add(CallAvailableTrade());
  }

  void _tapOrderLabelEvent(_, __) {
    add(ClearPriceEvent());
    add(const FocusKeyboardEvent(FocusKeyboard.price));
    add(CallAvailableTrade());
  }

  void _tapPriceButtonEvent(TapPriceButtonEvent event, emit) {
    final newText = state.inputPriceState.tap(increase: event.increase);
    emit(state.copyWith.inputPriceState(text: newText));
    add(CallAvailableTrade());
  }

  void _changeVolumeEvent(ChangeVolumeEvent event, emit) =>
      emit(state.copyWith.inputVolumeState(text: event.value));

  void _tapVolumeButtonEvent(TapVolumeButtonEvent event, emit) {
    final newText = state.inputVolumeState.tap(increase: event.increase);
    emit(state.copyWith.inputVolumeState(text: newText));
  }

  void _focusKeyboardEvent(FocusKeyboardEvent event, emit) =>
      emit(state.focusKeyboard(event.value));

  void _tapNextEvent(_, __) => add(
    FocusKeyboardEvent(
      state.inputPriceState.focus ? FocusKeyboard.volume : FocusKeyboard.price,
    ),
  );

  void _tapSuggestEvent(TapSuggestEvent event, emit) {
    if (state.inputPriceState.focus) {
      add(TapPriceEvent(event.value));
    }
    if (state.inputVolumeState.focus) {
      String volumeText = state.inputVolumeState.tapSuggest(event.value);
      if (volumeText == state.inputVolumeState.text) return;
      emit(state.copyWith.inputVolumeState(text: volumeText));
      add(PushMessageEvent(PlaceOrderMessage(PlaceOrderMessageType.tapVolume)));
    }
  }

  void _changeGtcEffectiveTimeEvent(ChangeGtcEffectiveTimeEvent event, emit) =>
      emit(
        state.copyWith(
          gtcEffectiveTime: GtcEffectiveTime(
            start: event.value.first,
            end: event.value.last,
          ),
        ),
      );

  void _top3ChangeEvent(Top3ChangeEvent event, emit) => emit(
    state.copyWith.inputPriceState(
      bidPrices: event.bidPriceList,
      offerPrices: event.offerPriceList,
    ),
  );

  void _fillInitRecommendDetailsOrder(_, emit) {
    if (state.stockEntity?.price == null || !recommendDetailsOrder) {
      return;
    }
    int multiple = 1000;
    if (state.order.isBuy) {
      if (state.inputPriceState.floorPrice >
          (recommendationDetailModel?.maxRecommendedPrice ?? 0) * multiple) {
        return;
      }
    } else if (!state.order.isBuy) {
      if (state.inputPriceState.ceilingPrice <
          (recommendationDetailModel?.minRecommendedPrice ?? 0) * multiple) {
        return;
      }
    }

    emit(
      state.copyWith
          .inputPriceState(
            text: state.stockEntity!.price.getPriceFormatted(
              currency: '',
              convertToThousand: true,
            ),
          )
          .copyWith(fillInitRecommendDetailsOrder: false),
    );
    add(CallAvailableTrade());
  }

  void _changeLotOfChartEvent(ChangeLotOfChartEvent event, emit) {
    emit(state.copyWith(lotOfChart: event.value));
  }

  Future _callAvailableTrade(CallAvailableTrade event, emit) async {
    if (state.symbol == null) return;
    emit(state.call(ApiStatus.callAvailableTrade, event.isLoading));
    try {
      final availableTrade = await repository.getAvailableTrade(
        _userId,
        AvailableTradeParams(
          symbol: state.symbol,
          quotePrice: state.quotePriceAvailableTradeParams,
        ),
      );
      emit(
        state.copyWith
            .inputVolumeState(
              maxVolume: getMaxVolumeByAvailableTradeResponse(
                availableTrade as AvailableTradeResponse,
              ),
              trade: availableTrade.maxVolumeSell,
            )
            .copyWith(
              availableTrade: validAvailableTrade(
                availableTrade.availableTrade,
              ),
              marginRate: availableTrade.marginRate,
            ),
      );
    } catch (e) {
      emit(state.error);
      add(
        PushMessageEvent(
          PlaceOrderMessage(PlaceOrderMessageType.errorCallApi, e),
        ),
      );
    } finally {
      emit(state.end(ApiStatus.callAvailableTrade));
    }
  }

  Future _callOrder(
    CheckModel initTransaction,
    AppAuthObj obj,
    PreCheckOrderRequest preCheckRequest,
  ) {
    final request = OrderRequest(
      authType: obj.type,
      tokenId: initTransaction.tokenid ?? '',
      transactionId: initTransaction.transactionId ?? '',
      instrument: preCheckRequest.instrument,
      volume: preCheckRequest.volume,
      order: preCheckRequest.order,
      type: preCheckRequest.type,
      price: preCheckRequest.price,
      buyIn: preCheckRequest.buyIn,
    );
    final param = RequestIdParams(requestId: _requestId);
    return repository.orders(_userId, param, request);
  }

  Future _orderSubmitEvent(OrderSubmitEvent event, emit) async {
    try {
      emit(state.call(ApiStatus.loadingOrder));
      if (state.wealthModel == null) {
        if (recommendDetailsOrder &&
            (recommendationDetailModel?.isBroker ?? false)) {
          await _callRecommendationOrder();
        } else {
          final preCheckRequest = await _callPreCheckOrder();
          final initTransaction = await _callInitVerifyTransaction();

          final obj = AppAuthObj(
            transactionId: initTransaction.transactionId,
            tokenid: initTransaction.tokenid,
            type: initTransaction.authtype,
          );

          if (state.orderType.isGtc) {
            await _callConditionOrder(initTransaction, obj, preCheckRequest);
          } else {
            await _callOrder(initTransaction, obj, preCheckRequest);
          }
        }
      } else {
        await _callOrderForWealth();
      }

      emit(state.copyWith(showConfirmOrder: event.value ?? false));
      add(OnSuccessOrderEvent());
    } catch (e) {
      final cbtt = CBTT(e);
      if (cbtt.isShowDialog) {
        return add(
          PushMessageEvent(
            PlaceOrderMessage(
              PlaceOrderMessageType.showCBTTConfirm,
              cbtt.content,
            ),
          ),
        );
      }
      add(
        PushMessageEvent(
          PlaceOrderMessage(PlaceOrderMessageType.errorCallApi, e),
        ),
      );
    } finally {
      emit(state.end(ApiStatus.loadingOrder));
    }
  }

  Future _callConditionOrder(
    CheckModel initTransaction,
    AppAuthObj obj,
    PreCheckOrderRequest preCheckRequest,
  ) {
    final request = GtcRequest(
      obj.type,
      initTransaction.tokenid!,
      initTransaction.transactionId!,
      EngineInput(
        symbol: preCheckRequest.instrument,
        startDate: state.gtcEffectiveTime.startText,
        endDate: state.gtcEffectiveTime.endText,
        price: preCheckRequest.conditionPrice,
        volume: preCheckRequest.volume,
        order: preCheckRequest.order,
      ),
    );
    final param = RequestIdParams(requestId: _requestId);
    return repository.conditionOrder(_userId, param, request);
  }

  Future _callRecommendationOrder() {
    final request = OrderRecommendationRequest(
      side: state.order,
      channel: OrderChannelEnum.customer.value,
      requestId: _requestId,
      stockRecommendationId: recommendationDetailModel?.id.toString(),
      orderCode: state.orderType,
      sessionType: state.sessionType,
      subAccount: state.subAccount,
      transactionDate: AppTimeUtils.getDateTimeString(),
      volume: state.inputVolumeState.value.toInt().toString(),
      price: state.inputPriceState.value.toString(),
      collaborationId: recommendationDetailModel?.createdBy,
      accountId: _userId,
      symbol: state.symbol,
    );
    return repository.orderRecommendation(request);
  }

  Future<CheckModel> _callInitVerifyTransaction() =>
      repository.initVerifyTransaction(_userId);

  Future<PreCheckOrderRequest> _callPreCheckOrder() async {
    final request = PlaceOrderBlocGetParamsUtil().getPreCheckOrderRequest(
      state,
    );

    final param = RequestIdParams(requestId: _requestId);
    final result = await repository.preCheckOrder(_userId, param, request);
    if (!result.isSuccess()) {
      throw ResponseError(message: result.em);
    }
    return request;
  }

  void _endHighlightErrorAnimationEvent(_, emit) =>
      emit(state.copyWith(endHighlightErrorAnimation: true));

  void _onSuccessOrderEvent(_, emit) {
    add(ClearPriceEvent());
    add(ClearVolumeEvent());
    add(const ChangeGtcEffectiveTimeEvent([null, null]));
    add(
      PushMessageEvent(
        PlaceOrderMessage(
          PlaceOrderMessageType.successOrder,
          'Đặt lệnh thành công',
        ),
      ),
    );
  }

  // void _getPriceBoardBuyInStatus(OnGetPriceBoardBuyInStatus even, emit) async {
  //   emit(state.call(ApiStatus.callPriceBoardBuyInStatus, true));
  //   final priceBoardBuyInStatus =
  //       await core.RemoteConfigService().getPriceBoardBuyInStatus();
  //   even.onDone(priceBoardBuyInStatus);
  //   emit(state.end(ApiStatus.callPriceBoardBuyInStatus));
  // }

  // Wealth
  Future _callOrderForWealth() {
    final request = OrderForWealthRequest(
      volume: state.inputVolumeState.value,
      symbol: state.symbolText,
      type: state.orderType.dataWealthServer,
      price: state.orderType.isLo ? state.inputPriceState.value : null,
      copierId: state.wealthModel?.copierId ?? -1,
    );
    if (state.order.isBuy) {
      return core.GetIt.instance.get<WealthRepository>().proactiveBuyStock(
        request,
      );
    }
    return core.GetIt.instance.get<WealthRepository>().proactiveSellStock(
      request,
    );
  }

  @override
  Future<void> close() {
    _orderHistorySubscription?.cancel();
    return super.close();
  }
}
