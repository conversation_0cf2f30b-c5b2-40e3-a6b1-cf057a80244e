import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/data/utils/constains.dart';

import '../../../../../utils/bloc/dialog_select_symbol/dialog_select_symbol_cubit.dart';
import '../../../../../utils/place_order_utils.dart';
import '../../bloc/bloc.dart';

class SymbolButton extends StatelessWidget {
  const SymbolButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<PlaceOrderBloc>();
    return TypeItemButton(
      title: BlocBuilder<PlaceOrderBloc, PlaceOrderState>(
        builder: (context, state) {
          return state.symbol == null
              ? Text(
                state.symbolText,
                style: vpTextStyle.body14?.copyWith(color: vpColor.textPrimary),
              )
              : Row(
                children: [
                  Text(
                    state.symbolText,
                    style: vpTextStyle.body14?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                  ),
                  kSpacingWidth4,
                  Text(
                    state.exchangeText,
                    style: vpTextStyle.captionRegular?.copyWith(
                      color: themeData.gray500,
                    ),
                  ),
                ],
              );
        },
      ),
      bottomSheetBuilder:
          (_) => BlocProvider<DialogSelectSymbolCubit>(
            create:
                (_) => DialogSelectSymbolCubit(
                  order: bloc.state.order,
                  orderType: bloc.state.orderType,
                  subAccount: bloc.state.subAccount,
                  allSymbolList: bloc.state.allSymbolList,
                ),
            child: const DialogSelectSymbol(),
          ),
      onDataReturn: (symbol) {
        bloc.add(ChangeSymbolEvent(symbol));
      },
      canChange: bloc.canChangeSymbol,
    );
  }
}
