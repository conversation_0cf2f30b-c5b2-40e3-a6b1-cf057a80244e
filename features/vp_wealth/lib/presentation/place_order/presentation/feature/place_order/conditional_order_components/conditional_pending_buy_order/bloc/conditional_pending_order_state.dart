part of 'conditional_pending_order_bloc.dart';

final initPriceState = ConditionalOrderActivationPriceState(
  formatPrice: true,
  priceNotFormat: '',
  orderType: OrderType.gtc,
  focusKeyboard: FocusKeyboard.price,
  highlight: false,
  exchange: Exchange.hsx,
  conditionalFocusKeyBoard: ConditionalFocusKeyBoard.none,
);

class ConditionalPendingOrderState
    implements
        BlocStateHaveConditionalPriceInput,
        BlocStateConditionalInterface {
  final SecuritiesPortfolioResponsesModel? securitiesPortfolioResponsesModel;
  final num? activationPrice;
  final DateTime? validTime;
  final ConditionalOrderPriceTypeEnum? activationPriceType;
  final EffectiveTimeModel effectiveTime;
  final OrderNavigateStatus? orderNavigateStatusOtherInput;
  final ConditionalDilutionTypeEnum? dilutionType;
  final Exchange exchange;
  final String? stockType;
  final ConditionalPriceInputState? conditionalPriceInputState;
  final ConditionalPriceInputState? activationPriceInputState;
  final ConditionalVolumeInputState? conditionalVolumeInputState;

  final ConditionalFocusKeyBoard? conditionalFocusKeyboard;
  final Order order;
  final num? maxVolumeToSell;
  final num? maxVolumeToBuy;
  final num? volumeFromSuggestion;
  final MarketCommandEnum? marketCommand;
  final SubAccountType? subAccountType;
  final bool isSuccessOrder;

  ConditionalPendingOrderState({
    this.activationPrice,
    this.validTime,
    this.activationPriceType,
    required this.effectiveTime,
    this.orderNavigateStatusOtherInput,
    this.dilutionType,
    this.exchange = Exchange.hsx,
    this.stockType,
    this.conditionalFocusKeyboard = ConditionalFocusKeyBoard.none,
    this.conditionalPriceInputState,
    this.activationPriceInputState,
    this.conditionalVolumeInputState,
    this.order = Order.buy,
    this.maxVolumeToSell,
    this.volumeFromSuggestion,
    this.maxVolumeToBuy,
    this.marketCommand,
    this.securitiesPortfolioResponsesModel,
    this.subAccountType,
    this.isSuccessOrder = false,
  });

  ConditionalPendingOrderState copyWith({
    SecuritiesPortfolioResponsesModel? securitiesPortfolioResponsesModel,
    DateTime? validTime,
    ConditionalOrderPriceTypeEnum? activationPriceType,
    double? price,
    num? volume,
    EffectiveTimeModel? effectiveTime,
    Order? order,
    // VolumeInputState? volumeInputState,
    num? maxVolumeToSell,
    int? marginRate,
    bool? showConfirmOrder,
    FocusKeyboard? focusKeyboard,
    bool? formatPrice,
    String? priceNotFormat,
    bool highlightPriceInput = false,
    bool highlightVolumeInput = false,
    List<String>? bidPriceSuggestList,
    List<String>? offerPriceSuggestList,
    Object? responseError,
    bool clearPrice = false,
    bool clearVolume = false,
    bool endHighlightErrorAnimation = false,
    double? referencePrice,
    double? closePrice,
    Exchange? exchange,
    String? stockType,
    OrderType? orderType,
    OrderNavigateStatus? orderNavigateStatusOtherInput,
    ConditionalDilutionTypeEnum? dilutionType,
    ConditionalFocusKeyBoard? conditionalFocusKeyboard,
    // ========= New
    ConditionalPriceInputState? conditionalPriceInputState,
    ConditionalPriceInputState? activationPriceInputState,
    ConditionalVolumeInputState? conditionalVolumeInputState,
    num? volumeFromSuggestion,
    num? maxVolumeToBuy,
    MarketCommandEnum? marketCommand,
    SubAccountType? subAccountType,
    bool isSuccessOrder = false,
  }) {
    return ConditionalPendingOrderState(
      validTime: validTime ?? this.validTime,
      activationPriceType: activationPriceType ?? this.activationPriceType,
      effectiveTime: effectiveTime ?? this.effectiveTime,
      dilutionType: dilutionType ?? this.dilutionType,
      orderNavigateStatusOtherInput:
          orderNavigateStatusOtherInput ?? this.orderNavigateStatusOtherInput,
      exchange: exchange ?? this.exchange,
      stockType: stockType ?? this.stockType,
      conditionalFocusKeyboard:
          conditionalFocusKeyboard ?? this.conditionalFocusKeyboard,
      conditionalPriceInputState:
          conditionalPriceInputState ?? this.conditionalPriceInputState,
      activationPriceInputState:
          activationPriceInputState ?? this.activationPriceInputState,
      conditionalVolumeInputState:
          conditionalVolumeInputState ?? this.conditionalVolumeInputState,
      order: order ?? this.order,
      maxVolumeToSell: maxVolumeToSell ?? this.maxVolumeToSell,
      volumeFromSuggestion: volumeFromSuggestion ?? this.volumeFromSuggestion,
      maxVolumeToBuy: maxVolumeToBuy ?? this.maxVolumeToBuy,
      marketCommand: marketCommand ?? this.marketCommand,
      securitiesPortfolioResponsesModel: securitiesPortfolioResponsesModel ??
          this.securitiesPortfolioResponsesModel,
      subAccountType: subAccountType ?? this.subAccountType,
      isSuccessOrder: isSuccessOrder,
    );
  }

  factory ConditionalPendingOrderState.init() {
    return ConditionalPendingOrderState(
      activationPrice: 0,
      validTime: DateTime.now(),
      activationPriceType: ConditionalOrderPriceTypeEnum.greaterThan,
      effectiveTime:
          EffectiveTimeModel(start: DateTime.now(), end: DateTime.now()),
      dilutionType: ConditionalDilutionTypeEnum.cancel,
    );
  }

  ConditionalPendingOrderState resetValueAfterChangeMethod() {
    return copyWith(
      effectiveTime:
          EffectiveTimeModel(start: DateTime.now(), end: DateTime.now()),
      dilutionType: ConditionalDilutionTypeEnum.cancel,
      activationPriceType: ConditionalOrderPriceTypeEnum.greaterThan,
      conditionalFocusKeyboard: ConditionalFocusKeyBoard.none,
      conditionalPriceInputState:
          conditionalPriceInputState?.copyWith(clearPrice: true),
      conditionalVolumeInputState:
          conditionalVolumeInputState?.copyWith(clearVolume: true),
      activationPriceInputState:
          activationPriceInputState?.copyWith(clearPrice: true),
    );
  }

  ConditionalPendingOrderState changePriceNotFormat(
          {required String priceText}) =>
      copyWith(
        price: priceText.price,
        formatPrice: false,
        priceNotFormat: priceText,
      );

  ConditionalPendingOrderState changePriceWithFormat({required double price}) =>
      copyWith(
        price: price,
        formatPrice: true,
      );

  bool isVolumeHighlight() {
    return false;
  }

  bool get visibleKeyboard =>
      conditionalFocusKeyboard != ConditionalFocusKeyBoard.none;

  bool get isAllInputDone =>
      (activationPriceInputState?.isDone ?? false) &&
      (conditionalPriceInputState?.isDone ?? false) &&
      (conditionalVolumeInputState?.isDone ?? false);

  OrderNavigateStatus getOrderNavigateStatus() {
    if (isAllInputDone) {
      return OrderNavigateStatus.enable;
    } else if (visibleKeyboard) {
      return OrderNavigateStatus.next;
    }
    return OrderNavigateStatus.disable;
  }

  @override
  ConditionalPriceInputState getPriceInputState() {
    return ConditionalPriceInputState(
      formatPrice: true,
      priceNotFormat: '',
      orderType: OrderType.waiting,
      exchange: exchange,
      focusKeyboard: ConditionalFocusKeyBoard.price,
      highlight: false,
    );
  }

  @override
  ConditionalFocusKeyBoard getFocusKeyboard() {
    return conditionalFocusKeyboard ?? ConditionalFocusKeyBoard.none;
  }

  num _getTotalOrderPrice() {
    final volume = conditionalVolumeInputState?.volume ?? 0;
    final price = conditionalPriceInputState?.price ?? 0;
    return volume * price;
  }

  num get totalOrderPrice => _getTotalOrderPrice();

  bool get isBuy => order == Order.buy;

  bool get isVolumeValidToShowMarketCommandSuggest =>
      (conditionalVolumeInputState?.isDone ?? false) &&
      !(conditionalVolumeInputState?.isOddLot ?? false);

  List<String> _getPriceSuggestion() {
    if (isVolumeValidToShowMarketCommandSuggest) {
      if (exchange.value == StockAppConstants.hsx) {
        return [MarketCommandEnum.mp.paramsRequest()];
      } else if (exchange.value == StockAppConstants.hnx) {
        return [
          MarketCommandEnum.mak.paramsRequest(),
          MarketCommandEnum.mok.paramsRequest(),
          MarketCommandEnum.mtl.paramsRequest(),
        ];
      } else {
        return [];
      }
    } else {
      return [];
    }
  }

  List<String> _getVolumeSuggestion() {
    if ((activationPriceInputState?.isDone ?? false) &&
        (conditionalPriceInputState?.isDone ?? false)) {
      if (isBuy) {
        final volumeSuggest = _roundVolume((maxVolumeToBuy ?? 0).toDouble())
            .toMoney(showSymbol: false);
        if (volumeSuggest != '0') {
          return [volumeSuggest];
        } else {
          return [];
        }
      } else {
        final volumeSuggest = _roundVolume((maxVolumeToSell ?? 0).toDouble())
            .toMoney(showSymbol: false);
        if (volumeSuggest != '0') {
          return [volumeSuggest];
        } else {
          return [];
        }
      }
    } else {
      return [];
    }
  }

  List<String> _getSuggestion() {
    if (conditionalFocusKeyboard == ConditionalFocusKeyBoard.price) {
      return _getPriceSuggestion();
    }
    if (conditionalFocusKeyboard == ConditionalFocusKeyBoard.volume) {
      return _getVolumeSuggestion();
    }
    return [];
  }

  int _roundVolume(double volume) {
    if (volume < 100.0) {
      return 0;
    }
    final evenDownRound = (volume / 100.0).floor() * 100.0;
    return evenDownRound.toInt();
  }

  List<String> get suggestion => _getSuggestion();

  @override
  String getVolumeChange() {
    return conditionalVolumeInputState?.text ?? '';
  }

  @override
  String valueChangeFromSuggestion() {
    return (volumeFromSuggestion ?? 0).toString();
  }

  bool get isNormalPrice =>
      (marketCommand ?? MarketCommandEnum.none) == MarketCommandEnum.none;

  String _getCostPriceDisplay() {
    final costPrice = securitiesPortfolioResponsesModel?.costPrice ?? 0;
    if (!isHoldSymbol) {
      return '--';
    } else {
      return costPrice.toDouble().getPriceFormatted(convertToThousand: true);
    }
  }

  String get costPriceDisplay => _getCostPriceDisplay();

  String get tradeDisplay => _getTradeDisplay();

  bool get isHoldSymbol => (securitiesPortfolioResponsesModel?.trade != null &&
      !((securitiesPortfolioResponsesModel?.trade ?? -1) < 0));

  String _getTradeDisplay() {
    if (!isHoldSymbol) {
      return '0';
    } else {
      return (securitiesPortfolioResponsesModel?.trade ?? 0)
          .toMoney(showSymbol: false);
    }
  }

  @override
  num? getTradeOfSecurities() {
    return securitiesPortfolioResponsesModel?.trade;
  }

  @override
  bool isMarketCommand() {
    return marketCommand != null && marketCommand != MarketCommandEnum.none;
  }
}
