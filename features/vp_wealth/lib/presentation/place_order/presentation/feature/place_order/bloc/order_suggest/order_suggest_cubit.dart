import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:tuple/tuple.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/data/model/assets/item_assets_model.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/exchange.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/order_type.dart';
import 'package:vp_wealth/presentation/place_order/domain/place_order_repository.dart';
import 'package:vp_wealth/presentation/wealths/socket/investment_tool_socket_connect.dart';
import 'package:vp_wealth/presentation/wealths/socket/model/investment_tool_oddlot_stock_data.dart';

class OrderSuggestCubit extends Cubit<List<String>> {
  OrderSuggestCubit({ItemAssetsModel? wealthModel})
    : _wealthModel = wealthModel ?? ItemAssetsModel(),
      super([]);

  final ItemAssetsModel _wealthModel;
  StreamSubscription? _socketSubscription;
  late Exchange _exchange;
  late OrderType _orderType;
  String _marketStatus = '';

  final dataForWealth = {
    SessionType.atc.name.toUpperCase(): [
      'O',
      'E',
      'I',
      'L',
      'F',
      'M',
      'A',
      'S',
      'K',
      'G',
      'J',
      'H',
      '2',
      '5',
      '10',
      '30',
      '13',
    ],
    SessionType.plo.name.toUpperCase(): ['35', '6'],
  };

  final data = {
    SessionType.ato.name.toUpperCase(): ['P', 'K', 'G', 'J', 'H'],
    SessionType.mp.name.toUpperCase(): [
      'O',
      'E',
      'I',
      'L',
      'F',
      'M',
      'K',
      'G',
      'J',
      'H',
    ],
    SessionType.mak.name.toUpperCase(): ['2', '5', '10', '13'],
    SessionType.mok.name.toUpperCase(): ['2', '5', '10', '13'],
    SessionType.mtl.name.toUpperCase(): ['2', '5', '10', '13'],
    SessionType.atc.name.toUpperCase(): [
      'O',
      'E',
      'I',
      'L',
      'F',
      'M',
      'A',
      'S',
      'K',
      'G',
      'J',
      'H',
      '2',
      '5',
      '10',
      '30',
      '13',
    ],
    SessionType.plo.name.toUpperCase(): ['35', '6'],
  };

  void _getMarketStatus() async {
    try {
      if (_orderType.isGtc || _exchange.isUpcom) {
        return emit([]);
      }
      final response = await GetIt.instance
          .get<PlaceOrderRepository>()
          .getMarketPriceChart(_exchange.value);
      if (response.marketCode == _exchange.value) {
        _marketStatus = response.marketStatus ?? '';
        _updateSuggest();
      }
    } catch (_) {
      final listOrder =
          _exchange.isHose
              ? [
                SessionType.ato.name.toUpperCase(),
                SessionType.mtl.name.toUpperCase(),
                SessionType.atc.name.toUpperCase(),
              ]
              : _exchange.isHnx
              ? [
                SessionType.atc.name.toUpperCase(),
                SessionType.mok.name.toUpperCase(),
                SessionType.mak.name.toUpperCase(),
                SessionType.mtl.name.toUpperCase(),
              ]
              : <String>[];
      emit(listOrder);
    }
  }

  void _updateSocket() {
    _socketSubscription?.cancel();
    if (_orderType.isGtc || _exchange.isUpcom) return;
    _socketSubscription = ISocketConnect.instance
        .addListener(_exchange.value, ISocketChannel.marketinfo)
        .streamData
        .listen((event) {
          _marketStatus =
              (event as IMarketInfoData).marketStatus?.toString() ?? '';
          _updateSuggest();
        });
  }

  void update(Tuple2 data) async {
    _exchange = data.item1;
    _orderType = data.item2;
    _updateSocket();
    _getMarketStatus();
  }

  void _updateSuggest() {
    final listOrder = <String>[];

    if ((_wealthModel.accountNo ?? '').isNotEmpty) {
      dataForWealth.forEach((key, value) {
        if (dataForWealth[key]!.contains(_marketStatus)) {
          if (_exchange == Exchange.hsx &&
              key == SessionType.mp.name.toUpperCase()) {
            listOrder.add(SessionType.mtl.name.toUpperCase());
          } else {
            listOrder.add(key);
          }
        }
      });
    } else {
      data.forEach((key, value) {
        if (data[key]!.contains(_marketStatus)) {
          if (_exchange == Exchange.hsx &&
              key == SessionType.mp.name.toUpperCase()) {
            listOrder.add(SessionType.mtl.name.toUpperCase());
          } else {
            listOrder.add(key);
          }
        }
      });
    }
    emit(listOrder);
  }

  @override
  Future<void> close() {
    _socketSubscription?.cancel();
    return super.close();
  }
}
