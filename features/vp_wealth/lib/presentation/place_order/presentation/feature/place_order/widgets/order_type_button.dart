import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/exchange.dart';
import 'package:vp_wealth/presentation/place_order/utils/widgets/dialog_select_order_type.dart';
import 'package:vp_wealth/presentation/place_order/utils/widgets/type_item_button.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/data/enum/order_type.dart';

import '../bloc/bloc.dart';

class OrderTypeButton extends StatelessWidget {
  const OrderTypeButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<PlaceOrderBloc>();
    return SymbolSelector(
      (symbol) => TypeItemButton(
        title: OrderTypeSelector(
          (orderTypeText) => Text(
            orderTypeText,
            style: vpTextStyle.body14?.copyWith(
              color: symbol == null ? themeData.gray500 : vpColor.textPrimary,
              height: 1.1,
            ),
          ),
        ),
        bottomSheetBuilder:
            (_) => DialogSelectOrderType(
              exchangeType: bloc.state.exchange ?? Exchange.hsx,
              recommendDetailsOrder: bloc.recommendDetailsOrder,
              order: bloc.state.order,
            ),
        onDataReturn: (type) => bloc.add(ChangeTypeEvent<OrderType>(type)),
        canChange: bloc.canChangeOrderType,
        enable: symbol != null,
      ),
    );
  }
}
