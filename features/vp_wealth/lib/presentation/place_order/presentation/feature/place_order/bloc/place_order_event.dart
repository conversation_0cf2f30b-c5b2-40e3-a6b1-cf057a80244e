part of 'place_order_bloc.dart';

abstract class PlaceOrderEvent extends Equatable {
  const PlaceOrderEvent();

  @override
  List<Object?> get props => [];
}

class InitEvent extends PlaceOrderEvent {}

class ChangeTypeEvent<T> extends Equatable implements PlaceOrderEvent {
  final T value;

  const ChangeTypeEvent(this.value);

  @override
  List<T> get props => [value];
}

class ChangeInputPriceEvent extends ChangeTypeEvent<String> {
  const ChangeInputPriceEvent(price) : super(price);
}

class ChangeVolumeEvent extends ChangeTypeEvent<String> {
  const ChangeVolumeEvent(volume) : super(volume);
}

class TapPriceButtonEvent extends PlaceOrderEvent {
  final bool increase;

  const TapPriceButtonEvent({required this.increase});
}

class TapVolumeButtonEvent extends PlaceOrderEvent {
  final bool increase;

  const TapVolumeButtonEvent({required this.increase});
}

class OrderSubmitEvent extends ChangeTypeEvent<bool?> {
  const OrderSubmitEvent(showConfirmOrder) : super(showConfirmOrder);
}

class FocusKeyboardEvent extends ChangeTypeEvent<FocusKeyboard> {
  const FocusKeyboardEvent(focusKeyboard) : super(focusKeyboard);
}

class TapNextEvent extends PlaceOrderEvent {}

class TapSuggestEvent extends ChangeTypeEvent<String> {
  const TapSuggestEvent(suggest) : super(suggest);
}

class ChangeGtcEffectiveTimeEvent extends ChangeTypeEvent<dynamic> {
  const ChangeGtcEffectiveTimeEvent(time) : super(time);
}

class ChangeSymbolEvent extends ChangeTypeEvent<String> {
  const ChangeSymbolEvent(symbol) : super(symbol);
}

class ChangeOrderEvent extends ChangeTypeEvent<Order> {
  const ChangeOrderEvent(order) : super(order);
}

class ChangeSubAccountTypeEvent extends ChangeTypeEvent<core.SubAccountType> {
  const ChangeSubAccountTypeEvent({required core.SubAccountType subAccountType})
      : super(subAccountType);
}

class CallApiEvent extends PlaceOrderEvent {
  final bool isLoading;

  CallApiEvent({this.isLoading = false});
}

class CallQuotes extends CallApiEvent {
  CallQuotes({super.isLoading});
}

class CallAllSymbol extends CallApiEvent {
  CallAllSymbol({super.isLoading});
}

class CallAvailableTrade extends CallApiEvent {
  CallAvailableTrade({super.isLoading});
}

class CallStockInfo extends CallApiEvent {
  CallStockInfo({super.isLoading});
}

class CallKrx extends CallApiEvent {
  CallKrx({super.isLoading});
}

class RefreshEvent extends PlaceOrderEvent {}

class FillInitRecommendDetailsOrder extends PlaceOrderEvent {}

class Top3ChangeEvent extends Equatable implements PlaceOrderEvent {
  final List<String> bidPriceList;
  final List<String> offerPriceList;

  const Top3ChangeEvent(
      {required this.bidPriceList, required this.offerPriceList});

  @override
  List<Object> get props => [bidPriceList, offerPriceList];
}

class TapPriceEvent extends ChangeTypeEvent<String> {
  const TapPriceEvent(price) : super(price);
}

class EndHighlightErrorAnimationEvent extends PlaceOrderEvent {}

class ClearPriceEvent extends PlaceOrderEvent {}

class ClearVolumeEvent extends PlaceOrderEvent {}

class ScrollPaddingChangeEvent extends PlaceOrderEvent {}

class TapOrderLabelEvent extends PlaceOrderEvent {}

class PushMessageEvent extends PlaceOrderEvent {
  final PlaceOrderMessage message;

  const PushMessageEvent(this.message);
}

class ChangeLotOfChartEvent extends ChangeTypeEvent<Lot> {
  const ChangeLotOfChartEvent(lot) : super(lot);
}

class OnSuccessOrderEvent extends PlaceOrderEvent {}

class ChangeNewOrderEvent extends ChangeTypeEvent<bool> {
  const ChangeNewOrderEvent(newOrder) : super(newOrder);
}

class OnGetPriceBoardBuyInStatus extends PlaceOrderEvent {
  final Function(bool priceBoardBuyInStatus) onDone;

  const OnGetPriceBoardBuyInStatus({required this.onDone});
}
