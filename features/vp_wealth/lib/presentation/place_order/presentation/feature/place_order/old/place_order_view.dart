import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_design_system/custom_widget/app_refresh_view.dart';
import 'package:vp_design_system/custom_widget/app_snackbar_utils.dart';
import 'package:vp_design_system/custom_widget/divider_widget.dart';
import 'package:vp_wealth/app_lifecycle/app_lifecycle_listener.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/state/place_order_message.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/condition_order_factories/conditional_bottom_layout_factory.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/condition_order_factories/conditional_order_component_factory.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_pending_buy_order/bloc/conditional_pending_order_bloc.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_pending_buy_order/conditional_order_selectors/place_order_conditional_order_selectors.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_take_profit_order/bloc/conditional_take_profit_order_bloc.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/utils/message_listener.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/widgets/confirm_notify_dialog.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/widgets/input_field/validator_field.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/stock_right/widget/error_widget.dart';

import '../bloc/bloc.dart';
import '../bloc/order_suggest/order_suggest_cubit.dart';
import '../conditional_order_components/loading/loading_builder.dart';
import '../widgets/input_field/order_suggest.dart';
import '../widgets/widgets.dart';
import 'widgets/header_label.dart';
import 'widgets/stock_info_view.dart';
import 'widgets/sub_account_button.dart';
import 'widgets/symbol_button.dart';

class PlaceOrderView extends StatefulWidget {
  const PlaceOrderView({Key? key}) : super(key: key);

  @override
  State<PlaceOrderView> createState() => _PlaceOrderViewState();
}

class _PlaceOrderViewState extends AppLifeCycleListener<PlaceOrderView> {
  late final PlaceOrderBloc _bloc;
  // late final SetupConfigurationAccountBloc _setupAccountBloc;
  // late final SetupConfigurationTransactionBloc _setupTransactionBloc;
  late final ConditionalTakeProfitOrderBloc _conditionalTakeProfitOrderBloc;
  late final ConditionalPendingOrderBloc _conditionalPendingOrderBloc;

  @override
  void initState() {
    super.initState();
    _bloc = context.read<PlaceOrderBloc>();
    // _setupAccountBloc = context.read<SetupConfigurationAccountBloc>()
    // ..apiConfigDefaultAccount();
    // _setupTransactionBloc = context.read<SetupConfigurationTransactionBloc>();
    _conditionalTakeProfitOrderBloc =
        context.read<ConditionalTakeProfitOrderBloc>();
    _conditionalPendingOrderBloc = context.read<ConditionalPendingOrderBloc>();
  }

  @override
  void onResumeApp() {
    if (mounted) {
      _refresh();
    }
  }

  Future _refresh() async {
    _bloc.add(RefreshEvent());
  }

  @override
  Widget build(BuildContext context) =>
      MessageListener<PlaceOrderBloc, PlaceOrderState>(
        message: {
          PlaceOrderMessageType.successOrder: (content) {
            if (_bloc.state.wealthModel != null) {
              Navigator.of(context)
                ..pop('refresh')
                ..pop('refresh');
              showSnackBar(context, content);
              return;
            }
            // Navigator.of(context).popUntilOrPushIfNotExist(
            //     MainRouter.stockManager,
            //     routeRoot: HomeRouter.home);
            showSnackBar(context, content);
          },
          PlaceOrderMessageType.errorCallApi: (content) => showError(content),
          PlaceOrderMessageType.showCBTTConfirm:
              (content) => showCBTTNotifyDialog(content: content),
          PlaceOrderMessageType.changeConfirmOrder: (content) => {},
          PlaceOrderMessageType.changeStockType: (content) {
            _conditionalTakeProfitOrderBloc.add(
              TakeProfitStockTypeChangeEvent(content),
            );
            _conditionalPendingOrderBloc.add(
              ConditionalOrderStockTypeChangeEvent(content),
            );
          },
          PlaceOrderMessageType.updateOrderSuggest: (content) {
            context.read<OrderSuggestCubit>().update(content);
          },
        },
        child: Column(
          children: [
            const HeaderLabel(),
            Expanded(
              child: LoadingBuilder<PlaceOrderBloc>(
                retry: ErrorNetworkWidget(onPressed: _refresh, tryAgain: true),
                child: Column(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(
                          left: SizeUtils.kSize16,
                          right: SizeUtils.kSize16,
                          top: SizeUtils.kSize16,
                        ),
                        child: PullToRefreshView(
                          onRefresh: _refresh,
                          child: ListView(
                            physics: const AlwaysScrollableScrollPhysics(),
                            controller: _bloc.scrollController,
                            children: [
                              const Row(
                                children: [
                                  Expanded(
                                    child: SizedBox(
                                      height: SizeUtils.kHeight48,
                                      child: BuySellSwitch(),
                                    ),
                                  ),
                                  kSpacingWidth8,
                                  Expanded(child: SubAccountButton()),
                                ],
                              ),
                              kSpacingHeight8,
                              const Row(
                                children: [
                                  Expanded(child: SymbolButton()),
                                  kSpacingWidth8,
                                  Expanded(child: OrderTypeButton()),
                                ],
                              ),
                              const StockInfoView(),
                              ChoiceConditionalOrderSelector((
                                isChoiceConditionalOrder,
                              ) {
                                if (!isChoiceConditionalOrder) {
                                  return const Column(
                                    children: [
                                      kSpacingHeight16,
                                      TextInputField(),
                                      OrderSuggest(),
                                      ValidatorField(),
                                      kSpacingHeight8,
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Expanded(
                                            child: AvailableTradeLabel(),
                                          ),
                                          kSpacingWidth8,
                                          MarginRateLabel(),
                                        ],
                                      ),
                                      GtcEffectiveTimeButton(),
                                    ],
                                  );
                                } else {
                                  return const ConditionalComponentFactory();
                                }
                              }),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const DividerWidget(),
                    const ConditionalBottomLayoutFactory(
                      orderBottomLayout: OrderBottomLayout(),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
}
