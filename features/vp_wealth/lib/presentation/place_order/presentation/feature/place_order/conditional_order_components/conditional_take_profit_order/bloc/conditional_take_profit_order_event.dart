part of 'conditional_take_profit_order_bloc.dart';

@immutable
abstract class ConditionalTakeProfitOrderEvent {}

class ConditionalTakeProfitInitData extends ConditionalTakeProfitOrderEvent {}

class TakeProfitFocusKeyboardEvent
    extends ChangeTypeEvent<ConditionalTakeProfitFocusKeyboard>
    implements ConditionalTakeProfitInitData {
  const TakeProfitFocusKeyboardEvent(focusKeyboard) : super(focusKeyboard);
}

class ConditionalTakeProfitChangeDilutionTypeEvent
    extends ConditionalTakeProfitOrderEvent {
  final ConditionalDilutionTypeEnum dilutionType;

  ConditionalTakeProfitChangeDilutionTypeEvent({required this.dilutionType});
}

class ConditionalTakeProfitChangeEffectiveTimeEvent
    extends ChangeTypeEvent<dynamic>
    implements ConditionalTakeProfitOrderEvent {
  const ConditionalTakeProfitChangeEffectiveTimeEvent(time) : super(time);
}

class TapDecreaseRateProfitEvent extends ConditionalTakeProfitOrderEvent {}

class TapIncreaseRateProfitEvent extends ConditionalTakeProfitOrderEvent {}

class ChangeRateProfitMarginEvent extends ConditionalTakeProfitOrderEvent {
  final String value;

  ChangeRateProfitMarginEvent(this.value);
}

class RateProfitFocusKeyboardEvent extends ConditionalTakeProfitOrderEvent {
  final ConditionalFocusKeyBoard conditionalFocusKeyBoard;

  RateProfitFocusKeyboardEvent(this.conditionalFocusKeyBoard);
}

class TapDecreaseSlippageProfitMarginEvent
    extends ConditionalTakeProfitOrderEvent {}

class TapIncreaseSlippageProfitMarginEvent
    extends ConditionalTakeProfitOrderEvent {}

class ChangeSlippageProfitMarginEvent extends ConditionalTakeProfitOrderEvent {
  final String value;

  ChangeSlippageProfitMarginEvent(this.value);
}

class SlippageProfitMarginFocusKeyboardEvent
    extends ConditionalTakeProfitOrderEvent {
  final ConditionalFocusKeyBoard conditionalFocusKeyBoard;

  SlippageProfitMarginFocusKeyboardEvent(this.conditionalFocusKeyBoard);
}

class UpdateOrderNavigateStatusEvent extends ConditionalTakeProfitOrderEvent {
  final OrderNavigateStatus orderNavigateStatus;

  UpdateOrderNavigateStatusEvent(this.orderNavigateStatus);
}

class TakeProfitUpdateOrderNavigateStatusOtherInputEvent
    extends ConditionalTakeProfitOrderEvent {
  final OrderNavigateStatus orderNavigateStatus;

  TakeProfitUpdateOrderNavigateStatusOtherInputEvent(
      {required this.orderNavigateStatus});
}

class TakeProfitUnFocusAllInput extends ConditionalTakeProfitOrderEvent {}

class TakeProfitUpdateOrderTypeEvent extends ConditionalTakeProfitOrderEvent {
  final OrderType orderType;

  TakeProfitUpdateOrderTypeEvent(this.orderType);
}

class TakeProfitUpdateSubAccountEvent extends ConditionalTakeProfitOrderEvent {
  final SubAccountType subAccountType;

  TakeProfitUpdateSubAccountEvent(this.subAccountType);
}

class TakeProfitUpdateExchangeEvent extends ConditionalTakeProfitOrderEvent {
  final Exchange exchange;

  TakeProfitUpdateExchangeEvent(this.exchange);
}

class TakeProfitStockTypeChangeEvent extends ConditionalTakeProfitOrderEvent {
  final String? stockType;

  TakeProfitStockTypeChangeEvent(this.stockType);
}

class UpdateTriggerConditionEvent extends ConditionalTakeProfitOrderEvent {
  final ConditionalTakeProfitTriggerConditionEnum triggerCondition;

  UpdateTriggerConditionEvent(this.triggerCondition);
}

class TakeProfitSubmitEvent extends ConditionalTakeProfitOrderEvent {
  final PlaceOrderState placeOrderState;
  final String userId;
  final VoidCallback onSuccess;

  TakeProfitSubmitEvent(this.placeOrderState,
      {required this.userId, required this.onSuccess});
}

class TakeProfitClearStateEvent extends ConditionalTakeProfitOrderEvent {}

class TakeProfitInitialDataEvent extends ConditionalTakeProfitOrderEvent {}

class TakeProfitGetPortfolioResponseModel
    extends ConditionalTakeProfitOrderEvent {}

class OnTapFocusVolumeInput extends ConditionalTakeProfitOrderEvent {
  OnTapFocusVolumeInput();
}

class UpdateTakeProfitVolumeEvent extends ConditionalTakeProfitOrderEvent {
  final ConditionalVolumeInputState volumeInputState;

  UpdateTakeProfitVolumeEvent(this.volumeInputState);
}

// ==========NEW ===========
class UpdateRateProfitInputStateEvent extends ConditionalTakeProfitOrderEvent {
  final TakeProfitRateProfitInputState rateProfitInput;

  UpdateRateProfitInputStateEvent(this.rateProfitInput);
}

class UpdateSlippageInputStateEvent extends ConditionalTakeProfitOrderEvent {
  final TakeProfitSlippageInputState slippageInputState;

  UpdateSlippageInputStateEvent(this.slippageInputState);
}

class TakeProfitTapNextButtonEvent extends ConditionalTakeProfitOrderEvent {
  TakeProfitTapNextButtonEvent();
}

class OnTapFocusSlippageInput extends ConditionalTakeProfitOrderEvent {
  OnTapFocusSlippageInput();
}

class OnTapFocusRateProfitInput extends ConditionalTakeProfitOrderEvent {
  OnTapFocusRateProfitInput();
}

class OnTapCloseKeyboardEvent extends ConditionalTakeProfitOrderEvent {
  OnTapCloseKeyboardEvent();
}

class TakeProfitOnUpdateSymbol extends ConditionalTakeProfitOrderEvent {
  final String symbol;

  TakeProfitOnUpdateSymbol(this.symbol);
}

class TakeProfitOnTapVolumeSuggest extends ConditionalTakeProfitOrderEvent {
  final String value;

  TakeProfitOnTapVolumeSuggest(this.value);
}

class TakeProfitOnUpdateMaxVolume extends ConditionalTakeProfitOrderEvent {
  final num value;

  TakeProfitOnUpdateMaxVolume(this.value);
}

class TakeProfitUpdateOrderEvent extends ConditionalTakeProfitOrderEvent {
  final Order order;

  TakeProfitUpdateOrderEvent({required this.order});
}
