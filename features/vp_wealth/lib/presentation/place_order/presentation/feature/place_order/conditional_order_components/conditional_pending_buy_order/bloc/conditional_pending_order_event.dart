part of 'conditional_pending_order_bloc.dart';

abstract class ConditionalPendingOrderEvent {}

class IInitialInputValue extends ConditionalPendingOrderEvent {
  PlaceOrderState placeOrderState;

  IInitialInputValue(this.placeOrderState);
}

class ActivationPriceFocusKeyboardEvent extends ConditionalPendingOrderEvent {
  ConditionalFocusKeyBoard conditionalFocusKeyBoard;

  ActivationPriceFocusKeyboardEvent(this.conditionalFocusKeyBoard);
}

class OnChangePriceType extends ConditionalPendingOrderEvent {
  ConditionalOrderPriceTypeEnum timeType;

  OnChangePriceType(this.timeType);
}

class IChangeVolumeEvent extends ConditionalPendingOrderEvent {
  final String value;

  IChangeVolumeEvent(this.value);
}

class IChangePriceEvent extends ConditionalPendingOrderEvent {
  final String value;

  IChangePriceEvent(this.value);
}

class ChangeEffectiveTimeEvent extends ChangeTypeEvent<dynamic>
    implements ConditionalPendingOrderEvent {
  const ChangeEffectiveTimeEvent(time) : super(time);
}

class IOrderSubmitEvent extends ChangeTypeEvent<bool?>
    implements ConditionalPendingOrderEvent {
  const IOrderSubmitEvent(showConfirmOrder) : super(showConfirmOrder);
}

class UpdateOtherInputEvent extends ConditionalPendingOrderEvent {
  final OrderNavigateStatus status;

  UpdateOtherInputEvent(this.status);
}

class ConditionalOrderSubmitOrderEvent extends ConditionalPendingOrderEvent {
  final PlaceOrderState placeOrderState;
  final String userId;
  final VoidCallback onSuccess;

  ConditionalOrderSubmitOrderEvent(
    this.placeOrderState, {
    required this.userId,
    required this.onSuccess,
  });
}

class ConditionalOrderChangeDilutionTypeEvent
    extends ConditionalPendingOrderEvent {
  final ConditionalDilutionTypeEnum dilutionType;

  ConditionalOrderChangeDilutionTypeEvent({required this.dilutionType});
}

class ConditionalOrderChangeExchangeEvent extends ConditionalPendingOrderEvent {
  final Exchange exchange;

  ConditionalOrderChangeExchangeEvent({required this.exchange});
}

class ConditionalOrderStockTypeChangeEvent
    extends ConditionalPendingOrderEvent {
  final String? stockType;

  ConditionalOrderStockTypeChangeEvent(this.stockType);
}

class UpdateOrderNavigateStatusOtherInputEvent
    extends ConditionalPendingOrderEvent {
  final OrderNavigateStatus orderNavigateStatus;

  UpdateOrderNavigateStatusOtherInputEvent({required this.orderNavigateStatus});
}

class ConditionalUpdateOrderEvent extends ConditionalPendingOrderEvent {
  final Order order;

  ConditionalUpdateOrderEvent({required this.order});
}

class UpdateConditionalPriceEvent extends ConditionalPendingOrderEvent {
  final ConditionalPriceInputState priceInputState;

  UpdateConditionalPriceEvent(this.priceInputState);
}

class UpdateConditionalActivationPriceEvent
    extends ConditionalPendingOrderEvent {
  final ConditionalPriceInputState activationPrice;

  UpdateConditionalActivationPriceEvent(this.activationPrice);
}

class UpdateConditionalVolumeEvent extends ConditionalPendingOrderEvent {
  final ConditionalVolumeInputState volumeInputState;

  UpdateConditionalVolumeEvent(this.volumeInputState);
}

class TapNextButtonEvent extends ConditionalPendingOrderEvent {
  TapNextButtonEvent();
}

class OnTapFocusPriceInput extends ConditionalPendingOrderEvent {
  OnTapFocusPriceInput();
}

class OnTapFocusActivationPriceInput extends ConditionalPendingOrderEvent {
  OnTapFocusActivationPriceInput();
}

class OnTapFocusVolumeInput extends ConditionalPendingOrderEvent {
  OnTapFocusVolumeInput();
}

class OnTapCloseKeyboardEvent extends ConditionalPendingOrderEvent {
  OnTapCloseKeyboardEvent();
}

class OnTapVolumeSuggest extends ConditionalPendingOrderEvent {
  final String value;

  OnTapVolumeSuggest(this.value);
}

class UpdateMaxVolumeToSell extends ConditionalPendingOrderEvent {
  final num value;

  UpdateMaxVolumeToSell(this.value);
}

class PendingOrderUpdateSubAccountEvent extends ConditionalPendingOrderEvent {
  final SubAccountType subAccountType;

  PendingOrderUpdateSubAccountEvent(this.subAccountType);
}

class UpdateMaxVolumeToBuy extends ConditionalPendingOrderEvent {
  final num value;

  UpdateMaxVolumeToBuy(this.value);
}

class PendingOrderUpdateSymbolEvent extends ConditionalPendingOrderEvent {
  final String symbol;

  PendingOrderUpdateSymbolEvent(this.symbol);
}

class PendingOrderResetState extends ConditionalPendingOrderEvent {
  PendingOrderResetState();
}

class CallAvailableTradeWithErrorCatch extends ConditionalPendingOrderEvent {}
