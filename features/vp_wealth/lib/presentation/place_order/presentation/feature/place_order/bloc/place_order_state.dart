import 'package:easy_localization/easy_localization.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:tuple/tuple.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_wealth/data/model/assets/item_assets_model.dart';
import 'package:vp_wealth/data/model/stock_detail_entity.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/lang/stock_localized_values.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/exchange.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/place_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/domain/model/gtc_effective_time.dart';
import 'package:vp_wealth/presentation/place_order/gen/locale_keys.g.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/state/input_state/input_price_state.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/state/input_state/input_volume_state.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/state/place_order_message.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/state/state.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/loading/loading_state.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/utils/listener/listener.dart';
import 'package:vp_wealth/presentation/place_order/utils/ext.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/stock_order_entity.dart';
import 'package:vp_wealth/presentation/wealths/socket/investment_tool_socket_connect.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/data/stock_helper.dart';

part 'place_order_state.freezed.dart';

List<OrderType> conditionalOrderTypes = [
  OrderType.stopLoss,
  OrderType.takeProfit,
  OrderType.waiting,
];

@freezed
abstract class PlaceOrderState
    with _$PlaceOrderState, MessageState<PlaceOrderMessage?>, LoadingState {
  PlaceOrderState._();

  factory PlaceOrderState({
    @Default(false) bool newOrder,
    required SubAccountType subAccount,
    required String? symbol,
    String? fullName,
    ItemAssetsModel? wealthModel,
    @Default(0) num availableTrade,
    double? referencePrice,
    @override PlaceOrderMessage? message,
    @override required bool isTest,
    required InputPriceState inputPriceState,
    required InputVolumeState inputVolumeState,
    required int marginRate,
    required GtcEffectiveTime gtcEffectiveTime,
    @Default(false) bool showConfirmOrder,
    @Default(false) bool endHighlightErrorAnimation,
    @Default(false) bool isPriceTapping,
    StockDetailEntity? stockEntity,
    required bool fillInitRecommendDetailsOrder,
    @override Set? loadingStack,
    @override bool? isError,
    @Default(Lot.even) Lot lotOfChart,
    @Default([]) List<StockOrderEntity> allSymbolList,
  }) = _PlaceOrderState;

  OrderType get orderType => inputPriceState.orderType;

  SessionType? get sessionType => inputPriceState.sessionType;

  Exchange? get exchange => inputPriceState.exchange;

  Order get order => inputPriceState.order;

  bool get isLoOrGtc => orderType.isLoOrGtc;

  bool get isLoOrder => orderType == OrderType.lo;

  bool get isBuyIn => orderType == OrderType.buyIn;

  bool get isLoOrBuyIn => isLoOrder || isBuyIn;

  bool get isLoOrGtcOrBuyIn => isLoOrGtc || isBuyIn;

  String? get referencePriceText =>
      referencePrice?.getPriceFormatted(convertToThousand: true);

  String get symbolText =>
      symbol ?? (newOrder ? '-' : getStockLang(StockKeyLang.sCode));

  String get exchangeText =>
      exchange != null
          ? (newOrder ? '(${exchange!.value})' : exchange!.value)
          : (newOrder ? '-' : '');

  String get fullNameText => fullName ?? '-';

  LabelState get subAccountLabel => LabelState(
    label:
        subAccount == SubAccountType.normal
            ? LocaleKeys.placeOrder_subAccount_ordinary.tr()
            : LocaleKeys.placeOrder_subAccount_deposit.tr(),
  );

  bool get isNormalSubAccount => subAccount == SubAccountType.normal;

  bool get _isMarginSubAccount => subAccount == SubAccountType.margin;

  double get _quotePrice =>
      isLoOrGtcOrBuyIn
          ? inputPriceState.value
          : order.isBuy
          ? inputPriceState.ceilingPrice
          : inputPriceState.floorPrice;

  double? get quotePriceAvailableTradeParams =>
      inputPriceState.isDone ? _quotePrice : null;

  String get orderTypeText {
    switch (orderType) {
      case OrderType.gtc:
        return 'Lệnh GTC';
      // case OrderType.waiting:
      //   return LocaleKeys.placeOrder_condition_waitingCommand.tr();
      // case OrderType.takeProfit:
      //   return LocaleKeys.placeOrder_condition_takeProfitCommand.tr();
      // case OrderType.stopLoss:
      //   return LocaleKeys.placeOrder_condition_stopLossCommand.tr();
      case OrderType.buyIn:
        return 'Lệnh Buy-in';
      default:
        return 'Lệnh thường';
    }
  }

  TitleState get availableTradeState {
    final keyLang =
        order.isBuy
            ? 'Sức mua'
            : 'KL khả dụng';
    final label =
        symbol == null
            ? '-'
            : order.isBuy
            ? availableTrade.toMoney()
            : inputVolumeState.tradeText;
    return TitleState(
      keyLang: keyLang,
      label: label,
      visible: order.isBuy || !orderType.isGtc,
    );
  }

  TitleState get maxBuyVolumeState {
    final label =
        symbol == null
            ? '-'
            : inputVolumeState.maxVolume.toMoney(showSymbol: false);
    return TitleState(
      keyLang: LocaleKeys.placeOrder_maxBuyVolume.tr(),
      label: label,
      visible: order.isBuy && !orderType.isGtc,
    );
  }

  MarginRateState get marginRateState => MarginRateState(
    label: symbol == null ? '-' : '$marginRate%',
    visible: order.isBuy && _isMarginSubAccount,
  );

  ValueLabelState get valueLabel => ValueLabelState(
    label: (_quotePrice * inputVolumeState.value).valueText,
    color: order.isBuy ? themeData.primary : themeData.red,
  );

  String get gtcEffectiveTimeText => gtcEffectiveTime.toString();

  bool get visibleKeyboard => inputPriceState.focus || inputVolumeState.focus;

  OrderNavigateStatus get orderNavigateStatus {
    if (inputPriceState.isDone &&
        inputVolumeState.isDone &&
        (!orderType.isGtc || orderType.isGtc && gtcEffectiveTime.isDone)) {
      return OrderNavigateStatus.enable;
    }
    if (visibleKeyboard && isLoOrGtcOrBuyIn && sessionType == null) {
      return OrderNavigateStatus.next;
    }
    return OrderNavigateStatus.disable;
  }

  GtcEffectiveTimeState get gtcEffectiveTimeState => GtcEffectiveTimeState(
    time: gtcEffectiveTime,
    label: gtcEffectiveTime.toString(),
    labelStyle: vpTextStyle.body14!.copyWith(
      color: gtcEffectiveTime.isDone ? null : themeData.gray500,
    ),
    visible: orderType.isGtc,
  );

  OrderState get orderState => OrderState(
    order: inputPriceState.order,
    switchKeyLang: getStockLang(
      order.isBuy ? StockKeyLang.buy : StockKeyLang.sell,
    ),
    buttonKeyLang:
        order.isBuy
            ?  'Đặt mua'
            : 'Đặt bán',
    color: order.isBuy ? themeData.primary : themeData.red.withRed(205),
  );

  HeaderLabelState get headerLabelState => HeaderLabelState(
    label: order.isBuy ? StockKeyLang.buyOrder : StockKeyLang.sellOrder,
    symbol: visibleKeyboard ? ' $symbol' : '',
    labelColor: order.isBuy ? themeData.primary : themeData.red,
    subLabel: StockKeyLang.placeStockOrder,
    showSubLabel: !visibleKeyboard,
  );

  bool get isChoiceConditionalOrder =>
      conditionalOrderTypes.contains(orderType);

  bool get isOddLot =>
      newOrder ? lotOfChart.isOddLot : NumExt(inputVolumeState.value).isOddLot;

  ISocketChannel get stockInfoSocketChannel =>
      isBuyIn
          ? ISocketChannel.stockinfoMultipleBoard
          : isOddLot
          ? ISocketChannel.oddLotStockInfo
          : ISocketChannel.stockinfo;

  String get totalBidQTTY =>
      exchange.isHose || lotOfChart.isOddLot
          ? getStockLang(StockKeyLang.bidSide)
          : '${LocaleKeys.overbought.tr()}: ${StockHelper.formatVol(stockEntity?.totalBidQTTY)}';

  String get totalOfferQTTY =>
      exchange.isHose || lotOfChart.isOddLot
          ? getStockLang(StockKeyLang.offerSide)
          : '${LocaleKeys.oversold.tr()}: ${StockHelper.formatVol(stockEntity?.totalOfferQTTY)}';

  num get maxVolume => inputVolumeState.maxVolume;

  PlaceOrderState focusKeyboard(FocusKeyboard focus) => copyWith
      .inputPriceState(focusKeyboard: focus)
      .copyWith
      .inputVolumeState(focusKeyboard: focus);

  PlaceOrderState changeOrder(Order order) => copyWith
      .inputPriceState(order: order)
      .copyWith
      .inputVolumeState(order: order);

  PlaceOrderState changeExchange(Exchange? exchange) =>
      copyWith.inputPriceState(exchange: exchange);

  PlaceOrderState changeOrderType(OrderType orderType) => copyWith
      .inputPriceState(orderType: orderType)
      .copyWith
      .inputVolumeState(orderType: orderType);

  PlaceOrderState changeSessionType(SessionType? sessionType) => copyWith
      .inputPriceState(sessionType: sessionType)
      .copyWith
      .inputVolumeState(sessionType: sessionType);

  @override
  copy(data, {Type? type}) {
    if (type == MessageState) return copyWith(message: data);
    if (type == LoadingState) {
      return copyWith(
        loadingStack: (data as Tuple2).item1,
        isError: data.item2,
      );
    }
  }
}
