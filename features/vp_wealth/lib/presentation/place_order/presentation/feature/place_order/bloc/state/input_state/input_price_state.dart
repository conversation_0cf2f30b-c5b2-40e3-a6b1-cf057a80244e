import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/services.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/exchange.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/place_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/gen/locale_keys.g.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/state/input_field/bloc/input_field_cubit.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/condition/condition_command_util.dart';
import 'package:vp_wealth/presentation/place_order/utils/ext.dart';
part 'input_price_state.freezed.dart';

enum Error {
  outOfRange,
  step10Invalid,
  step50Invalid,
  step100Invalid,
}

@freezed
abstract class InputPriceState extends InputFieldState with _$InputPriceState {
  InputPriceState._();

  factory InputPriceState({
    required String text,
    required Order order,
    required OrderType orderType,
    SessionType? sessionType,
    required double ceilingPrice,
    required double floorPrice,
    Exchange? exchange,
    required FocusKeyboard focusKeyboard,
    List<String>? bidPrices,
    List<String>? offerPrices,
    String? stockType,
  }) = _InputPriceState;

  @override
  String get hint => 'Giá';

  double get step => stockType.isCW || (stockType.isETF && !exchange.isUpcom)
      ? 10.0
      : exchange.isHose
          ? value.stepHose
          : 100.0;

  @override
  Error? get error {
    if (text.price == 0.0 ||
        (orderType.isLo && (value > ceilingPrice || value < floorPrice))) {
      return Error.outOfRange;
    }
    if (value % step == 0) return null;
    if (step == 10.0) return Error.step10Invalid;
    if (step == 50.0) return Error.step50Invalid;
    return Error.step100Invalid;
  }

  @override
  Map<dynamic, String> get errorToText => {
        Error.outOfRange: 'Giá nhập phải nằm trong khoảng giá trần và giá sàn',
        Error.step10Invalid: 'Bước giá không hợp lệ phải chia hết cho 10 đ',
        Error.step50Invalid: 'Bước giá không hợp lệ phải chia hết cho 50 đ',
        Error.step100Invalid: 'Bước giá không hợp lệ phải chia hết cho 100 đ',
      };

  @override
  bool get focus => focusKeyboard == FocusKeyboard.price;

  @override
  List<TextInputFormatter> get inputFormatter =>
      [...removeZeroStartInputFormatter, ...priceInputFormatter];

  @override
  String tap({bool increase = true}) {
    if (isEmpty) {
      return (order == Order.buy ? floorPrice : ceilingPrice)
          .getPriceFormatted(convertToThousand: true);
    }
    if (value == 0.0 && !increase) {
      return 0.0.getPriceFormatted(convertToThousand: true);
    }
    final newText = ConditionCommandUtil.updateValue(increase, value, step)
        .toDouble()
        .getPriceFormatted(convertToThousand: true);
    return newText.length > maxLength ? text : newText;
  }

  @override
  double get value => sessionType == null
      ? text.price ?? 0.0
      : order == Order.buy
          ? ceilingPrice
          : floorPrice;

  @override
  List<String> get suggests =>
      (order == Order.buy ? offerPrices : bidPrices)
          ?.where((suggest) => suggest != '-')
          .toList() ??
      [];
}
