import 'package:collection/collection.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/extensions/list_extensions.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/presentation/place_order/model/securities_portfolio_responses_model.dart';

class GetPortFolioResponse {
  final mainStockRepo = GetIt.instance.get<WealthRepository>();

  Future<SecuritiesPortfolioResponsesModel> bySymbolAndSubAccount({
    required String symbol,
    required SubAccountType subAccountType,
  }) async {
    String userId =
        GetIt.instance
            .get<SubAccountCubit>()
            .getSubAccount(subAccountType)
            ?.id ??
        '';
    final data = await getSecuritiesPortfolioSubAccount(userId);
    return getModelFromList(data, symbol);
  }

  SecuritiesPortfolioResponsesModel getModelFromList(
    List<SecuritiesPortfolioResponsesModel> data,
    String symbol,
  ) {
    final securitiesPortfolioResponsesModel = data.firstWhere(
      (element) => element.symbol == symbol,
      orElse: () {
        return SecuritiesPortfolioResponsesModel(trade: -1);
      },
    );

    /// merge costPrice with WFT
    final wftPortfolioResponsesModel = data.firstWhereOrNull(
      (element) => element.symbol == symbol + '_WFT',
    );
    if (wftPortfolioResponsesModel != null) {
      securitiesPortfolioResponsesModel.mixCostPriceWithWFT(
        wftPortfolioResponsesModel,
      );
    }
    return securitiesPortfolioResponsesModel;
  }

  Future<List<SecuritiesPortfolioResponsesModel>>
  getSecuritiesPortfolioSubAccount(String accountId) async {
    List<SecuritiesPortfolioResponsesModel> dataResponse =
        <SecuritiesPortfolioResponsesModel>[];
    try {
      final value = await mainStockRepo.getSecuritiesPortfolio(accountId);

      if (value.isNotEmpty) {
        // value.mixSymbolWFT();

        final listSymbols = value.map((e) => e.symbol ?? '').toSet().toList();

        /// get stock detail because securities portfolio return cached value
        /// (basic price, referencePrice, floorPrice...)
        final details = await mainStockRepo.getStocks(
          listSymbols.symbolsFormat,
        );

        final data =
            value
                .map(
                  (e) => e.copyWith(
                    details.firstWhereOrNull(
                      (stock) => e.symbol == stock.symbol,
                    ),
                  ),
                )
                .toList();
        dataResponse.addAll(data);
      }
    } catch (e, stackTrace) {
      final response = e is ResponseError && e.code == 'IVBERR02' ? null : e;
      showError(response);
    }
    return dataResponse;
  }
}
