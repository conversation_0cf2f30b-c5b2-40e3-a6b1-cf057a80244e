import 'package:collection/collection.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:vp_common/error/handle_error.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/widget/log_api/models/app_base_response.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/common/utils/stock_utils.dart';
import 'package:vp_wealth/data/model/stock_detail_entity.dart' as entity;
import 'package:vp_wealth/data/utils/dio_extention.dart';
import 'package:vp_wealth/data/utils/download_util/base_download_file_manager.dart';
import 'package:vp_wealth/data/utils/download_util/download_file_manager.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/presentation/place_order/assets/stock_key_assets.dart';
import 'package:vp_wealth/presentation/place_order/data/api/place_order_path_api.dart';
import 'package:vp_wealth/presentation/place_order/data/model/params/available_trade_params.dart';
import 'package:vp_wealth/presentation/place_order/data/model/params/get_all_stock_holding_params.dart';
import 'package:vp_wealth/presentation/place_order/data/model/params/quotes_param.dart';
import 'package:vp_wealth/presentation/place_order/data/model/params/request_id_params.dart';
import 'package:vp_wealth/presentation/place_order/data/model/request/gtc_request.dart';
import 'package:vp_wealth/presentation/place_order/data/model/request/order_recommedation_request.dart';
import 'package:vp_wealth/presentation/place_order/data/model/request/order_request.dart';
import 'package:vp_wealth/presentation/place_order/data/model/response/available_trade_response.dart';
import 'package:vp_wealth/presentation/place_order/data/model/response/select_stock_symbol/get_stock_holding_response.dart';
import 'package:vp_wealth/presentation/place_order/data/model/response/stock_detail_model.dart';
import 'package:vp_wealth/presentation/place_order/domain/model/market/market_price_chart_entity.dart';
import 'package:vp_wealth/presentation/place_order/domain/place_order_repository.dart';
import 'package:vp_wealth/presentation/place_order/model/check_model.dart';
import 'package:vp_wealth/presentation/place_order/model/request/pre_check_order_request.dart';
import 'package:vp_wealth/presentation/place_order/model/securities_portfolio_responses_model.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/stock_order_entity.dart';
import 'package:vp_wealth/presentation/wealths/order/stock_order_model.dart';

class PlaceOrderRepositoryImpl implements PlaceOrderRepository {
  final Dio _restDio;
  final Dio _restSaleSupportDio;
  final Dio _baseDio;
  final Dio _noAuthDio;
  final Dio _restKrxDio;

  PlaceOrderRepositoryImpl({
    required Dio baseDio,
    required Dio restDio,
    required Dio restSaleSupportDio,
    required Dio noAuthDio,
    required Dio restKrxDio,
  }) : _baseDio = baseDio,
       _restDio = restDio,
       _restSaleSupportDio = restSaleSupportDio,
       _noAuthDio = noAuthDio,
       _restKrxDio = restKrxDio;

  @override
  Future<List<GetStockHoldingResponse>> getAllStockHolding(
    GetAllStockHoldingPrams params,
  ) async {
    List<GetStockHoldingResponse> list = [];
    try {
      Response response = await _restDio.get(
        PlaceOrderPathApi.securities,
        queryParameters: params.toJson(),
      );
      final result = AppBaseResponse.fromJson(response.data);
      if (result.isSuccess() && result.data is List) {
        for (var jsonObj in result.data) {
          list.add(GetStockHoldingResponse.fromJson(jsonObj));
        }
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (e) {
      throw HandleError.from(e);
    }
    return list;
  }

  @override
  Future<StockOrderEntity> getQuotes(QuotesParam param) async {
    try {
      Response response = await _restDio.get(
        '/flex/${PlaceOrderPathApi.quotes}',
        queryParameters: param.toJson(),
      );
      final result = AppBaseResponse.fromJson(response.data);
      if (result.isSuccess() && result.data != null) {
        return StockOrderModel.fromQuotesJson(result.data[0]).entity;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<List<StockOrderEntity>> getQuotesAll() async {
    List<StockOrderEntity> _transformData(dynamic data) =>
        (data as List)
            .map((e) => StockOrderModel.fromQuotesJson(e).entity)
            .toList()
          ..sort((a, b) => (a.symbol).compareTo(b.symbol));

    try {
      Response response = await _noAuthDio.get(
        '/noauth${PlaceOrderPathApi.quotes}',
        queryParameters: {'symbols': 'ALL'},
      );
      final result = AppBaseResponse.fromJson(response.data);
      if (result.isSuccess() && result.data != null && result.data is List) {
        final listStock = await compute(_transformData, result.data);
        return listStock;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<AvailableTradeResponse> getAvailableTrade(
    String accountId,
    AvailableTradeParams params,
  ) async {
    try {
      Response response = await _restDio.get(
        PlaceOrderPathApi.availableTrade(accountId),
        queryParameters: params.toJson(),
      );
      final result = AppBaseResponse.fromJson(response.data);
      if (result.isSuccess() && result.data != null) {
        return AvailableTradeResponse.fromJson(result.data);
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<AppBaseResponse> preCheckOrder(
    String accountId,
    RequestIdParams params,
    PreCheckOrderRequest request,
  ) async {
    try {
      Response response = await _restDio.post(
        PlaceOrderPathApi.preCheckOrder(accountId),
        queryParameters: params.toJson(),
        data: request.toJson(),
      );
      return AppBaseResponse.fromJson(response.data);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<CheckModel> initVerifyTransaction(String accountId) async {
    try {
      Response response = await _restDio.post(
        PlaceOrderPathApi.initVerifyTransaction(accountId),
      );
      final result = AppBaseResponse.fromJson(response.data);
      if (result.isSuccess() && result.data != null) {
        return CheckModel.fromJson(result.data);
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<AppBaseResponse> orders(
    String accountId,
    RequestIdParams params,
    OrderRequest request,
  ) async {
    try {
      Response response = await _restDio.post(
        PlaceOrderPathApi.orders(accountId),
        queryParameters: params.toJson(),
        data: request.toJson(),
        options: Options(headers: StockUtils.orderHeaders),
      );

      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess()) {
        return result;
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future conditionOrder(
    String accountId,
    RequestIdParams params,
    GtcRequest request,
  ) async {
    try {
      Response response = await _restDio.post(
        PlaceOrderPathApi.conditionOrder(accountId),
        queryParameters: params.toJson(),
        data: request.toJson(),
        options: Options(headers: StockUtils.orderHeaders),
      );

      final beResponse = BEBaseResponse.fromJson(response.data);

      if (beResponse.isSuccess()) {
        return beResponse;
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BEBaseResponse> orderRecommendation(
    OrderRecommendationRequest request,
  ) async {
    try {
      Response response = await _restSaleSupportDio.post(
        PlaceOrderPathApi.orderRecommendation,
        data: request.toJson(),
        options: Options(headers: StockUtils.orderHeaders),
      );

      final beResponse = BEBaseResponse.fromJson(response.data);

      if (beResponse.isSuccess()) {
        return beResponse;
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BEBaseResponse> updateShareHolder({
    required RequestIdParams params,
    required String symbol,
    required String transType,
  }) async {
    try {
      final response = await _baseDio.post(
        PlaceOrderPathApi.shareHolder,
        data: {
          ...params.toJson(),
          'via': 'Z',
          'symbol': symbol,
          'transType': transType,
        },
      );
      final beResponse = BEBaseResponse.fromJson(response.data);
      if (beResponse.isSuccess()) {
        return BEBaseResponse.fromJson(response.data);
      } else {
        throw ResponseError.fromJson(response.data);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<bool> downloadShareHolderPdf() async {
    try {
      final byteData = await rootBundle.load(StockKeyAssets.pdfShareHolder);
      final filePath = await DownloadFileManager.instance.saveFileWithByteData(
        byteData: byteData,
        fileName: 'TT96_2020_TT-BTC',
        extension: FileExtension.pdf,
      );
      return filePath != null;
    } catch (e) {
      return false;
    }
  }

  // final _indexCode = ['VNINDEX', 'HNXINDEX', 'UPCOMINDEX'];
  final _indexCode = ['VNINDEX'];

  List<entity.StockDetailEntity> _parseList(
    Response<dynamic> response,
    String indexCode,
  ) {
    final beResponse = BEBaseResponse.fromJson(response.data);
    if (beResponse.isSuccess() && beResponse.data is Map) {
      return ((beResponse.data['indexCode']?[indexCode] ?? []) as List)
          .map((e) => StockDetailModel.fromJson(e).entity)
          .toList();
    } else {
      throw ResponseError.fromJson(response.data);
    }
  }

  @override
  Future<List<entity.StockDetailEntity>> getMultiBoardStockInfoByList() async {
    try {
      final response = await Future.wait(
        _indexCode.map(
          (e) => _restKrxDio.get(
            PlaceOrderPathApi.multiBoardStockInfoByList,
            queryParameters: {'boardId': 'G7', 'indexCode': e},
          ),
        ),
      );
      return response
          .mapIndexed(
            (index, element) => _parseList(element, _indexCode[index]),
          )
          .expand((e) => e)
          .toList();
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<List<entity.StockDetailEntity>> getStockInfoByListMultipleBoard(
    String symbol,
  ) async {
    try {
      final response = await _restKrxDio.get(
        PlaceOrderPathApi.stockInfoByListMultipleBoard,
        queryParameters: {'boardId': 'G7', 'symbols': symbol},
      );
      final beResponse = BEBaseResponse.fromJson(response.data);

      if (beResponse.isSuccess() && beResponse.data != null) {
        return transformListStockEntities((beResponse.data as Map)['symbols']);
      } else {
        throw ResponseError.fromJson(response.data);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<MarketPriceChartEntity> getMarketPriceChart(String marketCode) async {
    try {
      // Now this will work with the extension
      var response = await _restDio
          .transformGet(
            '/invest/api/v2/marketInfo?Marketcode=$marketCode',
            transformMarketChartEntity,
          )
          .then((response) => response);

      return response;
    } catch (e) {
      throw HandleError.from(e);
    }
  }
}
