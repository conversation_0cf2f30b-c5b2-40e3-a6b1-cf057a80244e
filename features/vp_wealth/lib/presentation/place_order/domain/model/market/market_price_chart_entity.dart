import 'package:collection/collection.dart';
import 'package:vp_wealth/presentation/place_order/domain/model/market/chart_price_market_entity.dart';
import 'package:vp_wealth/presentation/place_order/domain/model/market/market_data_default.dart';
import 'market_entity.dart';

class MarketPriceChartEntity {
  double reference;
  num? totalVolume;
  String indexColor;
  double indexChange;
  double indexPercentChange;
  double marketIndex;
  String? marketCode;
  String? indexTime;
  num? totalValue;
  String? marketStatus;
  List<IndexEntity> index;
  int? advances;
  int? declines;
  int? noChange;
  int? numOfCeiling;
  int? numOfFloor;
  num? totalTrade;
  num? oddLotTotalValue;
  num? oddLotTotalVolume;

  MarketPriceChartEntity({
    required this.reference,
    required this.indexColor,
    required this.indexChange,
    required this.indexPercentChange,
    required this.marketIndex,
    required this.indexTime,
    this.marketCode,
    required this.totalVolume,
    required this.index,
    required this.totalValue,
    required this.marketStatus,
    required this.advances,
    required this.declines,
    required this.noChange,
    required this.numOfFloor,
    required this.numOfCeiling,
    required this.totalTrade,
    required this.oddLotTotalValue,
    required this.oddLotTotalVolume,
  });
}

extension MarketEntityMapper on MarketPriceChartEntity {
  MarketEntity get marketEntity => MarketEntity(
        indexColor: indexColor,
        marketCode: marketCode ?? 'HOSE',
        marketIndex: marketIndex,
        marketId: '',
        indexChange: indexChange,
        indexPercentChange: indexPercentChange,
        totalValue: totalValue,
        marketStatus: marketStatus,
        advances: advances,
        declines: declines,
        noChange: noChange,
        numberOfCe: numOfCeiling,
        numberOfFl: numOfFloor,
        reference: reference,
        totalTrade: totalTrade,
        oddLotTotalValue: oddLotTotalValue,
        oddLotTotalVolume: oddLotTotalVolume,
        indexTime: indexTime,
      );
}

extension MarketEntityListMapper on List<MarketPriceChartEntity?> {
  List<MarketEntity> get marketEntityList {
    if (every((e) => e == null)) {
      return MarketDataDefault.getListMarketDefault();
    }
    return mapIndexed((index, e) => e == null
        ? MarketDataDefault.getListErrorMarket()[index]
        : e.marketEntity).toList();
  }
}

class IndexEntity {
  double marketIndex;

  String indexTime;

  num? volume;

  num? totalVolume;

  num? totalValue;

  IndexEntity({
    required this.marketIndex,
    required this.indexTime,
    required this.volume,
    required this.totalVolume,
    required this.totalValue,
  });

  IndexEntity copyWith({
    double? marketIndex,
    String? indexTime,
    num? volume,
    num? totalVolume,
    num? totalValue,
  }) {
    return this
      ..marketIndex = marketIndex ?? this.marketIndex
      ..indexTime = indexTime ?? this.indexTime
      ..volume = volume ?? this.volume
      ..totalVolume = totalVolume ?? this.totalVolume
      ..totalValue = totalValue ?? this.totalValue;
  }
}

extension ChartPriceMarketEntityListMapper on List<IndexEntity> {
  List<ChartPriceMarketEntity> get chartPriceMarketEntity => mapIndexed(
        (index, e) => ChartPriceMarketEntity(
          x: index,
          close: e.marketIndex,
          time: e.indexTime,
          volume: e.volume,
          totalVolume: e.totalVolume,
          totalValue: e.totalValue,
        ),
      ).toList();
}
