import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:vp_wealth/presentation/place_order/gen/locale_keys.g.dart';

class GtcEffectiveTime extends Equatable {
  final DateTime? start;
  final DateTime? end;

  const GtcEffectiveTime({this.start, this.end});

  DateTime get startDate => start ?? DateTime.now();

  DateTime get endDate => end ?? DateTime.now();

  String get startText =>
      start == null ? '' : '${start!.day}-${start!.month}-${start!.year}';

  String get endText =>
      end == null ? '' : '${end!.day}-${end!.month}-${end!.year}';

  bool get isDone => start != null && end != null;

  GtcEffectiveTime.init()
      : start = DateTime.now(),
        end = DateTime.now();

  @override
  String toString() => isDone
      ? '${start!.day}/${start!.month}/${start!.year}-${end!.day}/${end!.month}/${end!.year}'
      : 'Chọn kho<PERSON>ng thời gian';

  @override
  List<Object?> get props => [start, end];
}
