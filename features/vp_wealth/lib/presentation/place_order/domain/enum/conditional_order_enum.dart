import 'package:easy_localization/easy_localization.dart';
import 'package:vp_common/utils/navigation_service.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/lang/stock_localized_values.dart';
import 'package:vp_wealth/presentation/place_order/gen/locale_keys.g.dart';

enum ConditionalOrderPriceTypeEnum {
  greaterThan,
  lessThan,
}

extension ConditionalOrderPriceTypeEnumExt on ConditionalOrderPriceTypeEnum {
  String toParamRequest() {
    switch (this) {
      case ConditionalOrderPriceTypeEnum.greaterThan:
        return 'UP';
      case ConditionalOrderPriceTypeEnum.lessThan:
        return 'DOWN';
      default:
        return 'UP';
    }
  }

  String getSymbol() {
    switch (this) {
      case ConditionalOrderPriceTypeEnum.greaterThan:
        return '≥';
      case ConditionalOrderPriceTypeEnum.lessThan:
        return '≤';
      default:
        return '≥';
    }
  }
}

enum ConditionalDilutionTypeEnum { adp, cancel }

extension ConditionalDilutionTypeExt on ConditionalDilutionTypeEnum {
  String toParamRequest() {
    switch (this) {
      case ConditionalDilutionTypeEnum.adp:
        return 'ADP';
      case ConditionalDilutionTypeEnum.cancel:
        return 'CANCEL';
      default:
        return 'ADP';
    }
  }

  String toStringShowDialog() {
    final context =
              GetIt.instance<NavigationService>().navigatorKey.currentContext!;
    switch (this) {
      case ConditionalDilutionTypeEnum.adp:
        return 'Tự động điều chỉnh giá kích hoạt & giá đặt';
      case ConditionalDilutionTypeEnum.cancel:
        return getStockLang(StockKeyLang.cancelCommand);
      default:
        return 'Tự động điều chỉnh giá kích hoạt & giá đặt';
    }
  }
}

enum FlexPriceType {
  market,
  limit,
}

extension FlexPriceTypeExt on FlexPriceType {
  String toParamRequest() {
    switch (this) {
      case FlexPriceType.market:
        return 'MARKET';
      case FlexPriceType.limit:
        return 'LIMIT';
      default:
        return 'MARKET';
    }
  }
}
