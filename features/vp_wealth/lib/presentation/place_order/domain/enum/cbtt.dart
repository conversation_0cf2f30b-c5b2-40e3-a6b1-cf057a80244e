import 'package:vp_common/error/transform_error.dart';

class CBTT {
  late final String ec;

  CBTT(Object object)
      : ec = object is ResponseError ? object.code.toString() : '';

  bool get isNot => ec.toString() == '-700090' || ec.toString() == '-700091';

  bool get isOverVolume => ec.toString() == '-700092';

  bool get isNotEffectTime => ec.toString() == '-700093';

  bool get isShowDialog => isNot || isOverVolume || isNotEffectTime;

  String get content => isNot
      ? '<p>Tổng giá trị giao dịch (trên ngày/tháng) vượt quá số lượng giao dịch không phải công bố thông tin theo quy định. Vui lòng liên hệ <span>Nhân viên chăm sóc</span> hoặc Tổng đài Dịch vụ khách hàng <span>1900 636 679</span> để được hỗ trợ.</p>'
      : isOverVolume
          ? '<p>Tổng giá trị giao dịch vượt quá giá trị đã đăng ký công bố thông tin. Vui lòng liên hệ <span>Nhân viên chăm sóc</span> hoặc Tổng đài Dịch vụ khách hàng <span>1900 636 679</span> để được hỗ trợ.</p>'
          : '<p>Tổng giá trị giao dịch vượt quá số lượng quy định của NNB/NLQ do chưa đến thời gian hiệu lực đã công bố thông tin. Vui lòng liên hệ <span>Nhân viên chăm sóc</span> hoặc Tổng đài Dịch vụ khách hàng <span>1900 636 679</span> để được hỗ trợ.</p>';
}
