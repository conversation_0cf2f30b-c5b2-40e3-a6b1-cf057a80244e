import 'package:dio/dio.dart';
import 'package:vp_common/error/handle_error.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/presentation/place_order/domain/conditional_order_repository.dart';
import 'package:vp_wealth/presentation/place_order/domain/params_request/new_conditional_order_params.dart';
import '../../../common/utils/stock_utils.dart';
import '../data/data.dart';

class ConditionalOrderRepositoryIml implements ConditionalOrderRepository {
  final Dio _restClient;

  ConditionalOrderRepositoryIml(this._restClient);

  @override
  Future<BEBaseResponse> conditionalOrderRequest(
      NewConditionalOrderParams params) async {
    try {
      Response response = await _restClient.post(
        PlaceOrderPathApi.conditionalOrderRequest,
        data: params.toJson(),
        options: Options(headers: StockUtils.orderHeaders),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result;
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
  }
}
