import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';

class SuggestItem extends StatelessWidget {
  const SuggestItem({Key? key, required this.text, required this.onTap})
    : super(key: key);
  final String text;
  final ValueChanged<String> onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onTap(text),
      behavior: HitTestBehavior.opaque,
      child: Container(
        height: SizeUtils.kSize40,
        alignment: Alignment.center,
        child: AutoSizeText(
          text,
          style: vpTextStyle.body14?.copyWith(color: vpColor.textPrimary),
          minFontSize: 2,
          maxLines: 1,
        ),
      ),
    );
  }
}
