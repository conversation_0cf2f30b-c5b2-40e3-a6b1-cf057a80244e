import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:vp_core/theme/theme_service.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/button_bottom_sheet.dart';
import 'package:vp_design_system/custom_widget/divider_widget.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/lang/stock_localized_values.dart';
import 'package:vp_wealth/presentation/place_order/gen/locale_keys.g.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/widget/container_hepler.dart';

class DialogSelectSubAccount extends StatelessWidget {
  const DialogSelectSubAccount({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        padding: const EdgeInsets.all(SizeUtils.kSize8),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Container(
              width: double.infinity,
              decoration: ContainerHelper.decorationBottom(),
              child: ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder:
                    (_, index) => InkWell(
                      onTap: () {
                        context.pop(SubAccountType.values[index]);
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(SizeUtils.kSize16),
                        child: Center(
                          child: Text(
                            index == 0
                                ? LocaleKeys.placeOrder_subAccount_ordinaryFull
                                    .tr()
                                : LocaleKeys.placeOrder_subAccount_depositFull
                                    .tr(),
                            style: vpTextStyle.body16?.copyWith(
                              color: themeData.gray700,
                            ),
                          ),
                        ),
                      ),
                    ),
                separatorBuilder: (_, __) => const DividerWidget(),
                itemCount: 2,
              ),
            ),
            const SizedBox(height: SizeUtils.kSize8),
            ButtonBottomSheet(
              text: getStockLang(StockKeyLang.cancel),
              color: ColorDefine.red,
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }
}
