import 'package:equatable/equatable.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/order.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/order_type.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/stock_order_entity.dart';
import 'package:vp_wealth/presentation/wealths/search/bloc/search_cubit.dart';

part 'dialog_select_symbol_state.dart';

class DialogSelectSymbolCubit extends SearchCubit<StockOrderEntity> {
  final Order order;
  final OrderType orderType;
  final SubAccountType subAccount;
  final List<StockOrderEntity> allSymbolList;

  DialogSelectSymbolCubit({
    required this.order,
    required this.orderType,
    required this.subAccount,
    required this.allSymbolList,
  }) : super(
         queries: [(e) => e.symbol],
         sorts: [
           (e, _) => e.searchType.index,
           (e, query) => e.symbol.indexOf(query.toUpperCase()),
           (e, _) => e.symbol.length,
           (e, _) => e.symbol,
         ],
       );

  @override
  Future<List<StockOrderEntity>> getData() async {
    if (order == Order.buy || orderType == OrderType.gtc) {
      return allSymbolList;
    }
    final holdingList = await GetIt.instance
        .get<WealthRepository>()
        .getSecuritiesPortfolio(
          GetIt.instance.get<SubAccountCubit>().getSubAccount(subAccount)?.id ??
              '',
        );
    final tradeList = holdingList.where((holding) => (holding.trade ?? 0) > 0);
    return allSymbolList
        .where(
          (stock) => tradeList.any((trade) => stock.symbol == trade.symbol),
        )
        .toList();
  }
}
