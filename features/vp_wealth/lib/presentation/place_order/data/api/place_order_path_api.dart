class PlaceOrderPathApi {
  /*-----select stock-----*/
  // Lấy thông tin các mã chứng khoán nắm giữ của tài khoản
  static const securities = '/inq/securities';

  // Lấy các mã chứng khoán
  static const quotes = '/quotes';
  static const instruments = '/datafeed/instruments';

  /*-----order-----*/
  // Lấy thông tin sức mua của tiểu khoản
  static availableTrade(String accountId) =>
      '/flex/inq/accounts/$accountId/availableTrade';

  // Tạo verify cho transaction đặt lệnh
  static initVerifyTransaction(String accountId) =>
      '/accounts/$accountId/initVerifyTransaction';

  // preCheck Đặt lệnh
  static preCheckOrder(String accountId) =>
      '/accounts/$accountId/precheckOrder';

  // Kiểm tra mã xác thực cho phần Order
  static const twoFactorAuth = '/twoFactorAuth';

  // Đặt lệnh
  static orders(String accountId) => '/accounts/$accountId/orders';

  // Đặt lệnh điều kiện
  static conditionOrder(String accountId) =>
      '/accounts/$accountId/conditionOrder';

  // Đặt lệnh sale support
  static const String orderRecommendation = '/sales-support/customer/stocks/order';

  // Đặt lệnh điều kiện
  static const String conditionalOrderRequest =
      '/condition-order/api/stoploss/newConditionOrderRequest';

  // Cảnh báo thông tin trước giao dịch của NNB, NLQ
  static const String shareHolder =
      '/flex-connector-external-api/external/v1/holders';

  // Bảng giá phiên Buy-in
  static const String multiBoardStockInfoByList =
      '/invest/api/v2/multiBoardStockInfoByList';

  // Bảng giá phiên Buy-in
  static const String stockInfoByListMultipleBoard =
      '/invest/api/v2/stockInfoByListMultipleBoard';
}
