import 'package:vp_common/constants/app_constants.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/order.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/place_order_enum.dart';

class OrderRecommendationRequest {
  OrderRecommendationRequest({
    this.requestId,
    this.stockRecommendationId,
    this.price,
    this.side,
    this.startDate,
    this.volume,
    this.expiredDate,
    this.channel,
    this.collaborationId,
    this.accountId,
    this.orderCode,
    this.sessionType,
    this.subAccount,
    this.authType,
    this.transactionDate,
    this.symbol,
  });

  String? stockRecommendationId;
  String? collaborationId;
  SubAccountType? subAccount;
  OrderType? orderCode;
  SessionType? sessionType;
  String? channel;
  Order? side;
  String? price;
  String? volume;
  String? startDate;
  String? expiredDate;
  String? requestId;
  String? authType;
  String? transactionDate;
  String? accountId;
  String? symbol;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['stockRecommendationId'] = stockRecommendationId;
    map['symbol'] = symbol;
    map['transactionDate'] = transactionDate;
    map['orderCode'] = (sessionType ?? orderCode)?.name.toUpperCase();
    map['authType'] = authType;
    map['accountId'] = accountId;
    map['collaborationId'] = collaborationId;
    map['volume'] = volume;
    map['channel'] = channel;
    map['requestId'] = requestId;
    map['subAccount'] = subAccount?.name.toUpperCase();
    map['startDate'] = startDate;
    map['expiredDate'] = expiredDate;
    map['side'] = side?.name.toUpperCase();
    map['price'] =
        sessionType == null ? price : sessionType!.name.toUpperCase();
    map['via'] = AppConstants.viaApp;
    return map;
  }
}
