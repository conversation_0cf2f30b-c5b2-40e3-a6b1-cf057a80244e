import 'package:collection/collection.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/data/model/stock_detail_entity.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/presentation/place_order/domain/model/market/market_price_chart_entity.dart';
import 'package:vp_wealth/presentation/place_order/domain/model/market/market_price_chart_model.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/domain/entity/stock_portfolio_entity.dart';

class SecuritiesPortfolioResponsesModel {
  String? accountID;
  String? symbol;
  int? total;
  int? trade;
  int? blocked;
  int? vsdMortgage;
  int? restrict;
  int? receivingRight;
  int? receivingT0;
  int? receivingT1;
  int? receivingT2;
  num? costPrice;
  String? marginAmt;
  int? withDraw;
  int? mortgage;
  num? basicPrice;
  num? basicPriceOrigin;
  int? matchingAmt;
  String? issell;
  String? sectype;
  String? custodycd;
  String? closeprice;
  num? costPriceAmt;
  num? basicPriceAmt;
  int? totalpnl;
  String? producttypename;
  double ceilingPrice = 0;
  double floorPrice = 0;
  double referencePrice = 0;

  num totalProfit = 0;
  num percentTotalProfit = 0;
  double todayChangePrice = 0;
  double? todayChangePercent;

  SecuritiesPortfolioResponsesModel({
    this.accountID,
    this.symbol,
    this.total,
    this.trade,
    this.blocked,
    this.vsdMortgage,
    this.restrict,
    this.receivingRight,
    this.receivingT0,
    this.receivingT1,
    this.receivingT2,
    this.costPrice,
    this.marginAmt,
    this.withDraw,
    this.mortgage,
    this.basicPrice,
    this.basicPriceOrigin,
    this.matchingAmt,
    this.issell,
    this.sectype,
    this.custodycd,
    this.closeprice,
    this.costPriceAmt,
    this.basicPriceAmt,
    this.totalpnl,
    this.producttypename,
  });

  SecuritiesPortfolioResponsesModel.fromJson(Map<String, dynamic> json) {
    accountID = json['accountID'];
    symbol = json['symbol'];
    total = json['total'];
    trade = json['trade'];
    blocked = json['blocked'];
    vsdMortgage = json['vsdMortgage'];
    restrict = json['restrict'];
    receivingRight = json['receivingRight'];
    receivingT0 = json['receivingT0'];
    receivingT1 = json['receivingT1'];
    receivingT2 = json['receivingT2'];
    costPrice = json['costPrice'];
    marginAmt = json['marginAmt'];
    withDraw = json['withDraw'];
    mortgage = json['mortgage'];
    basicPrice = json['basicPrice'];
    basicPriceOrigin = json['basicPrice'];
    matchingAmt = json['matchingAmt'];
    issell = json['issell'];
    sectype = json['sectype'];
    custodycd = json['custodycd'];
    percentTotalProfit =
        json['pnlrate'] is num
            ? json['pnlrate']
            : num.tryParse(
                  json['pnlrate'].toString().trim().replaceAll(',', ''),
                ) ??
                0;
    closeprice = json['closeprice'];
    costPriceAmt = json['costPriceAmt'];
    basicPriceAmt = json['basicPriceAmt'];
    totalpnl = json['totalpnl'];
    producttypename = json['producttypename'];
    totalProfit = json['pnlamt'] ?? 0;

    try {
      // final stock = AppData().allStocks.firstWhereOrNull(
      //   (element) => symbol == element.symbol,
      // );
      final stock = GetIt.instance
          .get<WealthRepository>()
          .getAllStockAtSplash()
          .then((onValue) {
            return onValue.firstWhereOrNull(
              (element) => symbol == element.symbol,
            );
          });

      // ceilingPrice = stock?.ceilingPrice ?? 0;
      // floorPrice = stock?.floorPrice ?? 0;
      // referencePrice = stock?.referencePrice ?? 0;
      ceilingPrice = 0;
      floorPrice = 0;
      referencePrice = 0;
      todayChangePercent =
          referencePrice == 0
              ? 0
              : (((basicPrice ?? 0) - referencePrice) / referencePrice) * 100;
    } catch (e) {
      ceilingPrice = 0;
      floorPrice = 0;
      referencePrice = 0;
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['accountID'] = accountID;
    data['symbol'] = symbol;
    data['total'] = total;
    data['trade'] = trade;
    data['blocked'] = blocked;
    data['vsdMortgage'] = vsdMortgage;
    data['restrict'] = restrict;
    data['receivingRight'] = receivingRight;
    data['receivingT0'] = receivingT0;
    data['receivingT1'] = receivingT1;
    data['receivingT2'] = receivingT2;
    data['costPrice'] = costPrice;
    data['marginAmt'] = marginAmt;
    data['withDraw'] = withDraw;
    data['mortgage'] = mortgage;
    data['basicPrice'] = basicPrice;
    data['matchingAmt'] = matchingAmt;
    data['issell'] = issell;
    data['sectype'] = sectype;
    data['custodycd'] = custodycd;
    data['pnlrate'] = percentTotalProfit;
    data['closeprice'] = closeprice;
    data['costPriceAmt'] = costPriceAmt;
    data['basicPriceAmt'] = basicPriceAmt;
    data['totalpnl'] = totalpnl;
    data['producttypename'] = producttypename;
    return data;
  }

  String get percentTotalProfitString =>
      (costPrice ?? 0) != 0
          ? percentTotalProfit.toDouble().getValuePercentAbs()
          : '';
}

extension StockPortfolioMapper on SecuritiesPortfolioResponsesModel {
  StockPortfolioEntity get entity => StockPortfolioEntity(
    symbol: symbol ?? '',
    totalVol: total ?? 0,
    availableToTradeVol: trade ?? 0,
    costPrice: costPrice ?? 0,
    accountId: accountID ?? '',
    productTypeName: producttypename ?? '',
    basicPrice: basicPrice ?? 0,
  );

  SecuritiesPortfolioResponsesModel copyWith(StockDetailEntity? stock) {
    if (stock == null) return this;

    final _basicPrice = stock.price;
    final _costPrice = costPrice ?? 0.0;

    final _totalProfit = (_basicPrice - _costPrice) * (total ?? 0);
    final _percentTotalProfit =
        _costPrice == 0 ? 0.0 : (_basicPrice - _costPrice) / _costPrice * 100;

    this
      ..basicPrice = _basicPrice
      ..floorPrice = stock.floorPrice
      ..ceilingPrice = stock.ceilingPrice
      ..referencePrice = stock.referencePrice
      ..totalProfit = _totalProfit
      ..percentTotalProfit = _percentTotalProfit
      ..todayChangePrice = stock.changeValue ?? 0.0
      ..todayChangePercent = stock.changePercent;
    return this;
  }

  SecuritiesPortfolioResponsesModel mixWithWFT(
    SecuritiesPortfolioResponsesModel wftData,
  ) {
    final mixTotal = (total ?? 0) + (wftData.total ?? 0);
    final mixTotalCapital =
        (total ?? 0) * (costPrice ?? 0) +
        (wftData.total ?? 0) * (wftData.costPrice ?? 0);
    final mixProfit = totalProfit + wftData.totalProfit;
    final mixProfitPercent = mixProfit * 100 / mixTotalCapital;
    final wftReceivingRight = wftData.receivingRight ?? 0;
    final mixReceivingRight =
        wftReceivingRight == 0 ? wftData.trade : wftReceivingRight;

    final mixBlocked = (blocked ?? 0) + (wftData.blocked ?? 0);
    final mixMortgage = (vsdMortgage ?? 0) + (wftData.vsdMortgage ?? 0);
    final mixRestrict = (restrict ?? 0) + (wftData.restrict ?? 0);

    this
      ..total = mixTotal
      ..costPrice = mixTotalCapital / mixTotal
      ..costPriceAmt = mixTotalCapital
      ..totalProfit = mixProfit
      ..percentTotalProfit = mixProfitPercent
      ..receivingRight = mixReceivingRight
      ..blocked = mixBlocked
      ..vsdMortgage = mixMortgage
      ..restrict = mixRestrict;
    return this;
  }

  void mixCostPriceWithWFT(SecuritiesPortfolioResponsesModel wftData) {
    final mixTotal = (total ?? 0) + (wftData.total ?? 0);
    final mixTotalCapital =
        (total ?? 0) * (costPrice ?? 0) +
        (wftData.total ?? 0) * (wftData.costPrice ?? 0);
    costPrice = mixTotalCapital / mixTotal;
  }
}

extension ListSecuritiesPortfolioResponsesModelExt
    on List<SecuritiesPortfolioResponsesModel> {
  List<SecuritiesPortfolioResponsesModel> mixSymbolWFT() {
    final symbols = map((e) => e.symbol ?? '').toList();

    final listSymbolWFT = symbols.where((symbol) => symbol.contains('_WFT'));

    if (listSymbolWFT.isEmpty) {
      return this;
    }

    for (var symbolWFT in listSymbolWFT) {
      final indexOfWFT = symbolWFT.indexOf('_WFT');
      final symbol = symbolWFT.substring(0, indexOfWFT);
      final symbolWFTData = firstWhere((data) => data.symbol == symbolWFT);

      /// in list includes both WFT symbol and normal symbol
      if (symbols.contains(symbol)) {
        final symbolData = firstWhere((data) => data.symbol == symbol);
        symbolData.mixWithWFT(symbolWFTData);
        remove(symbolWFTData);
      } else {
        /// in list includes only WFT symbol
        symbolWFTData.symbol = symbol;
        if (symbolWFTData.receivingRight == 0) {
          symbolWFTData.receivingRight = symbolWFTData.trade;
        }
        symbolWFTData.trade = 0;
      }
    }
    return this;
  }
}

List<StockPortfolioEntity> transformStockPortfolioEntity(dynamic json) =>
    (json as List<dynamic>)
        .map((e) => SecuritiesPortfolioResponsesModel.fromJson(e))
        .toList()
        .mixSymbolWFT()
        .map((e) => e.entity)
        .toList();

MarketPriceChartEntity transformMarketChartEntity(dynamic json) =>
    MarketPriceChartModel.fromJson(
      (json as Map<String, dynamic>)["data"],
    ).entity;
