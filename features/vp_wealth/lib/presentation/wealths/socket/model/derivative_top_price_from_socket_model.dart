/// e : "TopNPrice"
/// s : "VN30F2404"
/// t : 1
/// bp : 1268.90
/// bq : 2
/// cb : 2
/// sp : 1269
/// sq : 42
/// cs : 42
/// a : "U"
/// ok : 1

class DerivativeTopNPriceFromSocketModel {
  DerivativeTopNPriceFromSocketModel({
    String? e,
    String? symbol,
    num? top,
    String? buyPrice,
    num? buyQuantity,
    num? cumulativeBuyVolume,
    String? sellPrice,
    num? sellQuantity,
    num? cumulativeSellVolume,
    String? action,
    num? ok,
  }) {
    _e = e;
    _s = symbol;
    _t = top;
    _bp = buyPrice;
    _bq = buyQuantity;
    _cb = cumulativeBuyVolume;
    _sp = sellPrice;
    _sq = sellQuantity;
    _cs = cumulativeSellVolume;
    _a = action;
    _ok = ok;
  }

  DerivativeTopNPriceFromSocketModel.fromJson(dynamic json) {
    _e = json['e'];
    _s = json['s'];
    _t = json['t'];
    _bp = json['bp'];
    _bq = json['bq'];
    _cb = json['cb'];
    _sp = json['sp'];
    _sq = json['sq'];
    _cs = json['cs'];
    _a = json['a'];
    _ok = json['ok'];
  }

  String? _e;
  String? _s;
  num? _t;
  String? _bp;
  num? _bq;
  num? _cb;
  String? _sp;
  num? _sq;
  num? _cs;
  String? _a;
  num? _ok;

  DerivativeTopNPriceFromSocketModel copyWith({
    String? e,
    String? s,
    num? t,
    String? bp,
    num? bq,
    num? cb,
    String? sp,
    num? sq,
    num? cs,
    String? a,
    num? ok,
  }) =>
      DerivativeTopNPriceFromSocketModel(
        e: e ?? _e,
        symbol: s ?? _s,
        top: t ?? _t,
        buyPrice: bp ?? _bp,
        buyQuantity: bq ?? _bq,
        cumulativeBuyVolume: cb ?? _cb,
        sellPrice: sp ?? _sp,
        sellQuantity: sq ?? _sq,
        cumulativeSellVolume: cs ?? _cs,
        action: a ?? _a,
        ok: ok ?? _ok,
      );

  String? get e => _e;

  String? get symbol => _s;

  num? get top => _t;

  String? get buyPrice => _bp;

  num? get buyQuantity => _bq;

  num? get cumulativeBuyVolume => _cb;

  String? get sellPrice => _sp;

  num? get sellQuantity => _sq;

  num? get cumulativeSellVolume => _cs;

  String? get action => _a;

  num? get ok => _ok;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['e'] = _e;
    map['s'] = _s;
    map['t'] = _t;
    map['bp'] = _bp;
    map['bq'] = _bq;
    map['cb'] = _cb;
    map['sp'] = _sp;
    map['sq'] = _sq;
    map['cs'] = _cs;
    map['a'] = _a;
    map['ok'] = _ok;
    return map;
  }
}
