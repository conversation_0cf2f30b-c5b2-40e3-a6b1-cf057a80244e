enum ISocketChannel {
  marketData,
  marketInfo,
  stockInfo,
  marketStockInfo,
  topNPrice,
  marketEvent,
  marketVolume,
  stockImpact,
  subscribe,
  unsubscribe,
}

extension ISocketChannelExt on ISocketChannel {
  String get nameChannel {
    switch (this) {
      case ISocketChannel.marketData:
        return 'marketdata';
      case ISocketChannel.marketInfo:
        return 'marketinfo';
      case ISocketChannel.stockInfo:
        return 'stockinfo';
      case ISocketChannel.marketStockInfo:
        return 'market_stockinfo';
      case ISocketChannel.topNPrice:
        return 'topNPrice';
      case ISocketChannel.marketEvent:
        return 'marketEvent';
      case ISocketChannel.marketVolume:
        return 'marketVolume';
      case ISocketChannel.stockImpact:
        return 'stock_impact';
      case ISocketChannel.subscribe:
        return 'subscribe';
      case ISocketChannel.unsubscribe:
        return 'unsubscribe';
      default:
        return '';
    }
  }

  String get nameSocket {
    switch (this) {
      case ISocketChannel.marketData:
        return 'aggTrade';
      case ISocketChannel.marketInfo:
        return 'aggMarket';
      case ISocketChannel.stockInfo:
        return 'stockInfo';
      case ISocketChannel.marketStockInfo:
        return 'stockInfo';
      case ISocketChannel.topNPrice:
        return 'TopNPrice';
      case ISocketChannel.marketEvent:
        return 'marketEvent';
      case ISocketChannel.marketVolume:
        return 'marketVolume';
      case ISocketChannel.stockImpact:
        return 'stockImpact';
      case ISocketChannel.subscribe:
        return 'subscribe';
      case ISocketChannel.unsubscribe:
        return 'unsubscribe';
      default:
        return '';
    }
  }
}
