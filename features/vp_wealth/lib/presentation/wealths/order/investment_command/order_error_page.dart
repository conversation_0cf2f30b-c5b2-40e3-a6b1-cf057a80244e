import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/utils/go_router_helper.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/data/utils/padding_extends.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/router/wealth_router.dart';

class OrderErrorPage extends StatelessWidget {
  final String? messageError;

  const OrderErrorPage({Key? key, this.messageError}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        top: false,
        child: Stack(
          children: [
            SizedBox(
              width: double.infinity,
              child: Assets.images.backgroundError.image(fit: BoxFit.fill),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                margin: EdgeInsets.only(
                  top: (108 + MediaQuery.of(context).padding.top),
                ),
                decoration: BoxDecoration(
                  color: vpColor.backgroundElevation0,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(SizeUtils.kSize16),
                    topRight: Radius.circular(SizeUtils.kSize16),
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Assets.icons.icCancel
                        .svg(width: 160, height: 160)
                        .paddingOnly(bottom: 0, top: 100),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: SizeUtils.kSize48,
                      ),
                      child: Text(
                        'Giao dịch không thành công',
                        style: vpTextStyle.headineBold6?.copyWith(
                          color: vpColor.textPrimary,
                        ),
                      ),
                    ),
                    kSpacingHeight4,
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: SizeUtils.kSize24,
                      ),
                      child: Text(
                        messageError ?? '',
                        textAlign: TextAlign.center,
                        style: vpTextStyle.body14?.copyWith(
                          color: vpColor.textSecondary,
                        ),
                      ),
                    ),
                    const Spacer(),
                    VpsButton.primarySmall(
                      title: 'Xem sổ lệnh',
                      width: double.infinity,
                      alignment: Alignment.center,
                      onPressed: () {
                        context.popUntilRoute(WealthRouter.wealthMainPage);
                        context.replaceNamed(
                          WealthRouter.wealthMainPage,
                          extra: 2,
                        );
                      },
                    ).paddingSymmetric(horizontal: 16, vertical: 0),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
