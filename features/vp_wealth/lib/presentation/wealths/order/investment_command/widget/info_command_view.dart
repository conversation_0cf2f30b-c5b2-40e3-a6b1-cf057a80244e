import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/enum/investment_command_status_enum.dart';
import 'package:vp_wealth/data/model/order/investment_command_model.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/place_order_bloc_map_import.dart';
import 'package:vp_wealth/presentation/wealths/widgets/utils/item_row_text.dart';

class InfoCommandView extends StatelessWidget {
  final InvestmentCommandModel model;
  final bool showStatus;
  final String? transactionTypeName;

  const InfoCommandView({
    Key? key,
    required this.model,
    this.showStatus = true,
    this.transactionTypeName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            if (showStatus) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Trạng thái',
                    style: vpTextStyle.body14?.copyWith(
                      color: vpColor.textTertiary,
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(SizeUtils.kRadius4),
                      color: model.status?.color.withOpacity(
                        context.read<ThemeCubit>().currentTheme == AppTheme.dark
                            ? 0.32
                            : 0.16,
                      ),
                    ),
                    padding: const EdgeInsets.symmetric(
                      vertical: SizeUtils.kSize4,
                      horizontal: SizeUtils.kSize8,
                    ),
                    child: Text(
                      model.status?.title ?? '',
                      style: vpTextStyle.captionSemiBold?.copyWith(
                        color: model.status?.textColor,
                      ),
                    ),
                  ),
                ],
              ),
              kSpacingHeight4,
            ],
            ItemRowText(
              title: 'Loại giao dịch',
              value: 'Mua',
              textColor: vpColor.textAccentGreen,
            ),
            ItemRowText(
              title: 'Loại đầu tư',
              value: transactionTypeName ?? model.transactionTypeName,
            ),
            ItemRowText(title: 'Ngày giao dịch', value: model.createdDate),
            ItemRowText(
              title: 'Số tài khoản',
              value:
                  GetIt.instance<AuthCubit>().userInfo?.userinfo?.custodycd ??
                  '',
            ),
            const ItemRowText(title: 'Loại lệnh', value: 'Lệnh thường'),
            ItemRowText(
              title: 'Tổng giá trị giao dịch dự kiến',
              value: model.totalCurrentValue.valueText,
            ),
          ],
        ),
      ],
    );
  }
}
