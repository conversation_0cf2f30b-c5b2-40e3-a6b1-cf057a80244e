import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:vp_common/utils/money_utils.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/data/enum/investment_command_status_enum.dart';
import 'package:vp_wealth/data/enum/transaction_kind_enum.dart';
import 'package:vp_wealth/data/enum/transaction_type_enum.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/router/wealth_router.dart';
import '../../../../../data/model/order/investment_order_model.dart';
import '../accept_command_detail_page.dart';

class InvestmentOrderItem extends StatefulWidget {
  String date;
  List<InvestmentOrderModel> investItems;
  // final VoidCallback onDetail;

  InvestmentOrderItem({Key? key, required this.date, required this.investItems})
    : super(key: key);

  @override
  State<InvestmentOrderItem> createState() => _InvestmentOrderItemState();
}

class _InvestmentOrderItemState extends State<InvestmentOrderItem> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            // widget.date.stringToDateDdMmYyyy(),
            widget.date,
            style: vpTextStyle.body14?.copyWith(color: vpColor.textSecondary),
          ),
          kSpacingHeight8,
          Column(
            children:
                widget.investItems.map((item) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: InkWell(
                      onTap: () {
                        // navigate two screen
                        if (item.commandStatus ==
                            InvestmentCommandStatusEnum.accept) {
                          // trường hợp lệnh đã xác nhận
                          // -> navigate tới màn hình lệnh đình kỳ
                          // call api wea-048
                          context.push(
                            WealthRouter.acceptCommandDetailPage,
                            extra: AcceptCommandDetailArguments(
                              transactionTypeName: 'Đầu tư chủ động',
                              model: item,
                            ),
                          );
                        } else {
                          // lệnh : hết hiệu lực và chờ xác nhận,
                          // -> navitate tới màn hình chi tiét xác nhận lệnh
                          // call api : [wea-020]
                          context.push(
                            WealthRouter.awaitCommandPage,
                            extra: item.id ?? 0,
                          );
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.all(
                            Radius.circular(8),
                          ),
                          color: vpColor.backgroundElevation0,
                        ), //
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  children: [
                                    if (item.investType ==
                                        TransactionTypeEnum.T)
                                      Assets.icons.icUserPlus.svg(
                                        colorFilter: ColorFilter.mode(
                                          vpColor.iconPrimary,
                                          BlendMode.srcIn,
                                        ),
                                        width: 20,
                                        height: 20,
                                      ),
                                    RichText(
                                      text: TextSpan(
                                        children: [
                                          TextSpan(
                                            text: item.totalOrder ?? '',
                                            style: vpTextStyle.headineBold6
                                                ?.copyWith(
                                                  color: vpColor.textPrimary,
                                                ),
                                          ),
                                          TextSpan(
                                            text: ' lệnh',
                                            style: vpTextStyle.subtitle14
                                                ?.copyWith(
                                                  color: vpColor.textSecondary,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                statusWidget(item),
                              ],
                            ),
                            //--
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Tổng GTGD dự kiến',
                                  style: vpTextStyle.body14?.copyWith(
                                    color: vpColor.textTertiary,
                                  ),
                                ),
                                Text(
                                  MoneyUtils.formatMoney(
                                    (item.orderAmount ?? 0).toDouble(),
                                  ),
                                  style: vpTextStyle.subtitle14?.copyWith(
                                    color: vpColor.textPrimary,
                                  ),
                                ),
                              ],
                            ),
                            //--
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Loại giao dịch',
                                  style: vpTextStyle.body14?.copyWith(
                                    color: vpColor.textTertiary,
                                  ),
                                ),
                                Text(
                                  item.investType?.sortTitle ?? '',
                                  style: vpTextStyle.subtitle14?.copyWith(
                                    color: vpColor.textPrimary,
                                  ),
                                ),
                              ],
                            ),

                            // ---
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Lệnh',
                                  style: vpTextStyle.body14?.copyWith(
                                    color: vpColor.textTertiary,
                                  ),
                                ),
                                Text(
                                  item.transactionKind?.sortTitle ?? '',
                                  style: vpTextStyle.subtitle14?.copyWith(
                                    color: item.transactionKind?.textColor,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }

  //
  Widget statusWidget(InvestmentOrderModel item) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(4)),
        color: item.commandStatus?.color,
      ),
      child: Center(
        child: Text(
          item.commandStatus?.title ?? '',
          style: vpTextStyle.captionSemiBold?.copyWith(
            color: item.commandStatus?.textColor,
          ),
        ),
      ),
    );
  }
}
