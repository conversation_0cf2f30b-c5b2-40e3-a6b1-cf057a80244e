import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_design_system/custom_widget/app_snackbar_utils.dart';
import 'package:vp_design_system/custom_widget/list_select/item_select.dart';
import 'package:vp_wealth/common/utils/base_cubit.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/enum/command_detail_status_enum.dart';
import 'package:vp_wealth/data/enum/transaction_type_enum.dart';
import 'package:vp_wealth/data/model/asset/copier_info.dart';
import 'package:vp_wealth/data/model/order/accept_command_data_model.dart';
import 'package:vp_wealth/data/model/order/investment_command_model.dart';
import 'package:vp_wealth/data/utils/loading_utils.dart';
import 'package:vp_wealth/domain/request/fetch_list_investment_command_request.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/lang/stock_localized_values.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/widget/confirm_remove_dialog.dart';
import 'package:vp_wealth/presentation/wealths/order/investment_command/filter/filtter_command_param.dart';
import 'package:vp_wealth/router/wealth_router.dart';

part 'investment_command_state.dart';

FilterCommandParam get filterBottomDefault => const FilterCommandParam(
  commandDetailStatus: CommandDetailStatusEnum.values,
);

class InvestmentCommandCubit extends BaseCubit<InvestmentCommandState> {
  InvestmentCommandCubit() : super(InvestmentCommandState());

  final int _pageSize = 10;
  int _currentPage = 0;
  int _totalPage = 1;

  FetchListInvestmentCommandRequest get params =>
      FetchListInvestmentCommandRequest(
        page: _currentPage,
        size: _pageSize,
        status: state.orderStatus,
        investmentType: state.investmentType,
        name: state.accountModel?.copierInfo.name,
        fromDate:
            state.listTime.isNullOrEmpty
                ? null
                : AppTimeUtils.format(
                  state.listTime.first,
                  AppTimeUtilsFormat.dateNormal,
                ),
        toDate:
            state.listTime.isNullOrEmpty
                ? null
                : AppTimeUtils.format(
                  state.listTime.last,
                  AppTimeUtilsFormat.dateNormal,
                ),
      );

  FilterCommandParam filterParam = filterBottomDefault;

  void initial() {
    _resetParam();
    onLoadData();
  }

  Future<List<InvestmentCommandModel>?> onGetData() async {
    try {
      final result = await GetIt.instance
          .get<WealthRepository>()
          .getListInvestmentCommand(params);
      if (result.isSuccess()) {
        _totalPage = (result.totalPages ?? 1).toInt();
        return result.data;
      } else {
        return [];
      }
    } catch (e) {
      showError(e);
      return null;
    }
  }

  // lay chi tiet lenh : hết hiệu lực, chưa xác nhận
  InvestmentCommandModel investCommand = InvestmentCommandModel();
  Future<void> getDetailCommand(int idCommand) async {
    emit(state.copyWith(loading: true));
    try {
      InvestmentCommandModel result = await GetIt.instance
          .get<WealthRepository>()
          .detailAcceptCommand(idCommand);
      if (result.id != null) {
        investCommand = result;
        emit(state.copyWith(detailSuccess: true, loading: false, error: false));
      } else {
        emit(state.copyWith(detailSuccess: false, loading: false, error: true));
      }
    } catch (e) {
      showError(e);
    }
  }

  // lay chi tiet lenh : đã xác nhận
  Future<List<AcceptCommandDataModel>?> getDetailAcceptCommand(
    String type,
    int copierId,
  ) async {
    emit(state.copyWith(loading: true));
    try {
      String status =
          (filterParam.commandDetailStatus
                          ?.map((e) => (e as CommandDetailStatusEnum).value)
                          .join(',') ??
                      '')
                  .contains('all')
              ? ''
              : filterParam.commandDetailStatus
                      ?.map((e) => (e as CommandDetailStatusEnum).value)
                      .join(',') ??
                  '';

      final result = await GetIt.instance
          .get<WealthRepository>()
          .getDetailAcceptCommand(type, copierId, status);

      if (result.isSuccess()) {
        return result.data;
      } else {
        return [];
      }
    } catch (e) {
      showError(e);
      return null;
    }
  }

  // huỷ 1 lệnh
  void onCancelCommand({
    required int copierId,
    required String transactionId,
  }) async {
    final context =
        GetIt.instance<NavigationService>().navigatorKey.currentContext;
    if (context == null) return;

    final isCancelOrder = await showConfirmRemoveDialog(context);
    if (isCancelOrder) {
      /// pop bottom sheet when user select cancel order button
      Navigator.pop(context);

      cancelOrder(copierId: copierId, transactionId: transactionId);
    }
  }

  void cancelOrder({
    required int copierId,
    required String transactionId,
  }) async {
    LoadingUtil.showLoading();
    try {
      final result = await GetIt.instance.get<WealthRepository>().cancelOrder(
        copierId: copierId,
        transactionId: transactionId,
      );
      LoadingUtil.hideLoading();
      onRefreshDetail();
      showMsgCancelOrder(result);
    } catch (e) {
      LoadingUtil.hideLoading();
      showError(e);
    }
  }

  Future showMsgCancelOrder(BEBaseResponse value) async {
    if (value.isSuccess()) {
      showNotice(true);
      emit(state.copyWith(deleteOrderSuccess: true));
    } else {
      showNotice(false, message: value.message);
    }
  }

  void showNotice(bool success, {String? message}) {
    final context =
        GetIt.instance<NavigationService>().navigatorKey.currentContext;
    if (context == null) return;
    showSnackBar(
      context,
      message ??
          (success
              ? getStockLang(StockKeyLang.deleteOrderSuccess)
              : 'Có lỗi xảy ra'),
      isSuccess: success,
    );
  }

  void onExpand(bool value) {
    emit(state.copyWith(isExpand: value));
  }

  void onChangeTime(List<dynamic>? listTime) {
    emit(
      state.copyWith(
        listTime:
            listTime!.first == null
                ? [
                  WealthUtils().calculateSixMonthRange(DateTime.now()).start,
                  WealthUtils().calculateSixMonthRange(DateTime.now()).end,
                ]
                : listTime,
      ),
    );
    _resetParam();
    onLoadData();
  }

  void onChangePlan(AccountModel? accountModel) {
    state.accountModel = accountModel;
    emit(state.copyWith(accountModel: accountModel));
    _resetParam();
    onLoadData();
  }

  void onChangeInvestmentType(TransactionTypeEnum? investment) {
    emit(state.copyWith(investmentType: investment));
    _resetParam();
    onLoadData();
  }

  void onChangeStatus(List<ItemSelect>? listStatus) {
    if (listStatus?.length == 3) {
      emit(state.copyWith(listStatus: [], orderStatus: ''));
      _resetParam();
      onLoadData();
      return;
    }
    if (listStatus != null) {
      String _orderStatus = '';
      List<int> listStt = [];
      for (var e in listStatus) {
        listStt.add(e.value);
      }
      _orderStatus = listStt.join(",");
      emit(state.copyWith(listStatus: listStatus, orderStatus: _orderStatus));
      _resetParam();
      onLoadData();
    }
  }

  void acceptCommand({
    required int id,
    required int copierId,
    required num totalCurrentValue,
    required VoidCallback onTransferMoney,
  }) async {
    final context =
        GetIt.instance<NavigationService>().navigatorKey.currentContext!;
    try {
      LoadingUtil.showLoading();
      final cashDetail = await GetIt.instance
          .get<WealthRepository>()
          .cashDetail(copierId: copierId.toString());
      num baldefovd = num.parse(cashDetail.baldefovd ?? '0');
      if (baldefovd < totalCurrentValue) {
        LoadingUtil.hideLoading();
        onTransferMoney();
        return;
      }
      final result = await GetIt.instance.get<WealthRepository>().acceptCommand(
        id: id,
      );
      LoadingUtil.hideLoading();
      if (result.isSuccess()) {
        context.push(WealthRouter.orderSuccessPage);
      }
    } catch (e) {
      LoadingUtil.hideLoading();
      if (e is ResponseError &&
          (e.status == smartOTPUnRegistered ||
              e.status == closeInputOTPErrorCode))
        return;
      final message = await getErrorMessage(e);
      context.push(WealthRouter.orderErrorPage, extra: message);
    }
  }

  void onLoadData() async {
    emit(state.copyWith(loading: true));
    final data = await onGetData();
    if (data != null) {
      emit(state.copyWith(dataList: [...data], loading: false, error: false));
    } else {
      emit(state.copyWith(loading: false, error: true));
    }
  }

  void onloadDetail(String type, int copierId) async {
    emit(state.copyWith(loading: true, orderType: type, commandId: copierId));
    final data = await getDetailAcceptCommand(type, copierId);
    if (data != null) {
      emit(
        state.copyWith(itemResults: [...data], loading: false, error: false),
      );
    } else {
      emit(state.copyWith(loading: false, error: true));
    }
  }

  Future<void> onLoadMore() async {
    if (_currentPage >= _totalPage) {
      return;
    }
    _currentPage++;
    emit(state.copyWith(loadingMore: true));
    final data = await onGetData();
    if (data != null) {
      emit(
        state.copyWith(dataList: [...state.dataList, ...data], error: false),
      );
    } else {
      emit(state.copyWith(error: true));
    }
  }

  Future<void> onRefresh() async {
    _currentPage = 0;
    onLoadData();
  }

  Future<void> onRefreshDetail() async {
    _currentPage = 0;
    // onloadDetail();
  }

  void _resetParam() {
    _currentPage = 0;
  }

  bool hasLoadMore() {
    return _totalPage > _currentPage + 1;
  }

  void onBottomFilterApply(FilterCommandParam param) {
    filterParam = param;
    onloadDetail(state.orderType ?? '', state.commandId ?? 0);
  }
}
