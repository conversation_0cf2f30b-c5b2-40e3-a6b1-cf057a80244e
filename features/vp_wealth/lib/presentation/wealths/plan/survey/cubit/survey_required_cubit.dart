import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_wealth/data/model/category/category_model.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/data/model/survey/survey_question_model.dart';
import 'package:vp_wealth/data/utils/loading_utils.dart';
import 'package:vp_wealth/domain/request/fetch_list_plan_request.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';

part 'survey_required_state.dart';

class SurveyRequiredCubit extends Cubit<SurveyRequiredState> {
  SurveyRequiredCubit() : super(SurveyRequiredState());

  void onGetData() async {
    try {
      emit(state.copyWith(loading: true));
      final data =
          await GetIt.instance.get<WealthRepository>().getSurveyQuestions();
      List<AnswerFromUser?> answers = List.filled(data.length, null);
      emit(state.copyWith(questions: data, answerFromUser: answers));
    } catch (e) {
      emit(state.copyWith(error: true));
      showError(e);
    } finally {
      emit(state.copyWith(loading: false));
    }
  }

  int get totalQuestion {
    return state.questions.length;
  }

  void onFinish(
    Function(CategoryModel? data, List<PlanModel> dataList, int point)
    onCallBack,
  ) async {
    try {
      LoadingUtil.showLoading();
      final dataAnswer = {
        "data":
            state.answerFromUser
                .map((e) => e?.toJson())
                .where((element) => element != null)
                .toList(),
      };
      final point = await GetIt.instance
          .get<WealthRepository>()
          .updateSurveyAnswer(dataAnswer);
      final category = await onGetSampleCategory();
      LoadingUtil.hideLoading();
      var dataList = await getListPlan();
      onCallBack(category, dataList, point);
    } catch (e) {
      LoadingUtil.hideLoading();
      showError(e);
    }
  }

  void onUpdateAnswerSelected(int index, AnswerFromUser? answer) {
    final List<AnswerFromUser?> newAnswer = List.from(state.answerFromUser);
    newAnswer[index] = answer;
    emit(state.copyWith(answerFromUser: newAnswer));
  }

  bool checkEnableButton(int index) {
    if (state.answerFromUser.isEmpty) {
      return false;
    }
    if (state.questions.isNotEmpty && state.questions.length > index) {
      if (state.questions[index].isRequired == true) {
        return state.answerFromUser[index] != null;
      } else {
        return true;
      }
    }
    return false;
  }

  Future<CategoryModel?> onGetSampleCategory() async {
    try {
      final data =
          await GetIt.instance.get<WealthRepository>().getSampleCategory();
      return data;
    } catch (e) {
      showError(e);
      return null;
    } finally {}
  }

  Future<List<PlanModel>> getListPlan() async {
    try {
      FetchListPlanRequest params = FetchListPlanRequest(page: 0, size: 10);
      final result = await GetIt.instance.get<WealthRepository>().getListPlan(
        params,
      );
      if (result.isSuccess()) {
        return result.data;
      }
    } catch (e) {
      showError(e);
    }
    return [];
  }
}
