import 'dart:async';

import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/widget/appbar/appbar_view.dart';
import 'package:vp_design_system/widget/button/vps_button.dart';
import 'package:vp_design_system/widget/scaffold/scaffold_view.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/enum/investment_status_enum.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/data/utils/padding_extends.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/stock_right/widget/error_widget.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/widget/listview_shimmer_view.dart';
import 'package:vp_wealth/presentation/wealths/plan/survey/cubit/survey_result_cubit.dart';
import 'package:vp_wealth/presentation/wealths/plan/survey/risk_level_enum.dart';
import 'package:vp_wealth/router/wealth_router.dart';

class SurveyResultPage extends StatefulWidget {
  const SurveyResultPage({super.key});

  static Widget newInstance() => BlocProvider(
    create: (context) => SurveyResultCubit(),
    child: const SurveyResultPage(),
  );

  @override
  State<SurveyResultPage> createState() => _SurveyResultPageState();
}

class _SurveyResultPageState extends State<SurveyResultPage> {
  double thinknessOfGaugeRange = 0.4;
  late SurveyResultCubit _cubit;
  @override
  void initState() {
    _cubit = context.read<SurveyResultCubit>();
    _cubit.onGetSampleCategory();
    super.initState();
  }

  RiskLevel mapRiskLevel(int point) {
    if (point >= 8 && point <= 12) {
      return RiskLevel.low;
    }
    if (point >= 13 && point <= 20) {
      return RiskLevel.medium;
    } else {
      return RiskLevel.hight;
    }
  }

  Widget _body() {
    return BlocBuilder<SurveyResultCubit, SurveyResultState>(
      bloc: _cubit,
      builder: (context, state) {
        if (state.isLoading) {
          return const ListViewShimmerView();
        } else if (state.categorySuggest == null) {
          return ErrorNetworkWidget(
            onPressed: () => _cubit.onGetSampleCategory(),
          );
        } else {
          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(width: 1, color: vpColor.strokeNormal),
                    ),
                  ),
                  height: 228,
                  child: Column(
                    children: [
                      Expanded(
                        child: chartResult(
                          state.categorySuggest?.totalPoint ?? 0,
                          state.categorySuggest?.maxPoint ?? 32,
                        ),
                      ),
                      Text(
                        'Điểm khẩu vị rủi ro',
                        style: vpTextStyle.captionRegular?.copyWith(
                          color: vpColor.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 2),
                      RichText(
                        text: TextSpan(
                          text: '${state.categorySuggest?.totalPoint}',
                          style: vpTextStyle.headineBold5?.copyWith(
                            color: vpColor.textPrimary,
                          ),
                          children: [
                            TextSpan(
                              text: '/${state.categorySuggest?.maxPoint}',
                              style: vpTextStyle.subtitle16?.copyWith(
                                color: vpColor.textTertiary,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      RichText(
                        text: TextSpan(
                          text: 'Khẩu vị rủi ro ',
                          style: vpTextStyle.subtitle16?.copyWith(
                            color: vpColor.textPrimary,
                          ),
                          children: [
                            TextSpan(
                              text: _cubit.getRiskLevel.getLevel(),
                              style: vpTextStyle.subtitle16?.copyWith(
                                color: _cubit.getRiskLevel.getLevelColor(),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Bạn phù hợp với chiến lược đầu tư ${_cubit.getRiskLevel.getMethod()}',
                        style: vpTextStyle.body14?.copyWith(
                          color: vpColor.textSecondary,
                        ),
                      ).paddingHorizontal(16),
                      const SizedBox(height: 12),
                    ],
                  ),
                ),
                _descriptionWidget(state.categorySuggest?.description ?? ''),
                _allocationRecommendations(),
              ],
            ),
          );
        }
      },
    );
  }

  Widget _descriptionWidget(String description) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: vpColor.backgroundElevation1,
        borderRadius: const BorderRadius.all(Radius.circular(8)),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF2A3346).withOpacity(0.04),
            spreadRadius: -8,
            blurRadius: 24,
            offset: const Offset(0, 24),
          ),
          BoxShadow(
            color: const Color(0xFF2A3346).withOpacity(0.04),
            spreadRadius: -2.5,
            blurRadius: 5,
            offset: const Offset(0, 5),
          ),
          BoxShadow(
            color: const Color(0xFF2A3346).withOpacity(0.04),
            spreadRadius: -1.5,
            blurRadius: 3,
            offset: const Offset(0, 3),
          ),
          BoxShadow(
            color: const Color(0xFF2A3346).withOpacity(0.04),
            spreadRadius: -1,
            blurRadius: 2,
            offset: const Offset(0, 2),
          ),
          BoxShadow(
            color: const Color(0xFF0E3F7E).withOpacity(0.06),
            spreadRadius: 1,
            blurRadius: 0,
            offset: const Offset(0, 0),
          ),
          BoxShadow(
            color: const Color(0xFF2A3346).withOpacity(0.03),
            spreadRadius: -0.5,
            blurRadius: 1,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Chiến lược đầu tư phù hợp',
                    style: vpTextStyle.captionRegular?.copyWith(
                      color: vpColor.textTertiary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _cubit.getRiskLevel.getMethod(),
                    style: vpTextStyle.subtitle16?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                  ),
                ],
              ),
              const Spacer(),
              SizedBox(
                width: 56,
                height: 56,
                child: Assets.images.imageIncreeResult.image(),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: vpTextStyle.captionMedium?.copyWith(
              color: vpColor.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _allocationRecommendations() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Khuyến nghị phân bổ',
            style: vpTextStyle.subtitle16?.copyWith(color: vpColor.textPrimary),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: SizedBox(
              height: 24,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(
                    width: 120,
                    height: 20,
                    child: LinearProgressIndicator(
                      value: _cubit.getRiskLevel.getBondPercent(),
                      backgroundColor: vpColor.backgroundElevationMinus1,
                      color: vpColor.piechartGreen4,
                      minHeight: 10,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      '${WealthStock.current.bondsfunds} ',
                      style: vpTextStyle.captionMedium?.copyWith(
                        color: vpColor.textPrimary,
                      ),
                    ),
                  ),
                  Text(
                    _cubit.getRiskLevel.getBondsFunds(),
                    style: vpTextStyle.captionSemiBold?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(
            height: 24,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SizedBox(
                  width: 120,
                  height: 20,
                  child: LinearProgressIndicator(
                    value: _cubit.getRiskLevel.getStockPercent(),
                    backgroundColor: vpColor.backgroundElevationMinus1,
                    color: vpColor.piechartYellow4,
                    minHeight: 10,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 2,
                  child: Text(
                    '${WealthStock.current.stocksStockFunds} ',
                    style: vpTextStyle.captionMedium?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                  ),
                ),
                Text(
                  _cubit.getRiskLevel.getStocksStockFunds(),
                  style: vpTextStyle.captionSemiBold?.copyWith(
                    color: vpColor.textPrimary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _bottomWidget() {
    return BlocBuilder<SurveyResultCubit, SurveyResultState>(
      builder: (context, state) {
        return Container(
          decoration: BoxDecoration(
            border: Border(
              top: BorderSide(color: vpColor.strokeNormal, width: 1),
            ),
          ),
          margin: EdgeInsets.only(
            bottom: MediaQuery.of(context).padding.bottom,
            top: 16,
          ),
          child:
              _checkStatusIsActive(state.listPlan)
                  ? VpsButton.primarySmall(
                    alignment: Alignment.center,
                    onPressed:
                        () => context.push(WealthRouter.surveyRequiredPage),
                    title: 'Thực hiện lại khảo sát',
                  ).paddingLTRB(16, 16, 16, 0)
                  : Row(
                    children: [
                      VpsButton.secondarySmall(
                        onPressed:
                            () => context.push(WealthRouter.surveyRequiredPage),
                        title: 'Khảo sát lại',
                        disabled: false,
                      ),
                      const SizedBox(width: SizeUtils.kSize8),
                      Expanded(
                        child: VpsButton.primarySmall(
                          alignment: Alignment.center,
                          onPressed: () {
                            if (state.listPlan.isNotEmpty) {
                              context.push(
                                WealthRouter.planPage,
                                extra: state.listPlan.first,
                              );
                            } else {
                              context.push(WealthRouter.investmentGoalsPage);
                            }
                          },
                          title:
                              (state.listPlan.isNotEmpty)
                                  ? 'Hoàn tất lập kế hoạch'
                                  : 'Bắt đầu lập kế hoạch',
                        ),
                      ),
                    ],
                  ).paddingLTRB(16, 16, 16, 0),
        );
      },
    );
  }

  Widget get _closePlan {
    return InkWell(
      onTap: () {
        if (_checkStatusIsActive(_cubit.getPlan)) {
          context.pop();
          Navigator.of(context).pop();
          return;
        }
        Timer(const Duration(milliseconds: 300), () {
          context.go('/mainTabbar');
        });
      },
      child: Icon(Icons.close, color: vpColor.textPrimary).paddingTop4(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: VPScaffold(
        appBar: VPAppBar.flows(title: 'Kết quả khảo sát', leading: _closePlan),
        backgroundColor: vpColor.backgroundElevation0,
        // centerTitle: false,
        body: SafeArea(
          child: Column(
            children: [
              Divider(
                color: vpColor.backgroundElevationMinus1,
                height: 8,
                thickness: 8,
              ),
              Expanded(child: _body()),
              _bottomWidget(),
            ],
          ),
        ),
      ),
    );
  }

  Widget chartResult(int point, int maxPoint) {
    return SfRadialGauge(
      axes: <RadialAxis>[
        RadialAxis(
          showLabels: false,
          showAxisLine: false,
          showTicks: false,
          minimum: 0,
          maximum: 360,
          startAngle: 180,
          endAngle: 0,
          canScaleToFit: true,
          interval: 1,
          canRotateLabels: true,
          ranges: <GaugeRange>[
            GaugeRange(
              startValue: 0,
              endValue: 120,
              color: vpColor.piechartGreen4,
              sizeUnit: GaugeSizeUnit.factor,
              startWidth: thinknessOfGaugeRange,
              endWidth: thinknessOfGaugeRange,
            ),
            GaugeRange(
              startValue: 125,
              endValue: 245,
              color: vpColor.piechartYellow4,
              sizeUnit: GaugeSizeUnit.factor,
              startWidth: thinknessOfGaugeRange,
              endWidth: thinknessOfGaugeRange,
            ),
            GaugeRange(
              startValue: 250,
              endValue: 360,
              color: vpColor.piechartRed4,
              sizeUnit: GaugeSizeUnit.factor,
              startWidth: thinknessOfGaugeRange,
              endWidth: thinknessOfGaugeRange,
            ),
          ],
          pointers: <GaugePointer>[
            NeedlePointer(
              needleColor: vpColor.strokeGray,
              value: _cubit.getRiskLevel.getPositionOfGauge(),
              needleLength: 0.55,
              needleStartWidth: 0.2,
              needleEndWidth: 3.5,
              knobStyle: const KnobStyle(
                sizeUnit: GaugeSizeUnit.factor,
                knobRadius: 0.05,
              ),
            ),
          ],
        ),
      ],
    );
  }

  bool _checkStatusIsActive(List<PlanModel> listPlan) {
    if (listPlan.isEmpty) return false;
    if (listPlan.first.investmentStatus == InvestmentStatusEnum.signed) {
      return true;
    }
    return false;
  }
}
