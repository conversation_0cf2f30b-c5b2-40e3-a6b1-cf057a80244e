import 'package:flutter/material.dart';
import 'package:vp_common/widget/vpbank_loading.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/data/utils/padding_extends.dart';
import 'package:vp_wealth/data/utils/wealth_constants.dart';
import 'package:vp_wealth/data/utils/wealth_notification_dialog.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/lang/stock_localized_values.dart';
import 'package:vp_wealth/presentation/wealths/plan/survey/cubit/survey_required_cubit.dart';
import 'package:vp_wealth/presentation/wealths/plan/survey/cubit/survey_required_index_cubit.dart';
import 'package:vp_wealth/presentation/wealths/plan/survey/widgets/survey_required_inprogess_percent.dart';
import 'package:vp_wealth/presentation/wealths/plan/survey/widgets/survey_required_item.dart';
import 'package:vp_wealth/presentation/wealths/widgets/none_widget.dart';
import 'package:vp_wealth/router/wealth_router.dart';

class SurveyRequiredPage extends StatefulWidget {
  const SurveyRequiredPage({super.key});

  @override
  State<SurveyRequiredPage> createState() => _SurveyRequiredPageState();
}

class _SurveyRequiredPageState extends State<SurveyRequiredPage> {
  final _cubit = SurveyRequiredCubit();
  final PageController _controller = PageController();
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => _cubit..onGetData()),
        BlocProvider(create: (_) => SurveyRequiredIndexCubit()),
      ],
      child: PopScope(
        canPop: false,
        child: VPScaffold(
          backgroundColor: vpColor.backgroundElevation0,
          appBar: VPAppBar.flows(title: 'Khảo sát', leading: _closePlan),
          body: Column(
            children: [
              Container(height: 8, color: vpColor.backgroundElevationMinus1),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  child: BlocBuilder<SurveyRequiredCubit, SurveyRequiredState>(
                    builder: (cxt, state) {
                      final state = _cubit.state;
                      if (state.loading) {
                        return const Center(child: VPBankLoading());
                      }
                      if (state.questions.isEmpty) {
                        return NoneWidget(
                          desc: getStockLang(StockKeyLang.noData),
                          button: true,
                          action: WealthStock.current.retry,
                          onPressed: _cubit.onGetData,
                        );
                      }
                      List<Widget> items = [];
                      for (
                        int index = 0;
                        index < state.questions.length;
                        index++
                      ) {
                        items.add(
                          Padding(
                            padding: EdgeInsets.only(
                              bottom:
                                  index != state.questions.length - 1 ? 16 : 0,
                            ),
                            child: SurveyRequiredItem(
                              model: state.questions[index],
                              index: index,
                            ),
                          ),
                        );
                      }
                      return PageView(
                        physics: const NeverScrollableScrollPhysics(),
                        children: items,
                        scrollDirection: Axis.horizontal,
                        controller: _controller,
                        onPageChanged: (_) {},
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
          bottomNavigationBar:
              BlocBuilder<SurveyRequiredCubit, SurveyRequiredState>(
                builder: ((context, state) {
                  return Padding(
                    padding: EdgeInsets.only(
                      bottom:
                          MediaQuery.of(context).padding.bottom +
                          (MediaQuery.of(context).padding.bottom > 0 ? 0 : 32),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        BlocBuilder<SurveyRequiredIndexCubit, int>(
                          builder: (ctx, index) {
                            return SurveyRequiredInProgressPercent(
                              segments: state.questions.length,
                              filledSegments: index,
                            );
                          },
                        ).paddingBottom16(),
                        _bottomWidget(),
                      ],
                    ),
                  );
                }),
              ),
        ),
      ),
    );
  }

  Widget _bottomWidget() {
    return BlocBuilder<SurveyRequiredIndexCubit, int>(
      builder: (cxt, state) {
        if (state == 0) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: BlocBuilder<SurveyRequiredCubit, SurveyRequiredState>(
              builder: (cxt, stateSurvey) {
                return BlocBuilder<SurveyRequiredIndexCubit, int>(
                  builder: (context, state) {
                    return VpsButton.primarySmall(
                      width: double.infinity,
                      alignment: Alignment.center,
                      disabled: !_cubit.checkEnableButton(state),
                      title:
                          state == (stateSurvey.questions.length - 1)
                              ? WealthStock.current.completed
                              : 'Câu tiếp theo',
                      onPressed: () {
                        if (state == (stateSurvey.questions.length - 1)) {
                          _cubit.onFinish((category, dataList, point) {
                            context.pushReplacement(WealthRouter.surveyResult);
                          });
                        } else {
                          context
                              .read<SurveyRequiredIndexCubit>()
                              .nextQuestion();
                          _controller.jumpToPage(state + 1);
                        }
                      },
                    );
                  },
                );
              },
            ),
          );
        } else {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [_previousQuestion(), _nextQuestion()],
            ),
          );
        }
      },
    );
  }

  Widget _nextQuestion() {
    return BlocBuilder<SurveyRequiredCubit, SurveyRequiredState>(
      builder: (cxt, stateSurvey) {
        return BlocBuilder<SurveyRequiredIndexCubit, int>(
          builder: (context, state) {
            return VpsButton.primarySmall(
              disabled: !_cubit.checkEnableButton(state),
              title:
                  state == (stateSurvey.questions.length - 1)
                      ? WealthStock.current.completed
                      : 'Câu tiếp theo',
              onPressed: () {
                if (state == (stateSurvey.questions.length - 1)) {
                  _cubit.onFinish((category, dataList, point) {
                    context.push(WealthRouter.surveyResult);
                  });
                } else {
                  context.read<SurveyRequiredIndexCubit>().nextQuestion();
                  _controller.jumpToPage(state + 1);
                }
              },
            );
          },
        );
      },
    );
  }

  Widget _previousQuestion() {
    return BlocBuilder<SurveyRequiredIndexCubit, int>(
      builder: (context, state) {
        return GestureDetector(
          onTap: () {
            context.read<SurveyRequiredIndexCubit>().previousQuestion();
            _controller.jumpToPage(state - 1);
          },
          child: Text(
            'Quay lại',
            style: vpTextStyle.subtitle14?.copyWith(color: vpColor.textBrand),
          ),
        );
      },
    );
  }

  void _onConfirmClose() {
    showNotifyDialog(
      barrierDismissible: false,
      context: context,
      title: WealthStock.current.cancelSurveyNew,
      titleStyle: vpTextStyle.headineBold6?.copyWith(
        color: vpColor.textPrimary,
      ),
      iconSize: 96,
      content: 'Bạn có chắc chắn muốn huỷ khảo sát?',
      image: Assets.icons.iconWarning.path,
      textButtonLeft: 'Đóng',
      colorButtonLeft: themeData.bgPopup,
      colorBorderButtonLeft: vpColor.strokeGray,
      textStyleLeft: vpTextStyle.subtitle14?.copyWith(
        color: vpColor.textPrimary,
      ),
      onPressedLeft: () {
        context.pop();
      },
      textButtonRight: WealthStock.current.cancelSurvey,
      colorButtonRight: vpColor.backgroundDanger,
      onPressedRight: () async {
        var surveyStatus =
            await GetIt.instance.get<WealthRepository>().getSurveyStatus();
        context.pop(context);
        if (surveyStatus == WealthConstants.NOT_HAVE_SURVEY) {
          context.pop(context);
          final result1 = await context.push(
            WealthRouter.tutorialPage,
            extra: {'isFirst': true, 'stepInit': 3},
          );
          if (result1 == true) {
            context.push(WealthRouter.surveyRequiredPage);
          }
          return;
        }
        Navigator.pop(context);
      },
    );
  }

  Widget get _closePlan {
    return InkWell(
      onTap: () => _onConfirmClose(),
      child: Icon(Icons.close, color: vpColor.iconPrimary).paddingTop4(),
    );
  }
}
