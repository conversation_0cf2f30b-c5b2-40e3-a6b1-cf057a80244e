import 'package:flutter/material.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:vp_common/extensions/list_extensions.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/app_refresh_view.dart';
import 'package:vp_design_system/custom_widget/app_snackbar_utils.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_design_system/widget/appbar/appbar_view.dart';
import 'package:vp_design_system/widget/scaffold/scaffold_view.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/enum/investment_frequency.dart';
import 'package:vp_wealth/data/enum/investment_status_enum.dart';
import 'package:vp_wealth/data/enum/target_fitler_enum.dart';
import 'package:vp_wealth/data/model/chart/chart_data_category_performance.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/data/utils/padding_extends.dart';
import 'package:vp_wealth/data/utils/wealth_notification_dialog.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/model/broker_model.dart';
import 'package:vp_wealth/presentation/place_order/utils/place_order_utils.dart';
import 'package:vp_wealth/presentation/wealths/intro/wealth_tutorial_page.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/detail_plan/action_contract_page.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/detail_plan/category_detail_widget.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/edit_plan/cubit/edit_wealth_plan_cubit.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/chart_invesment_category.dart';
import 'package:vp_wealth/router/wealth_router.dart';

class DetailWealthPlanPage extends StatefulWidget {
  final PlanModel model;

  const DetailWealthPlanPage({super.key, required this.model});

  @override
  State<DetailWealthPlanPage> createState() => _DetailWealthPlanPageState();
}

class _DetailWealthPlanPageState extends State<DetailWealthPlanPage> {
  PlanModel get _model => widget.model;
  final EditWealthPlanCubit _cubit = EditWealthPlanCubit();

  @override
  void initState() {
    super.initState();
    _cubit.initialData(_model);
  }

  @override
  Widget build(BuildContext context) {
    return VPScaffold(
      backgroundColor: vpColor.backgroundElevation0,
      appBar: _appBar(),
      body: SafeArea(
        child: PullToRefreshView(
          onRefresh: () async {
            _cubit.initialData(_model);
          },
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Divider(
                        color: vpColor.backgroundElevationMinus1,
                        height: 8,
                        thickness: 8,
                      ),
                      _allocationMoneyPlan,
                      _infoBasicPlan,
                      _listStockCategoryInvestment,
                    ],
                  ),
                ),
              ),
              Divider(
                color: vpColor.backgroundElevationMinus1,
                height: 2,
                thickness: 1,
              ),
              Container(
                width: double.infinity,
                color: vpColor.backgroundElevation0,
                height: 64,
                child: BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
                  bloc: _cubit,
                  builder: (context, state) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              WealthStock.current.percenCompleted,
                              style: vpTextStyle.captionRegular?.copyWith(
                                color: vpColor.textTertiary,
                              ),
                            ),
                            Text(
                              '${state.expectedPerformanceModel?.getPercentTarget ?? '0'}% của kế hoạch',
                              style: vpTextStyle.subtitle16?.copyWith(
                                color: vpColor.textPrimary,
                              ),
                            ),
                          ],
                        ).paddingTop4(),
                        const Spacer(),
                        GestureDetector(
                          onTap: () {
                            if (state.expectedPerformanceModel != null) {
                              context.push(
                                WealthRouter.categoryDetailWidget,
                                extra: CategoryDetailParams(
                                  expectedPerformanceModel:
                                      state.expectedPerformanceModel!,
                                ),
                              );
                            }
                          },
                          child: Text(
                            WealthStock.current.seeSample,
                            style: vpTextStyle.subtitle14?.copyWith(
                              color:
                                  state.plan != null
                                      ? enableSeeTarget(state.plan!)
                                          ? vpColor.textBrand
                                          : vpColor.textDisabled
                                      : vpColor.textDisabled,
                            ),
                          ),
                        ),
                        Assets.icons.icArrowRight
                            .svg(height: 20, width: 20)
                            .paddingTop4(),
                      ],
                    ).paddingHorizontal(16);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  bool enableSeeTarget(PlanModel plan) {
    if (plan.initialInvestment != null &&
        plan.investment != 0 &&
        plan.investmentTime != 0 &&
        plan.expectedProfit != 0) {
      return true;
    }
    return false;
  }

  Widget get _allocationMoneyPlan {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
          bloc: _cubit,
          buildWhen:
              (previous, current) =>
                  previous.isExpand != current.isExpand ||
                  previous.plan != current.plan,
          builder: (context, state) {
            return Padding(
              padding: const EdgeInsets.only(top: 16, left: 16, right: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    WealthStock.current.inforPlan,
                    style: vpTextStyle.subtitle16?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                  ).paddingBottom4(),
                  if (_model.investmentStatus == InvestmentStatusEnum.complete)
                    Row(
                      children: [
                        Text(
                          WealthStock.current.status,
                          style: vpTextStyle.body14?.copyWith(
                            color: vpColor.textTertiary,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          WealthStock.current.completed,
                          style: vpTextStyle.subtitle14?.copyWith(
                            color: vpColor.textPrimary,
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ],
                    ),
                  if (_model.investmentStatus != InvestmentStatusEnum.complete)
                    Row(
                      children: [
                        Text(
                          WealthStock.current.status,
                          style: vpTextStyle.body14?.copyWith(
                            color: vpColor.textTertiary,
                          ),
                        ),
                        const Spacer(),
                        BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
                          bloc: _cubit,
                          buildWhen:
                              (previous, current) =>
                                  previous.isActive != current.isActive,
                          builder: (context, state) {
                            return Text(
                              state.isActive
                                  ? WealthStock.current.active
                                  : WealthStock.current.stopWealth,
                              style: vpTextStyle.subtitle14?.copyWith(
                                color: vpColor.textPrimary,
                              ),
                              textAlign: TextAlign.left,
                            );
                          },
                        ),
                        const SizedBox(width: 8),
                        BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
                          bloc: _cubit,
                          buildWhen:
                              (previous, current) =>
                                  previous.isActive != current.isActive,
                          builder: (context, state) {
                            return FlutterSwitch(
                              width: 38.0,
                              height: 24.0,
                              toggleSize: 20.0,
                              value: state.isActive,
                              borderRadius: 99.0,
                              activeColor: vpColor.iconBrand,
                              inactiveColor: vpColor.iconTertiary,
                              padding: 2.0,
                              onToggle: (val) {
                                if (state.isActive) {
                                  _onStopInvestment(() {
                                    Navigator.pop(context);
                                    _cubit.onGetPreviewActionContract(
                                      val,
                                      () => context.push(
                                        WealthRouter.actionContractPage,
                                        extra: ActionContractArgument(
                                          plan: _model,
                                          cubit: _cubit,
                                          onChangeStatus:
                                              () => _cubit.onChangeStatus(
                                                val,
                                                () {
                                                  Navigator.pop(context);
                                                  _cubit.getPlanModel();
                                                  showSnackBar(
                                                    context,
                                                    'Dừng kế hoạch đầu tư thành công.',
                                                  );
                                                },
                                              ),
                                        ),
                                      ),
                                    );
                                  });
                                  return;
                                }
                                _onContinueInvestment(() {
                                  Navigator.pop(context);
                                  _cubit.onGetPreviewActionContract(
                                    val,
                                    () => context.push(
                                      WealthRouter.actionContractPage,
                                      extra: ActionContractArgument(
                                        plan: _model,
                                        cubit: _cubit,
                                        onChangeStatus:
                                            () => _cubit.onChangeStatus(
                                              val,
                                              () {
                                                Navigator.pop(context);
                                                _cubit.getPlanModel();
                                                showSnackBar(
                                                  context,
                                                  'Tiếp tục đầu tư thành công.',
                                                );
                                              },
                                            ),
                                      ),
                                    ),
                                  );
                                });
                              },
                            );
                          },
                        ),
                      ],
                    ),
                  _itemRowTextAllocationMoneyPlan(
                    title: 'Mục tiêu',
                    value: state.plan!.goal.getName(),
                  ),
                  if (state.isExpand) ...[
                    _itemRowTextAllocationMoneyPlan(
                      title: 'Tên kế hoạch',
                      value: state.plan!.name,
                    ),
                    _itemRowTextAllocationMoneyPlan(
                      title: 'Ngày tạo kế hoạch thành công',
                      value:
                          state.plan!.activeDate?.replaceAll('-', '/') ?? '--',
                    ),
                    FutureBuilder<BrokerModel?>(
                      future: state.plan!.infoBroker as Future<BrokerModel?>?,
                      builder: (cxt, snapshot) {
                        return _itemRowTextAllocationMoneyPlan(
                          title: 'Người giới thiệu',
                          value: snapshot.data?.fullName ?? '--',
                        );
                      },
                    ),
                  ],
                  kSpacingHeight8,
                  Align(
                    alignment: Alignment.center,
                    child: GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () => _cubit.onExpand(!state.isExpand),
                      child: RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text:
                                  "${state.isExpand ? 'Thu gọn' : 'Mở rộng'} ",
                              style: vpTextStyle.subtitle14?.copyWith(
                                color: vpColor.textBrand,
                              ),
                            ),
                            WidgetSpan(
                              alignment: PlaceholderAlignment.middle,
                              child: Padding(
                                padding: const EdgeInsets.only(
                                  top: SizeUtils.kSize2,
                                ),
                                child: Icon(
                                  state.isExpand
                                      ? Icons.keyboard_arrow_up
                                      : Icons.keyboard_arrow_down,
                                  size: SizeUtils.kSizeIcon16,
                                  color: vpColor.iconBrand,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
        Divider(height: 30, thickness: 1, color: vpColor.strokeNormal),
      ],
    );
  }

  Widget get _infoBasicPlan {
    return BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
      bloc: _cubit,
      buildWhen: (previous, current) => previous.plan != current.plan,
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    WealthStock.current.allocationPlan,
                    style: vpTextStyle.subtitle16?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                  ).paddingBottom4(),
                  _itemRowTextAllocationMoneyPlan(
                    title: WealthStock.current.amountInitialStart,
                    value: state.plan!.initialInvestment?.valueText ?? '--',
                  ),
                  _itemRowTextAllocationMoneyPlan(
                    title: WealthStock.current.periodicInvestmentAmount,
                    value: state.plan!.investment.valueText,
                  ),
                  _itemRowTextInfoBasic(
                    title: WealthStock.current.investmentTime,
                    value: '${state.plan!.investmentTime.toInt()} năm',
                  ),
                  _itemRowTextInfoBasic(
                    title: WealthStock.current.investmentFrequency,
                    value: state.plan!.investmentFrequency.title,
                  ),
                  _itemRowTextInfoBasic(
                    title: WealthStock.current.periodicInvestmentDate,
                    value:
                        (state.plan!.scheduleInvestment ?? '--')
                            .split('-')
                            .first,
                  ),
                  if (state.plan?.investmentStatus ==
                          InvestmentStatusEnum.signed ||
                      state.plan?.investmentStatus ==
                          InvestmentStatusEnum.draft ||
                      state.plan?.investmentStatus == InvestmentStatusEnum.all)
                    _itemRowTextAllocationMoneyPlan(
                      title: WealthStock.current.nextInvestmentPeriod,
                      value:
                          state.plan!.showNextInvestmentDate
                              ? state.plan!.nextInvestmentDate?.replaceAll(
                                    '-',
                                    '/',
                                  ) ??
                                  '--'
                              : '--',
                    ),
                  if (state.plan?.investmentStatus ==
                      InvestmentStatusEnum.complete)
                    _itemRowTextAllocationMoneyPlan(
                      title: 'Ngày hoàn thành kế hoạch',
                      value:
                          state.plan!.expireDate?.replaceAll('-', '/') ?? '--',
                    ),
                  if (state.plan?.investmentStatus == InvestmentStatusEnum.exit)
                    _itemRowTextAllocationMoneyPlan(
                      title: 'Ngày tạm dừng kế hoạch',
                      value:
                          state.plan!.stopInvestDate?.replaceAll('-', '/') ??
                          '--',
                    ),
                ],
              ),
            ),
            Divider(height: 30, thickness: 1, color: vpColor.strokeNormal),
          ],
        );
      },
    );
  }

  Widget get _listStockCategoryInvestment {
    return BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
      bloc: _cubit,
      buildWhen: (previous, current) => previous.plan != current.plan,
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                WealthStock.current.investmentCategory,
                style: vpTextStyle.subtitle16?.copyWith(
                  color: vpColor.textPrimary,
                ),
              ).paddingBottom8(),
              Row(
                children: [
                  Text(
                    WealthStock.current.expectedProfit,
                    style: vpTextStyle.body14?.copyWith(
                      color: vpColor.textTertiary,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${state.plan!.expectedProfit == 0 ? 'x' : state.plan!.expectedProfit.doubleFormatForDigits()}%/năm',
                    style: vpTextStyle.subtitle14?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                  ),
                ],
              ),
              ChartInvestmentCategory(
                chartData: state.plan!.chartData,
              ).paddingLeft8(),
            ],
          ),
        );
      },
    );
  }

  Widget get _categoryPerformance {
    return BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
      bloc: _cubit,
      buildWhen:
          (previous, current) =>
              previous.listChartDataCategoryPerformance !=
              current.listChartDataCategoryPerformance,
      builder: (context, state) {
        if (state.listChartDataCategoryPerformance.isNullOrEmpty) {
          return const SizedBox.shrink();
        }
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: SizeUtils.kSize16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              kSpacingHeight16,
              Text(
                WealthStock.current.expectedGrowth,
                style: vpTextStyle.body16?.copyWith(color: themeData.black),
              ),
              kSpacingHeight8,
              SfCartesianChart(
                plotAreaBorderWidth: 0,
                margin: EdgeInsets.zero,
                primaryYAxis: NumericAxis(
                  opposedPosition: true,
                  edgeLabelPlacement: EdgeLabelPlacement.hide,
                  numberFormat: NumberFormat.compactCurrency(locale: 'vi'),
                  labelFormat: '{value}',
                  majorGridLines: const MajorGridLines(width: 0),
                  majorTickLines: const MajorTickLines(width: 0),
                ),
                primaryXAxis: const CategoryAxis(
                  majorGridLines: MajorGridLines(width: 0),
                  majorTickLines: MajorTickLines(width: 0),
                ),
                tooltipBehavior: TooltipBehavior(
                  enable: true,
                  header: 'Tăng trưởng',
                ),
                series: <CartesianSeries>[
                  SplineSeries<ChartDataCategoryPerformance, num>(
                    dataSource: state.listChartDataCategoryPerformance,
                    splineType: SplineType.natural,
                    color: themeData.primary,
                    cardinalSplineTension: 0.9,
                    enableTooltip: true,
                    xValueMapper:
                        (ChartDataCategoryPerformance data, _) => data.date,
                    yValueMapper:
                        (ChartDataCategoryPerformance data, _) => data.value,
                  ),
                ],
              ),
              kSpacingHeight8,
            ],
          ),
        );
      },
    );
  }

  Widget _itemRowTextAllocationMoneyPlan({
    required String title,
    required String value,
    bool showIcon = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: SizeUtils.kSize4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: vpTextStyle.body14?.copyWith(color: vpColor.textTertiary),
          ),
          Row(
            children: [
              Text(
                value,
                textAlign: TextAlign.end,
                style: vpTextStyle.subtitle14?.copyWith(
                  color: vpColor.textPrimary,
                ),
              ),
              if (showIcon) ...[kSpacingHeight4, Assets.icons.icEdit.svg()],
            ],
          ),
        ],
      ),
    );
  }

  Widget _itemRowTextInfoBasic({required String title, required String value}) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: SizeUtils.kSize4),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: vpTextStyle.body14?.copyWith(
                  color: vpColor.textTertiary,
                ),
              ),
              Text(
                value,
                textAlign: TextAlign.end,
                style: vpTextStyle.subtitle14?.copyWith(
                  color: vpColor.textPrimary,
                ),
              ),
            ],
          ),
        ),
        // const DividerWidget(),
      ],
    );
  }

  void _onStopInvestment(VoidCallback onActionRight) {
    showNotifyDialog(
      barrierDismissible: false,
      context: context,
      title: WealthStock.current.doYouWantToStopInvesting,
      titleStyle: vpTextStyle.headineBold6?.copyWith(
        color: vpColor.textPrimary,
      ),
      iconSize: 96,
      contentWidget: Text(
        'Bằng việc dừng đầu tư, quý khách đồng ý rằng hệ thống sẽ tạm dừng nhắc lịch đầu tư theo kế hoạch. Tài sản của kế hoạch sẽ vẫn tiếp tục được duy trì trên tiểu khoản tích sản của khách hàng.',
        textAlign: TextAlign.center,
        style: vpTextStyle.body14?.copyWith(color: vpColor.textSecondary),
      ),
      imagePadding: const EdgeInsets.only(bottom: 10),
      image: Assets.icons.iconWarning.path,
      textButtonLeft: WealthStock.current.no,
      colorBorderButtonLeft: vpColor.strokeGray,
      colorButtonRight: vpColor.backgroundDanger,
      colorButtonLeft: themeData.bgPopup,
      textStyleLeft: vpTextStyle.subtitle14?.copyWith(
        color: vpColor.textSecondary,
      ),
      onPressedLeft: () => Navigator.pop(context),
      textButtonRight: WealthStock.current.yesStopInvesting,
      onPressedRight: onActionRight,
    );
  }

  void _onContinueInvestment(VoidCallback onActionRight) {
    showNotifyDialog(
      barrierDismissible: false,
      context: context,
      title: WealthStock.current.continueInvesting,
      titleStyle: vpTextStyle.headineBold6?.copyWith(
        color: vpColor.textPrimary,
      ),
      iconSize: 96,
      contentWidget: Text(
        'Bạn có chắc chắn tiếp tục đầu tư theo kế hoạch tích sản đã thiết lập?',
        textAlign: TextAlign.center,
        style: vpTextStyle.body14?.copyWith(color: vpColor.textSecondary),
      ),
      image: Assets.icons.icDone.path,
      imagePadding: const EdgeInsets.only(bottom: 10),
      textButtonLeft: WealthStock.current.no,
      colorBorderButtonLeft: vpColor.strokeGray,
      colorButtonRight: vpColor.backgroundBrand,
      textStyleLeft: vpTextStyle.subtitle14?.copyWith(
        color: vpColor.textSecondary,
      ),
      onPressedLeft: () => Navigator.pop(context),
      textButtonRight: WealthStock.current.yesContinueInvesting,
      onPressedRight: onActionRight,
    );
  }

  Widget get _editPlan {
    return BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
      bloc: _cubit,
      buildWhen: (previous, current) => previous.isActive != current.isActive,
      builder: (context, state) {
        if (!state.isActive) return const SizedBox.shrink();
        return InkWell(
          onTap:
              () =>
                  context.push(WealthRouter.editWealthPlanPage, extra: _cubit),
          child: Assets.icons.icEdit
              .svg(color: vpColor.iconPrimary)
              .paddingOnly(top: 12, right: 12, bottom: 12),
        );
      },
    );
  }

  VPAppBar _appBar() {
    return VPAppBar.flows(
      title: WealthStock.current.plan,
      leading: InkWell(
        onTap: () {
          if (_cubit.state.isChanged) {
            Navigator.pop(context, 'refresh');
            return;
          }
          Navigator.pop(context);
        },
        child:
            Icon(
              Icons.arrow_back_ios,
              size: 20,
              color: vpColor.iconPrimary,
            ).paddingTop4(),
      ),
      actions: [
        InkWell(
          onTap: () {
            context.push(WealthRouter.investmenApproachScreen);
          },
          child: Assets.icons.icMap.svg(color: vpColor.iconPrimary),
        ).paddingRight(12),
        _editPlan,
        InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const WealthTutorialPage(isFirst: false),
                fullscreenDialog: true,
              ),
            );
          },
          child: Assets.icons.icQuestionCircle.svg(color: vpColor.iconPrimary),
        ).paddingRight16(),
      ],
    );
  }
}
