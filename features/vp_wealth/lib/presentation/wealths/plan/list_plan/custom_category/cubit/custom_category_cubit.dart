import 'package:flutter/material.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/app_snackbar_utils.dart';
import 'package:vp_wealth/common/utils/color_utils.dart';
import 'package:vp_wealth/data/enum/symbol_type_enum.dart';
import 'package:vp_wealth/data/model/category/category_model.dart';
import 'package:vp_wealth/data/model/chart/chart_data_investment_category.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/place_order_bloc_map_import.dart';
import 'package:vp_wealth/presentation/place_order/utils/ext.dart';
import 'package:vp_wealth/presentation/wealths/plan/cubit/wealth_plan_cubit.dart';
import 'package:vp_wealth/wealth_data/wealth_data.dart';

part 'custom_category_state.dart';

class CustomCategoryCubit extends Cubit<CustomCategoryState> {
  CustomCategoryCubit() : super(const CustomCategoryState());

  void onShowSuggestMoney(bool isShow) {
    emit(state.copyWith(showSuggestMoney: isShow));
  }

  void onFetchListData(WealthPlanCubit wealthPlanCubit) {
    final isCustom =
        wealthPlanCubit.state.listStockCustomCategory.isNotEmpty &&
        wealthPlanCubit.state.investmentCategorySelected?.invPhilosophyType ==
            'CUSTOM';
    final listStock =
        isCustom
            ? wealthPlanCubit.state.listStockCustomCategory
            : (wealthPlanCubit
                    .state
                    .investmentCategorySelected
                    ?.masterPortfolios
                    ?.first
                    .listItemData ??
                []);
    final listChartData =
        listStock
            .asMap()
            .map(
              (index, e) => MapEntry(
                index,
                e.mapResponseToChartDataInvestmentCategory(index),
              ),
            )
            .values
            .cast<ChartDataInvestmentCategory>()
            .toList();

    emit(state.copyWith(listStock: listStock, listChartData: listChartData));
    updateInitialInvestmentAmount(
      wealthPlanCubit.textInitialInvestmentAmountController.text,
    );
    updatePeriodicInvestmentAmount(
      wealthPlanCubit.textMonthlyIncomeController.text,
    );
    if (listStock.isNotEmpty) {
      updateTotalRateAndExpectedProfit();
    }
  }

  void onAddItemListData(ItemData item, BuildContext context) {
    try {
      item.allocation = 0;
      if (state.listStock.any((data) => data.symbol == item.symbol)) {
        showSnackBar(
          context,
          'Mã chứng khoán đã tồn tại trong danh mục.',
          isSuccess: false,
        );
        return;
      }
      List<ItemData> listDraft = List<ItemData>.from(state.listStock);
      listDraft.add(item);
      List<ChartDataInvestmentCategory> listChartDataDraft =
          List<ChartDataInvestmentCategory>.from(state.listChartData);

      listChartDataDraft.add(
        ChartDataInvestmentCategory(
          item.symbol,
          item.allocation,
          Color(colors[listChartDataDraft.length % colors.length]),
        ),
      );
      showSnackBar(context, 'Đã thêm cổ phiếu vào danh mục.');

      emit(
        state.copyWith(listStock: listDraft, listChartData: listChartDataDraft),
      );
      updateTotalRateAndExpectedProfit(data: item);
    } catch (_) {}
  }

  void onRemoveItemListData(int index) {
    List<ItemData> listDraft = List<ItemData>.from(state.listStock);
    List<ChartDataInvestmentCategory> listChartDataDraft =
        List<ChartDataInvestmentCategory>.from(state.listChartData);
    listDraft.removeAt(index);
    listChartDataDraft.removeAt(index);
    showMessage(isSuccess: true, 'Xóa mã khỏi danh mục thành công.');
    emit(
      state.copyWith(listStock: listDraft, listChartData: listChartDataDraft),
    );
    updateTotalRateAndExpectedProfit();
  }

  void closeNoteMyCategory() {
    emit(state.copyWith(showNoteMyCategory: false));
  }

  void updateTotalRateAndExpectedProfit({ItemData? data}) {
    double totalCurrentRate = 0;
    double expectedProfit = 0;
    for (var item in state.listStock) {
      totalCurrentRate += item.allocation;
      expectedProfit +=
          item.allocation *
          (WealthData().allStocksWealth
                  .firstWhere(
                    (stock) => stock.symbol == item.symbol,
                    orElse:
                        () => ItemData(
                          symbol: '',
                          symbolType: SymbolTypeEnum.stock,
                          allocation: 0,
                        ),
                  )
                  .rateOfReturn ??
              0);
    }

    if (data != null) {
      final index = state.listChartData.indexWhere(
        (element) => element.x == data.symbol,
      );
      final updatedList = List<ChartDataInvestmentCategory>.from(
        state.listChartData,
      );
      updatedList[index] = ChartDataInvestmentCategory(
        data.symbol,
        data.allocation,
        Color(colors[index]),
      );
      emit(state.copyWith(listChartData: updatedList));
    }

    emit(
      state.copyWith(
        totalRate: 100 - totalCurrentRate,
        expectedProfit:
            totalCurrentRate == 0 ? 0 : expectedProfit / totalCurrentRate,
      ),
    );
  }

  void updateInitialInvestmentAmount(String value) {
    emit(state.copyWith(initialInvestmentAmount: value.volume));
  }

  void updatePeriodicInvestmentAmount(String value) {
    emit(
      state.copyWith(
        periodicInvestmentAmount: value.volume,
        showErrorPeriodAmount: value.isEmpty,
      ),
    );
  }

  void onFetchListStockCategorySuggest() async {
    var dataListValidate = WealthData().allStocksWealth.where((e) => e.priority == true).toList();
    emit(state.copyWith(listStockCategorySuggest: dataListValidate));
  }

  bool validateSaveChange() {
    String errorMessage = '';

    bool isValidInitialInvestment() => state.initialInvestmentAmount > 0;
    for (var item in state.listStock) {
      if ((isValidInitialInvestment() &&
              (state.initialInvestmentAmount * (item.allocation / 100)) <
                  100000) ||
          (state.periodicInvestmentAmount * (item.allocation / 100)) < 100000) {
        errorMessage =
            'Số tiền đầu tư tối thiểu cho mỗi mã cổ phiếu là 100,000 đồng';
        break;
      }
      if (item.allocation == 0) {
        errorMessage = 'Tỷ trong mã chứng khoán phải lớn hơn 0';
        break;
      }
    }
    //state.initialInvestmentAmount == 0 ||
    if (state.periodicInvestmentAmount == 0) {
      errorMessage = 'Số tiền đầu tư định kỳ phải lớn hơn 0';
    }

    if (state.totalRate != 0) {
      errorMessage = 'Tổng tỷ trọng danh mục phải bằng 100%';
    }

    if (errorMessage.isEmpty) return false;
    showErrorMessage(errorMessage);

    return true;
  }

  bool get isShowChartStock => state.listChartData.any((item) => item.y > 0);
}
