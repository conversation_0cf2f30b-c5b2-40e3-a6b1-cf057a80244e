import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/model/chart/chart_data_investment_category.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/data/model/stock_detail_entity.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/data/utils/loading_utils.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/data/utils/padding_extends.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/generated/assets.gen.dart' as wealthAssets;
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/lang/stock_localized_values.dart';
import 'package:vp_wealth/presentation/place_order/utils/place_order_utils.dart';
import 'package:vp_wealth/presentation/wealths/plan/custom_category/cubit/custom_category_validate_cubit.dart';
import 'package:vp_wealth/presentation/wealths/plan/custom_category/item_investment_stock.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/edit_plan/cubit/edit_wealth_plan_cubit.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/edit_plan/cusom_category/cubit/custom_category_cubit.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/edit_plan/stock_changed_properties.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/widget/icon_status_price_widget.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/chart_invesment_category.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/remove_wealth_dialog.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/text_suggest_widget.dart';
import 'package:vp_wealth/presentation/wealths/search/bloc/search_stock_bloc.dart';
import 'package:vp_wealth/presentation/wealths/search/search_stocks_page.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/presentation/bloc/details_stock_bloc.dart';
import 'package:vp_wealth/presentation/wealths/widgets/bottom_sheet/bottom_sheet_investment_guide_line.dart';
import 'package:vp_wealth/presentation/wealths/widgets/utils/right_suffix_money.dart';

class CustomInvestmentCategoryPage extends StatefulWidget {
  final EditWealthPlanCubit cubit;

  const CustomInvestmentCategoryPage({super.key, required this.cubit});

  @override
  State<CustomInvestmentCategoryPage> createState() =>
      _CustomInvestmentCategoryPageState();
}

class _CustomInvestmentCategoryPageState
    extends State<CustomInvestmentCategoryPage> {
  final CustomCategoryCubit _cubit = CustomCategoryCubit();

  EditWealthPlanCubit get _editWealthPlanCubit => widget.cubit;

  late final DetailsStockBloc _bloc = DetailsStockBloc();
  final _cubitValidate = CustomCategoryValidateCubit();
  FocusNode periodicAmountFocusnode = FocusNode();
  final FocusNode _initialInvestmentAmountFocusNode = FocusNode();
  late TextEditingController textPeriodicInvestmentAmountController;
  @override
  void initState() {
    super.initState();
    _cubit.onFetchListData(_editWealthPlanCubit);
    _cubit.onFetchListStockCategorySuggest();
    periodicAmountFocusnode.addListener(() {
      _cubit.onShowSuggestPeriodicInvestmentMoney(
        periodicAmountFocusnode.hasFocus,
      );
    });
    textPeriodicInvestmentAmountController = TextEditingController(
      text: _editWealthPlanCubit.getPeriodicInvestmentAmount.valueTextMoney,
    );
    textPeriodicInvestmentAmountController.selection = TextSelection.collapsed(
      offset: textPeriodicInvestmentAmountController.text.length - 4,
    );
  }

  @override
  void dispose() {
    periodicAmountFocusnode.dispose();
    _initialInvestmentAmountFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => _cubitValidate,
      child: GestureDetector(
        onTap: () => AppKeyboardUtils.dismissKeyboard(),
        child: VPScaffold(
          backgroundColor: vpColor.backgroundElevation0,
          appBar: VPAppBar.flows(title: 'Tuỳ chỉnh danh mục', leading: _close),
          body: SafeArea(
            child: Column(
              children: [
                Divider(
                  color: vpColor.backgroundElevationMinus1,
                  height: 8,
                  thickness: 8,
                ),
                Expanded(
                  child: ListView(
                    children: [
                      kSpacingHeight8,
                      _periodicInvestmentAmount,
                      SizedBox(
                        height: 2,
                        width: double.infinity,
                        child: ColoredBox(color: themeData.buttonDisableBg),
                      ).paddingBottom12(),
                      _listStockCategory,
                      _listStockCategorySuggest,
                    ],
                  ),
                ),
              ],
            ),
          ),
          bottomNavigationBar: SafeArea(
            bottom: MediaQuery.of(context).viewInsets.bottom <= 0,
            child: Wrap(
              children: [
                Visibility(
                  visible: !(MediaQuery.of(context).viewInsets.bottom > 0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _divider,
                      BlocBuilder<CustomCategoryCubit, CustomCategoryState>(
                        bloc: _cubit,
                        buildWhen:
                            (previous, current) =>
                                previous.listStock != current.listStock,
                        builder: (context, state) {
                          return VpsButton.primarySmall(
                            disabled: state.listStock.isEmpty,
                            title: WealthStock.current.buttonSaveChange,
                            alignment: Alignment.center,
                            width: double.infinity,
                            onPressed: () async {
                              if (_cubit.state.listStock.isEmpty) return;
                              if (_editWealthPlanCubit
                                  .originalPlanModel
                                  .showEditInitialInvestStatus) {
                                if (_cubit
                                    .validateSaveChangeHaveInitialAmount()) {
                                  _cubitValidate.validate();
                                  return;
                                }
                              } else {
                                if (_cubit.validateSaveChange()) {
                                  _cubitValidate.validate();
                                  return;
                                }
                              }
                              LoadingUtil.showLoading();
                              // AppData().allStocksWealth.clear();
                              await GetIt.instance
                                  .get<WealthRepository>()
                                  .getListStockOfWealth()
                                  .then((value) {
                                    LoadingUtil.hideLoading();
                                    context.pop({
                                      'listStock': _cubit.state.listStock,
                                      'amount':
                                          _cubit.state.periodicInvestmentAmount,
                                    });
                                    showSnackBar(
                                      context,
                                      'Đã cập nhật bản kế hoạch theo danh mục của tôi.',
                                      isSuccess: true,
                                    );
                                  });
                            },
                          ).paddingLTRB(16, 8, 16, 16);
                        },
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom,
                  ),
                  child: _buildSuggestPeriodicInvestmentMoney(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget get _periodicInvestmentAmount {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              WealthStock.current.periodicInvestmentAmount,
              style: vpTextStyle.body14?.copyWith(color: vpColor.textTertiary),
            ).paddingRight8(),
            SizedBox(
              width: 170,
              child: Stack(
                children: [
                  BlocBuilder<CustomCategoryCubit, CustomCategoryState>(
                    bloc: _cubit,
                    buildWhen:
                        (previous, current) =>
                            previous.showErrorEditPeriodicInvestmentAmount !=
                            current.showErrorEditPeriodicInvestmentAmount,
                    builder: (context, state) {
                      return VPTextField.small(
                        enableInteractiveSelection: false,
                        focusNode: periodicAmountFocusnode,
                        keyboardType: TextInputType.number,
                        inputType:
                            state.showErrorEditPeriodicInvestmentAmount
                                ? InputType.error
                                : InputType.rest,
                        inputFormatters: [
                          CurrencyInputFormatter(),
                          LengthLimitingTextInputFormatter(13),
                        ],
                        autofocus: false,
                        maxLength: 13,
                        hintText: WealthStock.current.hintTextEnterAmount,
                        onChanged: (value) {
                          if (value.isEmpty) {
                            _cubit.updatePeriodicInvestmentAmount(
                              textPeriodicInvestmentAmountController.text,
                            );
                            return;
                          }

                          if (textPeriodicInvestmentAmountController.text
                              .endsWith(',000')) {
                            _cubit.updatePeriodicInvestmentAmount(
                              textPeriodicInvestmentAmountController.text,
                            );
                            return;
                          }

                          // final newText = value + ',000';
                          textPeriodicInvestmentAmountController
                              .value = TextEditingValue(text: value + ',000');
                          textPeriodicInvestmentAmountController
                              .selection = TextSelection.collapsed(
                            offset:
                                textPeriodicInvestmentAmountController
                                    .text
                                    .length -
                                4,
                          );
                          _cubit.updatePeriodicInvestmentAmount(value);
                        },
                        controller: textPeriodicInvestmentAmountController,
                      );
                    },
                  ),
                  const RightSuffixMoney(),
                ],
              ),
            ),
          ],
        ),
        Container(
          decoration: BoxDecoration(
            color: vpColor.backgroundAccentYellow.withOpacity(0.16),
            borderRadius: const BorderRadius.all(Radius.circular(8)),
          ),
          child: Row(
            children: [
              wealthAssets.Assets.icons.icQuestion1.svg().paddingRight8(),
              kSpacingWidth4,
              Flexible(
                child: Text(
                  WealthStock.current.noteAboutPeriodicInvestmentAmount,
                  style: vpTextStyle.captionMedium?.copyWith(
                    color: vpColor.textPrimary,
                  ),
                ),
              ),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () => showBottomSheetRateInvestmentGuideline(context),
                child: Text(
                  WealthStock.current.seeNow,
                  style: vpTextStyle.captionMedium?.copyWith(
                    color: vpColor.textAccentYellow,
                  ),
                ),
              ).paddingLeft12(),
            ],
          ).paddingSymmetric(horizontal: 12, vertical: 8),
        ).paddingVertical(12),
      ],
    ).paddingLTRB(16, 16, 16, 0);
  }

  Widget get _listStockCategory {
    return BlocBuilder<CustomCategoryCubit, CustomCategoryState>(
      bloc: _cubit,
      buildWhen:
          (previous, current) =>
              previous.listStock != current.listStock ||
              previous.listChartData != current.listChartData,
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: SizeUtils.kSize16,
              ),
              child: Text(
                WealthStock.current.investmentCategory,
                style: vpTextStyle.subtitle16?.copyWith(
                  color: vpColor.textPrimary,
                ),
              ),
            ),
            kSpacingHeight8,
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: SizeUtils.kSize16,
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  BlocBuilder<CustomCategoryCubit, CustomCategoryState>(
                    bloc: _cubit,
                    buildWhen:
                        (previous, current) =>
                            previous.totalRate != current.totalRate,
                    builder: (context, state) {
                      return RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: WealthStock.current.rateInvestmentAmount,
                              style: vpTextStyle.captionMedium?.copyWith(
                                color: vpColor.textSecondary,
                              ),
                            ),
                            TextSpan(
                              text: ' ${100 - state.totalRate.toInt()}% ',
                              style: vpTextStyle.subtitle14?.copyWith(
                                color:
                                    state.totalRate != 0
                                        ? vpColor.textAccentRed
                                        : vpColor.textBrand,
                              ),
                            ),
                            TextSpan(
                              text: '/100%',
                              style: vpTextStyle.subtitle14?.copyWith(
                                color: vpColor.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                  if (state.listStock.isNotEmpty) _buttonAddStock,
                ],
              ),
            ).paddingBottom12(),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: SizeUtils.kSize16,
              ),
              child: BlocBuilder<CustomCategoryCubit, CustomCategoryState>(
                bloc: _cubit,
                buildWhen:
                    (previous, current) =>
                        previous.totalRate != current.totalRate,
                builder: (context, state) {
                  return (state.totalRate != 0 && state.listStock.isNotEmpty)
                      ? RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: 'Tổng tỷ lệ phân bổ phải đạt 100%',
                              style: vpTextStyle.body14?.copyWith(
                                color: vpColor.textAccentRed,
                              ),
                            ),
                          ],
                        ),
                      )
                      : const SizedBox();
                },
              ),
            ),
            state.listStock.isEmpty
                ? _listStockCategoryEmpty
                : _listStock(state.listStock),
            if (_cubit.isShowChartStock) _chartStock(state.listStock),
          ],
        );
      },
    );
  }

  Widget _chartStock(List<ItemData> listStock) {
    final List<ChartDataInvestmentCategory> chartData =
        listStock
            .asMap()
            .map(
              (index, e) => MapEntry(
                index,
                e.mapResponseToChartDataInvestmentCategory(index),
              ),
            )
            .values
            .cast<ChartDataInvestmentCategory>()
            .toList();
    return BlocBuilder<CustomCategoryCubit, CustomCategoryState>(
      buildWhen:
          (previous, current) =>
              previous.listStock != current.listStock ||
              previous.expectedProfit != current.expectedProfit ||
              previous.listChartData != current.listChartData,
      bloc: _cubit,
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                WealthStock.current.investmentCategory,
                style: vpTextStyle.subtitle16?.copyWith(
                  color: vpColor.textPrimary,
                ),
              ).paddingBottom8(),
              Row(
                children: [
                  Text(
                    WealthStock.current.expectedProfit,
                    style: vpTextStyle.body14?.copyWith(
                      color: vpColor.textTertiary,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${state.expectedProfit == 0 ? 'x' : state.expectedProfit.doubleFormatForDigits()}%/năm',
                    style: vpTextStyle.subtitle14?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                  ),
                ],
              ),
              ChartInvestmentCategory(chartData: chartData).paddingLeft8(),
            ],
          ),
        );
      },
    );
  }

  Widget get _listStockCategoryEmpty {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: SizeUtils.kSize16),
      child: SizedBox(
        width: double.infinity,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            kSpacingHeight16,
            wealthAssets.Assets.icons.emptyBox.svg(),
            kSpacingHeight16,
            Text(
              'Bạn chưa có cổ phiếu nào trong danh mục',
              style: vpTextStyle.body14?.copyWith(color: vpColor.textPrimary),
            ),
            _buttonAddStock,
            kSpacingHeight16,
          ],
        ),
      ),
    );
  }

  Widget _listStock(List<ItemData> listStock) {
    return BlocBuilder<CustomCategoryCubit, CustomCategoryState>(
      bloc: _cubit,
      buildWhen:
          (previous, current) =>
              previous.periodicInvestmentAmount !=
              current.periodicInvestmentAmount,
      builder: (context, state) {
        return Column(
          children:
              listStock
                  .asMap()
                  .map(
                    (index, itemStock) => MapEntry(
                      index,
                      ItemInvestmentStock(
                        data: itemStock,
                        periodicAmount:
                            textPeriodicInvestmentAmountController.text.volume,
                        onRemove: () => _cubit.onRemoveItemListData(index),
                        onUpdateTotalRate:
                            (data) => _cubit.updateTotalRateAndExpectedProfit(
                              data: data,
                            ),
                      ),
                    ),
                  )
                  .values
                  .toList(),
        );
      },
    );
  }

  Widget get _listStockCategorySuggest {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: SizeUtils.kSize16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Danh mục VPBankS khuyến nghị',
            style: vpTextStyle.headine5?.copyWith(color: themeData.black),
          ),
          kSpacingHeight4,
          Text(
            'Danh mục cổ phiếu VPBankS đánh giá và chọn lọc theo các ngành trên thị trường',
            style: vpTextStyle.body14?.copyWith(color: themeData.gray700),
          ),
          kSpacingHeight12,
          SizedBox(
            height: 66,
            child: BlocBuilder<CustomCategoryCubit, CustomCategoryState>(
              bloc: _cubit,
              buildWhen:
                  (previous, current) =>
                      previous.listStockCategorySuggest !=
                      current.listStockCategorySuggest,
              builder: (context, state) {
                return ListView.builder(
                  itemCount: state.listStockCategorySuggest.length,
                  scrollDirection: Axis.horizontal,
                  shrinkWrap: true,
                  itemBuilder: (cxt, index) {
                    ItemData item = state.listStockCategorySuggest[index];
                    return _itemStockSuggest(item: item);
                  },
                );
              },
            ),
          ),
          kSpacingHeight24,
        ],
      ),
    );
  }

  Widget _itemStockSuggest({required ItemData item}) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () => _showBottomSheetInfoStock(item: item),
      child: Container(
        width: SizeUtils.kSize120,
        height: SizeUtils.kSize72,
        margin: const EdgeInsets.only(right: SizeUtils.kSize10),
        decoration: BoxDecoration(
          border: Border(left: BorderSide(color: themeData.primary, width: 2)),
          image: DecorationImage(
            alignment: Alignment.centerRight,
            image: AssetImage(wealthAssets.Assets.images.bgItemStock.path),
          ),
          borderRadius: BorderRadius.circular(SizeUtils.kRadius8),
        ),
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(color: themeData.divider),
            borderRadius: BorderRadius.circular(SizeUtils.kRadius8),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: SizeUtils.kSize12,
            vertical: SizeUtils.kSize4,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                item.symbol,
                style: vpTextStyle.body16?.copyWith(color: themeData.black),
              ),
              Text(
                item.business ?? '',
                style: vpTextStyle.captionRegular?.copyWith(
                  color: themeData.gray900,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget get _buttonAddStock {
    return Container(
      height: 32,
      padding: const EdgeInsets.symmetric(horizontal: SizeUtils.kSize8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(SizeUtils.kRadius8),
        border: Border.all(color: vpColor.strokeGray),
      ),
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: _showBottomSheetSearchStock,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            wealthAssets.Assets.icons.icPlus
                .svg(color: vpColor.iconPrimary)
                .paddingRight8(),
            Text(
              'Thêm cổ phiếu',
              style: vpTextStyle.subtitle14?.copyWith(
                color: vpColor.textPrimary,
              ),
            ).paddingOnly(left: 4, bottom: 4),
          ],
        ),
      ),
    ).paddingTop12();
  }

  Future<void> _showBottomSheetSearchStock() async {
    await showModalBottomSheet(
      barrierColor: themeData.overlayBottomSheet,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return AppBottomSheet(
          isFullSize: true,
          initialChildSize: 0.85 * MediaQuery.of(context).size.height,
          child: BlocProvider<SearchStockBloc>(
            create: (context) => SearchStockBloc(),
            child: SearchStocksPage(
              dataList: _cubit.state.listStock,
              onAddList: (item) => _cubit.onAddItemListData(item, context),
            ),
          ),
        );
      },
    );
  }

  Future<void> _showBottomSheetInfoStock({required ItemData item}) async {
    _bloc.init(item.symbol);
    await showModalBottomSheet(
      barrierColor: themeData.overlayBottomSheet,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return StreamBuilder<StockDetailEntity>(
          stream: _bloc.streamStockItem,
          builder: (context, snapshot) {
            if (snapshot.data == null || snapshot.hasError) {
              return const SizedBox();
            }
            StockDetailEntity data = snapshot.data!;

            return AppBottomSheet(
              isFullSize: true,
              padding: const EdgeInsets.only(top: SizeUtils.kSize16),
              initialChildSize: 0.35 * MediaQuery.of(context).size.height,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: SizeUtils.kSize24,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: item.symbol,
                                style: vpTextStyle.headine5?.copyWith(
                                  color: themeData.black,
                                ),
                              ),
                              TextSpan(
                                text: "  ${data.exchange}",
                                style: vpTextStyle.body14?.copyWith(
                                  color: themeData.gray500,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Text(
                          data.companyName,
                          style: vpTextStyle.body14?.copyWith(
                            color: themeData.gray700,
                          ),
                        ),
                        kSpacingHeight8,
                        _buildPriceSection(),
                        kSpacingHeight18,
                      ],
                    ),
                  ),
                  _divider,
                  kSpacingHeight12,
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: SizeUtils.kSize24,
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: VpsButton.primarySmall(
                            title: 'Thêm vào danh mục',
                            onPressed: () {
                              Navigator.pop(context);
                              _cubit.onAddItemListData(item, context);
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildPriceSection({bool isOnlyPrice = false}) {
    return StreamBuilder<StockDetailEntity>(
      stream: _bloc.streamStockItem,
      builder: (context, snapshot) {
        if (snapshot.data == null || snapshot.hasError) {
          return const SizedBox();
        }

        StockDetailEntity data = snapshot.data!;

        final totalVolume = snapshot.data?.totalVolume ?? 0;

        final totalTrading = snapshot.data?.totalTradingValue ?? 0;

        final style = vpTextStyle.captionRegular?.copyWith(
          color: themeData.gray500,
        );

        return Row(
          children: [
            IntrinsicWidth(
              child: Row(
                children: [
                  StreamBuilder<dynamic>(
                    stream:
                        _bloc
                            .stockChangedProperties[StockChangedProperties
                                .lastPrice]!
                            .stream,
                    initialData: false,
                    builder: (context, snapshot) {
                      final price = data.currentPrice;

                      final content = price.getPriceFormatted(
                        currency: '',
                        convertToThousand: true,
                      );

                      final styleText = (isOnlyPrice
                              ? vpTextStyle.headine5
                              : vpTextStyle.headine6)
                          ?.copyWith(color: data.colorByPrice);

                      return Container(
                        color:
                            snapshot.data!
                                ? data.colorByPrice.withOpacity(0.16)
                                : null,
                        child: Text(content, style: styleText),
                      );
                    },
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      left: SizeUtils.kSize4,
                      top: isOnlyPrice ? 5 : SizeUtils.kSize16,
                    ),
                    child: StreamBuilder<bool>(
                      stream:
                          _bloc
                              .stockChangedProperties[StockChangedProperties
                                  .changeValue]!
                              .stream,
                      initialData: false,
                      builder: (context, snapshot) {
                        final changeValue = data.changeValue ?? 0;

                        final changePercent = data.changePercent ?? 0;

                        final content =
                            '${(changeValue.abs()).getPriceFormatted(currency: '', convertToThousand: true)} (${changePercent.getValuePercentAbs()})';
                        final textStyle = (isOnlyPrice
                                ? vpTextStyle.captionRegular
                                : vpTextStyle.subtitle14)
                            ?.copyWith(color: data.colorByPrice);
                        return Container(
                          color:
                              snapshot.data!
                                  ? data.colorByPrice.withOpacity(0.16)
                                  : null,
                          child: IntrinsicWidth(
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                IconStatusUpDownWidget(
                                  value: changeValue,
                                  iconSize: 12,
                                  color: data.colorByPrice,
                                ),
                                const SizedBox(width: SizeUtils.kSize4),
                                Expanded(
                                  child: Text(content, style: textStyle),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            kSpacingWidth8,
            isOnlyPrice
                ? const SizedBox.shrink()
                : Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(getStockLang(StockKeyLang.tt), style: style),
                          Text(
                            getTotalTrading(totalVolume),
                            style: Theme.of(context).textTheme.bodyLarge,
                          ),
                        ],
                      ),
                      kSpacingWidth24,
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(getStockLang(StockKeyLang.tv), style: style),
                          Text(
                            getTotalTrading(totalTrading),
                            style: Theme.of(context).textTheme.bodyLarge,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
          ],
        );
      },
    );
  }

  String getTotalTrading(double totalTrading) {
    if (totalTrading == 0.0) {
      return 0.toString();
    }
    if (totalTrading >= 1000000000) {
      return '${(totalTrading / 1000000000).toFormat2()} ${getStockLang(StockKeyLang.bil)}';
    } else if (totalTrading >= 1000000) {
      return '${(totalTrading / 1000000).toFormat2()} ${getStockLang(StockKeyLang.mil)}';
    } else if (totalTrading >= 1000) {
      return '${(totalTrading / 1000).toFormat2()} ${getStockLang(StockKeyLang.k)}';
    } else {
      return totalTrading.toString();
    }
  }

  Widget get _divider {
    return SizedBox(
      height: 2,
      width: double.infinity,
      child: ColoredBox(color: themeData.buttonDisableBg),
    );
  }

  Widget _buildSuggestPeriodicInvestmentMoney() {
    return BlocBuilder<CustomCategoryCubit, CustomCategoryState>(
      bloc: _cubit,
      buildWhen:
          (previous, current) =>
              previous.showSuggestPeriodicInvestmentMoney !=
              current.showSuggestPeriodicInvestmentMoney,
      builder: (context, state) {
        if (!state.showSuggestPeriodicInvestmentMoney) {
          return const SizedBox();
        }
        return WidgetTextSuggest(
          isNeedUnFocus: false,
          textController: textPeriodicInvestmentAmountController,
          onTap: () {
            _cubit.updatePeriodicInvestmentAmount(
              textPeriodicInvestmentAmountController.text,
            );
            AppKeyboardUtils.dismissKeyboard();
          },
        );
      },
    );
  }

  Widget get _close {
    return InkWell(
      onTap: () => dialogClose(context),
      child: Icon(Icons.close, color: vpColor.textPrimary).paddingTop4(),
    );
  }
}
