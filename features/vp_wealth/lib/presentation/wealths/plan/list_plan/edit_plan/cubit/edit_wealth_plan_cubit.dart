import 'dart:typed_data';

import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/data/enum/investment_frequency.dart';
import 'package:vp_wealth/data/enum/investment_status_enum.dart';
import 'package:vp_wealth/data/enum/symbol_type_enum.dart';
import 'package:vp_wealth/data/model/chart/chart_data_category_performance.dart';
import 'package:vp_wealth/data/model/chart/chart_data_investment_category.dart';
import 'package:vp_wealth/data/model/contract/wealth_contract_model.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/data/utils/loading_utils.dart';
import 'package:vp_wealth/domain/entity/item_expected_performance.dart';
import 'package:vp_wealth/domain/request/fetch_list_plan_request.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/generated/assets.gen.dart' as wealthAssets;
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/place_order_bloc_map_import.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/remove_wealth_dialog.dart';
import 'package:vp_wealth/wealth_data/wealth_data.dart';

part 'edit_wealth_plan_state.dart';

class EditWealthPlanCubit extends Cubit<EditWealthPlanState> {
  EditWealthPlanCubit() : super(const EditWealthPlanState());

  final TextEditingController textEditNamePlanController =
      TextEditingController();
  final TextEditingController textInvestmentTimeController =
      TextEditingController();
  final TextEditingController textPeriodicInvestmentDayController =
      TextEditingController();
  final TextEditingController textPeriodicInvestmentAmountController =
      TextEditingController();
  final TextEditingController textInitialInvestmentAmountController =
      TextEditingController();

  late PlanModel originalPlanModel;

  void initialData(PlanModel model) async {
    emit(state.copyWith(plan: model, planChange: model));
    onFetchDetailPlan(model.id ?? -1);
    onGetStatus(model);
    onCalculateExpectedPerformance(model);
  }

  void getPlanModel() async {
    FetchListPlanRequest params = FetchListPlanRequest(
      investmentFrequency: state.plan?.investmentFrequency,
      investmentStatus: state.plan?.investmentStatus,
      page: 0,
      size: 12,
    );
    try {
      final result = await GetIt.instance.get<WealthRepository>().getListPlan(
        params,
      );
      if (result.isSuccess()) {
        emit(
          state.copyWith(
            plan: result.data.first,
            planChange: result.data.first,
          ),
        );
      }
    } catch (e) {
      showError(e);
    }
    return null;
  }

  void prepareDateForEdit(PlanModel model) {
    try {
      originalPlanModel = model;
      onFetchDetailPlan(model.id ?? -1);
      textPeriodicInvestmentAmountController.text = model.investment.valueText
          .replaceAll(' đ', '');
      textPeriodicInvestmentAmountController
          .selection = TextSelection.collapsed(
        offset: textPeriodicInvestmentAmountController.text.length - 4,
      );
      textInitialInvestmentAmountController.text =
          model.initialInvestment?.valueText.replaceAll(' đ', '') ?? '';
      textInitialInvestmentAmountController.selection = TextSelection.collapsed(
        offset:
            textInitialInvestmentAmountController.text.length > 4
                ? textInitialInvestmentAmountController.text.length - 4
                : 0,
      );

      textEditNamePlanController.text = model.name;
      textInvestmentTimeController.text = model.investmentTime.toString();
      DateTime _scheduleInvestment = model.convertScheduleDateToDateTime;
      emit(
        state.copyWith(
          showErrorEditInitialInvestmentAmount: false,
          showErrorEditPeriodicInvestmentAmount: false,
          showErrorEditInvestmentTime: false,
          initialInvestmentAmount: model.initialInvestment,
          periodicInvestmentAmount: model.investment,
          valueInvestmentTime: model.investmentTime,
          namePlan: model.name,
          investmentFrequency: model.investmentFrequency,
          scheduleInvestment: _scheduleInvestment,
          listDataStock: model.itemList,
          showErrorEditPeriodicInvestmentDay: false,
        ),
      );
      updateListMonthOfQuarterly(_scheduleInvestment);
      onCalculateExpectedPerformance(model);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  Future<void> onFetchDetailPlan(int copierId) async {
    try {
      final data = await GetIt.instance.get<WealthRepository>().getDetailPlan(
        copierId,
      );
      emit(state.copyWith(plan: data, expectedProfit: 0));
      updateExpectedProfit(data.itemList);
    } catch (e) {
      showError(e);
    } finally {}
  }

  void updateCustomCategory(List<ItemData> dataList) {
    emit(state.copyWith(listDataStock: []));
    List<ItemData> dataListUpdate = List<ItemData>.from(dataList);
    updateExpectedProfit(dataListUpdate);
    emit(state.copyWith(listDataStock: dataListUpdate));
  }

  void updateExpectedProfit(List<ItemData> listStock) {
    double totalCurrentRate = 0;
    for (var item in listStock) {
      totalCurrentRate += item.allocation;
    }
    emit(
      state.copyWith(
        expectedProfit: totalCurrentRate == 0 ? 0 : expectedProfit(listStock),
      ),
    );
  }

  List<ChartDataInvestmentCategory> chartData(List<ItemData> dataList) {
    return dataList
        .asMap()
        .map(
          (index, e) => MapEntry(
            index,
            e.mapResponseToChartDataInvestmentCategory(index),
          ),
        )
        .values
        .cast<ChartDataInvestmentCategory>()
        .toList();
  }

  num expectedProfit(List<ItemData> dataList) {
    return dataList.fold<double>(
          0,
          (previousValue, element) =>
              previousValue +
              element.allocation *
                  (WealthData().allStocksWealth
                          .firstWhere(
                            (stock) => stock.symbol == element.symbol,
                            orElse:
                                () => ItemData(
                                  symbol: '',
                                  symbolType: SymbolTypeEnum.stock,
                                  allocation: 0,
                                ),
                          )
                          .rateOfReturn ??
                      0),
        ) /
        dataList.fold<double>(0, (total, item) => total + item.allocation);
  }

  void updateListMonthOfQuarterly(DateTime date) {
    if (state.investmentFrequency == InvestmentFrequency.quarterly) {
      int currentMonth = date.month;
      List<List<int>> allNumbers = [
        [1, 4, 7, 10],
        [2, 5, 8, 11],
        [3, 6, 9, 12],
      ];
      final matchingNumbers = allNumbers.firstWhere(
        (numbers) => numbers.contains(currentMonth),
      );
      emit(state.copyWith(listMonthOfQuarterly: matchingNumbers));
    }
  }

  void updateListDraftMonthOfQuarterly(DateTime date) {
    if (state.investmentFrequency == InvestmentFrequency.quarterly) {
      int currentMonth = date.month;
      List<List<int>> allNumbers = [
        [1, 4, 7, 10],
        [2, 5, 8, 11],
        [3, 6, 9, 12],
      ];
      final matchingNumbers = allNumbers.firstWhere(
        (numbers) => numbers.contains(currentMonth),
      );
      emit(state.copyWith(listDraftMonthOfQuarterly: matchingNumbers));
    }
  }

  void editInitialInvestmentAmount(num amount) {
    emit(state.copyWith(initialInvestmentAmount: amount));
  }

  void editNamePlan(String name) {
    emit(state.copyWith(namePlan: name));
  }

  void editInvestmentTime(num time) {
    emit(state.copyWith(valueInvestmentTime: time));
  }

  void resetInvestmentTime() {
    textInvestmentTimeController.text = state.valueInvestmentTime.toString();
  }

  void editPeriodicInvestmentDay(DateTime? day) {
    updateListMonthOfQuarterly(day!);
    emit(state.copyWith(periodicInvestmentDay: day));
  }

  void editScheduleInvestment(DateTime? day) {
    updateListMonthOfQuarterly(day!);
    emit(state.copyWith(scheduleInvestment: day));
  }

  void editPeriodicDraftInvestmentDay(DateTime? day) {
    updateListDraftMonthOfQuarterly(day ?? DateTime.now());
    emit(state.copyWith(periodicDraftInvestmentDay: day));
  }

  void editPeriodicInvestmentAmount(num amount) {
    textPeriodicInvestmentAmountController.selection = TextSelection.collapsed(
      offset: textPeriodicInvestmentAmountController.text.length - 4,
    );
    emit(
      state.copyWith(
        periodicInvestmentAmount: amount,
        showErrorEditPeriodicInvestmentAmount: false,
      ),
    );
  }

  void resetPeriodicInvestmentAmount() {
    textPeriodicInvestmentAmountController.text = state
        .periodicInvestmentAmount
        .valueText
        .replaceAll(' đ', '');
    textPeriodicInvestmentAmountController.selection = TextSelection.collapsed(
      offset: textPeriodicInvestmentAmountController.text.length - 4,
    );
  }

  void editInvestmentFrequency(InvestmentFrequency frequency) {
    emit(state.copyWith(investmentFrequency: frequency));
    updateListMonthOfQuarterly(state.scheduleInvestment!);
  }

  void onValidateEditNamePlan(String value) {
    if (value.isNotEmpty) {
      emit(state.copyWith(showErrorEditNamePlan: false));
      return;
    }
    emit(state.copyWith(showErrorEditNamePlan: true));
  }

  bool onValidateEditInvestmentTime(String value) {
    if (value.isEmpty) {
      emit(state.copyWith(showErrorEditInvestmentTime: true, isActive: false));
      return false;
    }
    if (int.parse(value) > 100) {
      textInvestmentTimeController.text = '';
      emit(
        state.copyWith(
          valueInvestmentTime: 0,
          showErrorEditInvestmentTime: true,
          isActive: false,
        ),
      );
      return false;
    }
    emit(state.copyWith(showErrorEditInvestmentTime: false, isActive: true));
    return true;
  }

  bool onValidateEditPeriodicInvestmentDay(int value) {
    if (value > 0 && value <= 31) {
      emit(state.copyWith(showErrorEditPeriodicInvestmentDay: false));
      return true;
    }
    emit(state.copyWith(showErrorEditPeriodicInvestmentDay: true));
    return false;
  }

  bool onValidateEditPeriodicInvestmentAmount(String value) {
    if (value.isNotEmpty) {
      emit(
        state.copyWith(
          showErrorEditPeriodicInvestmentAmount: false,
          isActive: true,
        ),
      );
      return true;
    }
    emit(
      state.copyWith(
        showErrorEditPeriodicInvestmentAmount: true,
        isActive: false,
      ),
    );
    return false;
  }

  bool validateInvestMentAmountCreateDraftPlan(PlanModel plan) {
    for (ItemData e in plan.itemList) {
      double investedAmount = (e.allocation / 100) * (plan.investment);
      if (investedAmount < 100000) {
        return false;
      }
    }
    return true;
  }

  bool validateInitialInvestmentCreateDraftPlan(PlanModel plan) {
    if ((plan.initialInvestment ?? 0) != 0) {
      for (ItemData e in plan.itemList) {
        double investedAmount =
            (e.allocation / 100) * (plan.initialInvestment ?? 0);
        if (investedAmount < 100000) {
          return false;
        }
      }
    }
    return true;
  }

  bool onValidateEditInitialInvestmentAmount(String value) {
    /// because don't required
    emit(state.copyWith(showErrorEditInitialInvestmentAmount: false));
    return true;
  }

  void onGetStatus(PlanModel model) {
    if (model.investmentStatus == InvestmentStatusEnum.signed) {
      emit(state.copyWith(isActive: true));
      return;
    }
    emit(state.copyWith(isActive: false));
  }

  void onChangeStatus(bool value, VoidCallback onSuccess) async {
    int copierId = state.plan!.id!;
    try {
      LoadingUtil.showLoading();
      final result =
          await (value
              ? GetIt.instance.get<WealthRepository>().activeInvestment(
                copierId: copierId,
              )
              : GetIt.instance.get<WealthRepository>().stopInvestment(
                copierId: copierId,
              ));
      if (result.isSuccess()) {
        emit(state.copyWith(isActive: value, isChanged: true));
        await onFetchDetailPlan(state.planChange!.id ?? -1);
        onSuccess();
        LoadingUtil.hideLoading();
      }
    } catch (e) {
      showError(e);
      LoadingUtil.hideLoading();
    } finally {}
  }

  void onGetPreviewActionContract(bool value, Function() onSuccess) async {
    int copierId = state.plan!.id!;
    try {
      final requestID =
          await (value
              ? GetIt.instance
                  .get<WealthRepository>()
                  .previewContractContinueInvestment(copierId)
              : GetIt.instance
                  .get<WealthRepository>()
                  .previewContractStopInvestment(copierId));
      LoadingUtil.hideLoading();
      emit(state.copyWith(requestIdActionContract: requestID));
      onSuccess();
    } catch (e) {
      showError(e);
    } finally {}
  }

  void onExpand(bool value) {
    emit(state.copyWith(isExpand: value));
  }

  String buildInvestmentSchedule() {
    return state.listMonthOfQuarterly.map((month) => ' $month').join(', ');
  }

  String buildDraftInvestmentSchedule() {
    return state.listDraftMonthOfQuarterly.map((month) => ' $month').join(', ');
  }

  void checkChangePlan(
    Function(PlanModel plan, bool checkSuccess) onContinue,
  ) async {
    try {
      LoadingUtil.showLoading();
      PlanModel plan = state.planChange!.copyWith(
        initialInvestment: state.initialInvestmentAmount,
        investment: state.periodicInvestmentAmount,
        investmentFrequency: state.investmentFrequency,
        startDate: state.plan?.startDate,
        nextInvestmentDate: state.scheduleInvestment!.formatToDdMmYyyy(),
        scheduleInvestment: AppTimeUtils.format(
          state.scheduleInvestment,
          AppTimeUtilsFormat.dateConditionOrder,
        ),
        investmentTime: state.valueInvestmentTime,
        itemList: state.listDataStock,
      );
      bool errorPeriodicAmount = !validateInvestMentAmountCreateDraftPlan(plan);
      if (errorPeriodicAmount) {
        emit(
          state.copyWith(
            showErrorEditPeriodicInvestmentAmount: errorPeriodicAmount,
          ),
        );
        LoadingUtil.hideLoading();
        showDialogWarningAmount(
          GetIt.instance<NavigationService>().navigatorKey.currentContext!,
          () {
            // unFocus();
            onContinue(plan, false);
          },
        );
        return;
      }
      final result = await GetIt.instance
          .get<WealthRepository>()
          .checkChangePlan(plan);
      final requestID = await GetIt.instance
          .get<WealthRepository>()
          .previewModifiedContract(plan);
      LoadingUtil.hideLoading();
      if (result.isSuccess()) {
        emit(state.copyWith(requestIdContract: requestID, planChange: plan));
        onContinue(plan, true);
      } else {
        showError(BEBaseResponse(code: result.code, message: result.message));
      }
    } catch (e) {
      LoadingUtil.hideLoading();
      if (e is ResponseError) {
        showSnackBar(
          GetIt.instance<NavigationService>().navigatorKey.currentContext!,
          e.message ?? '',
          isSuccess: false,
        );
      }
    } finally {
      LoadingUtil.hideLoading();
    }
  }

  void updateChangePlan(Function() onContinue) async {
    try {
      LoadingUtil.showLoading();

      final result = await GetIt.instance
          .get<WealthRepository>()
          .updateChangePlan(state.planChange!);
      LoadingUtil.hideLoading();
      if (result.isSuccess()) {
        await onFetchDetailPlan(state.planChange!.id ?? -1);
        emit(state.copyWith(isChanged: true));
        onContinue();
      }
    } catch (e) {
      LoadingUtil.hideLoading();
      showError(e);
    } finally {
      LoadingUtil.hideLoading();
    }
  }

  void onGetLinkContract(String requestID, {bool isContract = true}) async {
    try {
      WealthContractModel result = await GetIt.instance
          .get<WealthRepository>()
          .getContractByRequestId(requestID);
      if (!result.presignedUrl.isNullOrEmpty) {
        emit(
          state.copyWith(
            contract: isContract ? result : null,
            actionContract: isContract ? null : result,
          ),
        );
      }
    } catch (e) {
      showError(e);
    }
  }

  Future<Uint8List?> getContractData(String linkContract) async {
    try {
      if (DownloadUtils().checkUrlValid(linkContract)) {
        Uint8List? data = await DownloadUtils().getUrlContent(linkContract);
        if (data != null) {
          return data;
        }
      }
      return Future.error('');
    } catch (e) {
      return Future.error('');
    }
  }

  void downloadContract(
    BuildContext context,
    WealthContractModel? contract,
  ) async {
    final randomId = AppHelper().genXRequestID();
    final String tag = 'download_contract_$randomId';
    EasyDebounce.debounce(tag, const Duration(milliseconds: 1000), () async {
      try {
        showSnackBar(
          context,
          WealthStock.current.downloading,
          asset: wealthAssets.Assets.icons.icDownload.path,
          colorAsset: themeData.black,
        );
        final String defaultFileName = (contract?.fileName ?? '').replaceAll(
          '.pdf',
          '',
        );
        final downloadResult = await DownloadFileManager.instance.downloadFile(
          fileName: defaultFileName,
          extension: FileExtension.pdf,
          url: contract?.presignedUrl ?? '',
        );

        showSnackBar(
          context,
          downloadResult != null
              ? WealthStock.current.downloadSuccess
              : WealthStock.current.downloadFail,
          asset:
              downloadResult != null
                  ? Assets.icons.icSuccess.path
                  : wealthAssets.Assets.icons.icFail.path,
        );
      } catch (e) {
        showSnackBar(
          context,
          WealthStock.current.downloadFail,
          asset: wealthAssets.Assets.icons.icFail.path,
        );
      }
    });
  }

  void onCalculateExpectedPerformance(PlanModel plan) async {
    try {
      if (plan.investment <= 0 || plan.expectedProfit <= 0) {
        emit(state.copyWith(listChartDataCategoryPerformance: []));
        return;
      }
      final ExpectedPerformanceModel? result = await GetIt.instance
          .get<WealthRepository>()
          .calculateExpectedPerformance(
            plan.copyWith(
              startDate: plan.startDate.replaceAll('-', '/'),
              rateOfReturn: plan.expectedProfit,
            ),
          );
      List<ChartDataCategoryPerformance> listExpectedPerformance = [];
      if (result != null) {
        for (var element in result.futureValues) {
          listExpectedPerformance.add(
            element.mapResponseToChartDataCategoryPerformance(),
          );
        }
      }
      emit(
        state.copyWith(
          expectedPerformanceModel: result,
          listChartDataCategoryPerformance: listExpectedPerformance,
        ),
      );
    } catch (e) {
      showError(e);
    } finally {}
  }

  void acceptActionContract(bool? value) {
    emit(state.copyWith(acceptActionContract: value));
  }

  void acceptContract(bool? value) {
    emit(state.copyWith(acceptContract: value));
  }

  void onShowSuggestInitialInvestmentMoney(bool isShow) {
    emit(state.copyWith(showSuggestInitialInvestmentMoney: isShow));
  }

  void onShowSuggestPeriodicInvestmentMoney(bool isShow) {
    emit(state.copyWith(showSuggestPeriodicInvestmentMoney: isShow));
  }

  num get getPeriodicInvestmentAmount => state.periodicInvestmentAmount;

  num get getInitialInvestmentAmount => state.initialInvestmentAmount;
}
