import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/data/utils/padding_extends.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/place_order/utils/ext.dart';
import 'package:vp_wealth/presentation/wealths/order/investment_command/button_transparent.dart';
import 'package:vp_wealth/presentation/wealths/plan/custom_category/cubit/custom_category_validate_cubit.dart';
import 'package:vp_wealth/presentation/wealths/plan/custom_category/wealth_text_field.dart';

import '../../widgets/utils/text_field_current_update.dart';

class ItemInvestmentStock extends StatefulWidget {
  final ItemData data;
  final double? initialAmount;
  final double periodicAmount;
  final VoidCallback onRemove;
  final Function(ItemData data) onUpdateTotalRate;

  const ItemInvestmentStock({
    super.key,
    required this.data,
    this.initialAmount,
    required this.periodicAmount,
    required this.onRemove,
    required this.onUpdateTotalRate,
  });

  @override
  State<ItemInvestmentStock> createState() => _ItemInvestmentStockState();
}

class _ItemInvestmentStockState extends State<ItemInvestmentStock> {
  TextEditingController textEditRateController = TextEditingController();
  bool showError = false;

  ItemData get _data => widget.data;

  double? get _initialAmount => widget.initialAmount;

  double get _periodicAmount => widget.periodicAmount;

  final FocusNode _itemAllocationFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    textEditRateController.text =
        _data.allocation == 0 ? '' : _data.allocation.toInt().toString();

    _itemAllocationFocusNode.addListener(() {
      if ((_initialAmount != null &&
              _initialAmount != 0 &&
              ((_initialAmount! * (textEditRateController.text.volume / 100))
                      .toInt() <
                  100000) ||
          (((_periodicAmount * (textEditRateController.text.volume / 100))
                  .toInt() <
              100000)))) {
        showError = true;
        showMessage('Số tiền đầu tư tối thiểu cho mỗi mã tối thiểu 100.000đ');
      } else {
        showError = false;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    textEditRateController.text =
        _data.allocation == 0 ? '' : _data.allocation.toInt().toString();
    return BlocListener<CustomCategoryValidateCubit, int>(
      listener: (context, state) {
        if ((_initialAmount != null &&
                _initialAmount != 0 &&
                ((_initialAmount! * (textEditRateController.text.volume / 100))
                        .toInt() <
                    100000) ||
            (((_periodicAmount * (textEditRateController.text.volume / 100))
                    .toInt() <
                100000)))) {
          setState(() {
            showError = true;
          });
        } else {
          setState(() {
            showError = false;
          });
        }
      },
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: SizeUtils.kSize16),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: RichText(
                        text: TextSpan(
                          children: [
                            WidgetSpan(
                              alignment: PlaceholderAlignment.middle,
                              child: GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                onTap: widget.onRemove,
                                child: Assets.icons.icDeleteStock.svg(),
                              ),
                            ),
                            TextSpan(
                              text: '  ${_data.symbol}',
                              style: vpTextStyle.body16?.copyWith(
                                color: vpColor.textPrimary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Text(
                      'Tỷ lệ',
                      style: vpTextStyle.captionSemiBold?.copyWith(
                        color: vpColor.textTertiary,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      decoration: BoxDecoration(
                        color: vpColor.backgroundElevation0,
                        borderRadius: const BorderRadius.all(
                          Radius.circular(8),
                        ),
                        border: Border.all(color: vpColor.strokeNormal),
                      ),
                      child: RichText(
                        text: TextSpan(
                          children: [
                            // TextSpan(
                            //   text: WealthStock.current.rate,
                            //   style: vpTextStyle.captionSemiBold?.copyWith(
                            //     color: vpColor.textTertiary,
                            //   ),
                            // ),
                            WidgetSpan(
                              alignment: PlaceholderAlignment.middle,
                              child: SizedBox(
                                width: 96,
                                height: 36,
                                child: AppTextFieldCurrentUpdate(
                                  isMoney: false,
                                  enable: true,
                                  suffixIcon: Text(
                                    '%',
                                    style: vpTextStyle.body14?.copyWith(
                                      color: vpColor.textTertiary,
                                    ),
                                  ).paddingOnly(top: 4, left: 20, right: 4),
                                  typeInput: TextInputType.number,
                                  hintText: '',
                                  maxLength: 3,
                                  paddingBottom: 12,
                                  autofocus: false,
                                  focusNode: _itemAllocationFocusNode,
                                  textController: textEditRateController,
                                  isSuccess: false,
                                  messsage:
                                      textEditRateController.text.isEmpty
                                          ? 'Lỗi'
                                          : ((textEditRateController
                                                  .text
                                                  .isNotEmpty &&
                                              (((_periodicAmount *
                                                              (textEditRateController
                                                                      .text
                                                                      .volume /
                                                                  100))
                                                          .toInt() <
                                                      100000) ||
                                                  ((_initialAmount != null &&
                                                      _initialAmount != 0 &&
                                                      ((_initialAmount! *
                                                                  (textEditRateController
                                                                          .text
                                                                          .volume /
                                                                      100))
                                                              .toInt() <
                                                          100000))))))
                                          ? 'Lỗi'
                                          : null,
                                  onChanged: (value) {
                                    if (value == '0' ||
                                        (value.isEmpty) ||
                                        (value.length > 2 && value != '100')) {
                                      // if (!showError) {}
                                      showError = true;
                                    } else {
                                      showError = false;
                                    }
                                    // update tỷ trọng : total rate
                                    _data.allocation = value.volume;
                                    widget.onUpdateTotalRate(_data);
                                    setState(() {});
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                if (_initialAmount != null && _initialAmount != 0) ...[
                  kSpacingHeight8,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Đầu tư ban đầu',
                        style: vpTextStyle.body14?.copyWith(
                          color: vpColor.textTertiary,
                        ),
                      ),
                      Text(
                        (_initialAmount! * _data.allocation ~/ 100).valueText,
                        style: vpTextStyle.subtitle14?.copyWith(
                          color: vpColor.textPrimary,
                        ),
                      ),
                    ],
                  ),
                ],
                kSpacingHeight8,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Đầu tư định kỳ',
                      style: vpTextStyle.body14?.copyWith(
                        color: vpColor.textTertiary,
                      ),
                    ),
                    Text(
                      (_periodicAmount * _data.allocation ~/ 100).valueText,
                      style: vpTextStyle.subtitle14?.copyWith(
                        color: vpColor.textPrimary,
                      ),
                    ),
                  ],
                ).paddingBottom12(),
                Divider(height: 1, thickness: 1, color: vpColor.strokeNormal),
              ],
            ),
          ),
          kSpacingHeight12,
        ],
      ),
    );
  }

  bool validate() {
    if (((_periodicAmount * (textEditRateController.text.volume / 100))
                .toInt() <
            100000) &&
        textEditRateController.text.isNotEmpty) {
      return true;
    } else {
      return false;
    }
  }

  void onEditRate() async {
    textEditRateController.text =
        _data.allocation == 0 ? '' : _data.allocation.toInt().toString();
    var result = await showModalBottomSheet(
      barrierColor: themeData.overlayBottomSheet,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, setState) {
            return AppBottomSheet(
              isFullSize: true,
              initialChildSize: 0.6 * MediaQuery.of(context).size.height,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Tỷ lệ cổ phiếu ${_data.symbol}',
                    style: vpTextStyle.body14?.copyWith(color: themeData.black),
                  ),
                  kSpacingHeight8,
                  Stack(
                    children: [
                      AppTextField(
                        enable: true,
                        typeInput: TextInputType.number,
                        hintText: 'Nhập tỷ lệ',
                        maxLength: 3,
                        hintStyle: vpTextStyle.body16?.copyWith(
                          color: themeData.textHint,
                        ),
                        autofocus: true,
                        isAllowMoveCursor: false,
                        paddingBottom: 16,
                        textController: textEditRateController,
                        isSuccess: false,
                        messsage: showError ? 'Tỷ trọng không hợp lệ' : null,
                        onChanged: (value) {
                          if (value == '0' ||
                              (value.length > 2 && value != '100')) {
                            showError = true;
                          } else {
                            showError = false;
                          }
                          setState(() {});
                        },
                      ),
                      Positioned(
                        right: 16,
                        top: 13,
                        child: Text(
                          '%',
                          style: vpTextStyle.body14?.copyWith(
                            color: themeData.gray500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  kSpacingHeight8,
                  Row(
                    children: [
                      Expanded(
                        child: ButtonWidget(
                          action: WealthStock.current.buttonClose,
                          colorEnable: Colors.transparent,
                          colorBorder: themeData.borderBg,
                          textStyle: vpTextStyle.body14?.copyWith(
                            color: themeData.black,
                          ),
                          onPressed: () => Navigator.pop(context),
                        ),
                      ),
                      kSpacingWidth12,
                      Expanded(
                        child: ButtonWidget(
                          enable: isEnableAccept,
                          action: WealthStock.current.buttonAccept,
                          onPressed: () {
                            Navigator.pop(
                              context,
                              textEditRateController.text.volume,
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        );
      },
    );
    if (result == null) {
      textEditRateController.text =
          _data.allocation == 0 ? '' : _data.allocation.toInt().toString();
      return;
    }
    setState(() {
      _data.allocation = result;
      widget.onUpdateTotalRate(_data);
    });
  }

  bool get isEnableAccept {
    if (showError) return false;
    if (textEditRateController.text.isEmpty) return false;
    return true;
  }
}
