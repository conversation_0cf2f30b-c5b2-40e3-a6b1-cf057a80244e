import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/theme/theme_service.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';

class AppTextField extends StatefulWidget {
  const AppTextField({
    Key? key,
    this.hintText,
    this.maxLength,
    required this.textController,
    this.typeInput,
    required this.onChanged,
    this.prefixPathSVG,
    this.messsage,
    this.isSuccess,
    this.readOnly = false,
    this.isPassword = false,
    this.haveVisible = false,
    this.showCursor = true,
    this.focusNode,
    this.isAllowMoveCursor = true,
    this.autofocus = true,
    this.enable = true,
    this.listFormatter,
    this.isUpperCaseFirst = false,
    this.iconHide,
    this.iconShow,
    this.paddingBottom,
    this.paddingPrefix,
    this.enableBorderColor,
    this.errorStyle,
    this.textStyle,
    this.hintStyle,
    this.focusedErrorBorderColor,
    this.focusedBorderColor,
    this.disabledBorderColor,
    this.enabledBorderColor,
    this.contentPadding,
    this.autocorrect = true,
    this.enableSuggestions = true,
  }) : super(key: key);

  final String? hintText;
  final int? maxLength;
  final TextEditingController textController;
  final TextInputType? typeInput;
  final Function(String) onChanged;
  final String? prefixPathSVG;
  final String? messsage;
  final bool? readOnly;
  final bool? isPassword;
  final bool? isSuccess;
  final bool? haveVisible;
  final bool? showCursor;
  final FocusNode? focusNode;
  final bool? isAllowMoveCursor;
  final bool? autofocus;
  final bool? enable;
  final bool? isUpperCaseFirst;
  final List<TextInputFormatter>? listFormatter;
  final Widget? iconHide;
  final Widget? iconShow;
  final double? paddingBottom;
  final double? paddingPrefix;
  final Color? enableBorderColor;
  final TextStyle? errorStyle;
  final TextStyle? textStyle;
  final TextStyle? hintStyle;
  final Color? focusedErrorBorderColor;
  final Color? focusedBorderColor;
  final Color? disabledBorderColor;
  final Color? enabledBorderColor;
  final EdgeInsetsGeometry? contentPadding;
  final bool enableSuggestions;
  final bool autocorrect;
  @override
  State<AppTextField> createState() => AppTextFieldState();
}

class AppTextFieldState extends State<AppTextField> {
  bool _passwordVisible = false;

  @override
  void initState() {
    super.initState();
    _passwordVisible = widget.isPassword ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 42,
          child: TextField(
            enableSuggestions: widget.enableSuggestions,
            autocorrect: widget.autocorrect,
            keyboardAppearance: Brightness.dark,
            enabled: widget.enable,
            textCapitalization:
                (widget.isUpperCaseFirst ?? false)
                    ? TextCapitalization.sentences
                    : TextCapitalization.none,
            enableInteractiveSelection: widget.isAllowMoveCursor ?? true,
            focusNode: widget.focusNode,
            textAlignVertical: TextAlignVertical.bottom,
            readOnly: widget.readOnly ?? false,
            onChanged: (value) {
              widget.onChanged(value);
            },
            showCursor: widget.showCursor,
            autofocus: widget.autofocus ?? true,
            obscureText: _passwordVisible,
            cursorColor: themeData.black,
            controller: widget.textController,
            inputFormatters: [
              LengthLimitingTextInputFormatter(widget.maxLength),
              ...widget.listFormatter ?? [],
            ],
            style: widget.textStyle ?? Theme.of(context).textTheme.bodyLarge,
            keyboardType: widget.typeInput,
            decoration: const InputDecoration()
                .applyDefaults(Theme.of(context).inputDecorationTheme)
                .copyWith(
                  isDense: true,
                  fillColor: getColorBackground(),
                  filled: true,
                  errorMaxLines: 10,
                  contentPadding: _getContentPadding(),
                  prefixIcon:
                      widget.prefixPathSVG == null
                          ? null
                          : Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal:
                                  widget.paddingPrefix ?? SizeUtils.kSize16,
                            ),
                            child: SvgPicture.asset(
                              widget.prefixPathSVG ?? '',
                              width: SizeUtils.kSizeIcon16,
                              height: SizeUtils.kSizeIcon16,
                              color: Theme.of(context).iconTheme.color,
                            ),
                          ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: const BorderRadius.all(
                      Radius.circular(SizeUtils.kRadius8),
                    ),
                    borderSide: BorderSide(
                      width: 1,
                      color: _getFocusedErrorBorderColor(),
                    ),
                  ),
                  errorStyle: widget.errorStyle,
                  focusedBorder: OutlineInputBorder(
                    borderRadius: const BorderRadius.all(
                      Radius.circular(SizeUtils.kRadius8),
                    ),
                    borderSide: BorderSide(
                      width: 1,
                      color: _getFocusedBorderColor(),
                    ),
                  ),
                  disabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: _getDisabledBorderColor()),
                    borderRadius: BorderRadius.circular(SizeUtils.kRadius8),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: _getEnabledBorderColor()),
                    borderRadius: BorderRadius.circular(SizeUtils.kRadius8),
                  ),
                  hintText: widget.hintText,
                  hintStyle:
                      widget.hintStyle ??
                      vpTextStyle.body14?.copyWith(color: themeData.textHint),
                  suffixIcon:
                      (widget.haveVisible ?? false)
                          ? InkWell(
                            child:
                                _passwordVisible
                                    ? widget.iconHide ??
                                        Icon(
                                          Icons.visibility_off,
                                          color: themeData.gray700,
                                        )
                                    : widget.iconShow ??
                                        Icon(
                                          Icons.visibility,
                                          color: themeData.gray700,
                                        ),
                            onTap: () {
                              setState(() {
                                _passwordVisible = !_passwordVisible;
                              });
                            },
                          )
                          : const SizedBox.shrink(),
                ),
          ),
        ),
        const SizedBox(height: SizeUtils.kSize8),
        widget.messsage == null
            ? const SizedBox.shrink()
            : Text(
              widget.messsage ?? '',
              style:
                  widget.errorStyle ??
                  vpTextStyle.captionRegular?.copyWith(
                    color: getColor(themeData.red),
                  ),
            ),
      ],
    );
  }

  EdgeInsetsGeometry _getContentPadding() {
    final contentPadding = widget.contentPadding;
    if (contentPadding != null) {
      return contentPadding;
    } else {
      return EdgeInsets.only(
        bottom: widget.paddingBottom ?? SizeUtils.kSize32,
        left: widget.prefixPathSVG == null ? SizeUtils.kSize16 : 0,
      );
    }
  }

  Color _getFocusedErrorBorderColor() {
    final focusedErrorBorderColor = widget.focusedErrorBorderColor;
    if (focusedErrorBorderColor != null) {
      return focusedErrorBorderColor;
    } else {
      return themeData.red;
    }
  }

  Color _getFocusedBorderColor() {
    final focusedBorderColor = widget.focusedBorderColor;
    if (focusedBorderColor != null) {
      return focusedBorderColor;
    } else {
      return getColor(themeData.borderBg);
    }
  }

  Color _getDisabledBorderColor() {
    final disabledBorderColor = widget.disabledBorderColor;
    if (disabledBorderColor != null) {
      return disabledBorderColor;
    } else {
      final colorToCheck =
          context.read<ThemeCubit>().currentTheme == AppTheme.dark
              ? Colors.transparent
              : themeData.borderDisable;
      return getColor(colorToCheck);
    }
  }

  Color _getEnabledBorderColor() {
    final enableBorderColor = widget.enableBorderColor;
    if (enableBorderColor != null) {
      return enableBorderColor;
    } else {
      final colorToCheck =
          context.read<ThemeCubit>().currentTheme == AppTheme.dark
              ? Colors.transparent
              : themeData.borderDisable;
      return getColor(colorToCheck);
    }
  }

  Color getColor(Color color) {
    return (widget.messsage != null &&
            widget.textController.text.isNotEmpty &&
            widget.isSuccess != null)
        ? (widget.isSuccess ?? false ? color : ColorDefine.red)
        : color;
  }

  Color getColorBackground() {
    final opacity =
        context.read<ThemeCubit>().currentTheme == AppTheme.dark ? 0.32 : 0.16;
    return (widget.messsage != null &&
            widget.textController.text.isNotEmpty &&
            widget.isSuccess != null)
        ? (widget.isSuccess ?? false
            ? themeData.bgInput
            : themeData.errorBgInput.withOpacity(opacity))
        : context.read<ThemeCubit>().currentTheme == AppTheme.dark
        ? themeData.highlightBg
        : Colors.transparent;
  }
}
