import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:vp_common/utils/app_time_utils.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_design_system/widget/input_view/input_view.dart';
import 'package:vp_wealth/data/enum/investment_frequency.dart';
import 'package:vp_wealth/data/utils/app_keyboard_utils.dart';
import 'package:vp_wealth/data/utils/padding_extends.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/wealths/plan/cubit/wealth_plan_cubit.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/plan_utils_widget.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/section_widget.dart';
import 'package:vp_wealth/presentation/wealths/widgets/bottom_sheet/app_custom_calendar.dart';

class InvestmentTimeWidget extends StatefulWidget {
  final WealthPlanCubit wealthPlanCubit;
  const InvestmentTimeWidget({Key? key, required this.wealthPlanCubit})
    : super(key: key);

  @override
  State<InvestmentTimeWidget> createState() => _InvestmentTimeWidgetState();
}

class _InvestmentTimeWidgetState extends State<InvestmentTimeWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        BuildSection(
          stepNumber: 3,
          sectionName: WealthStock.current.investmentTime,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //// Đầu tư trong
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            WealthStock.current.investmentTimeIn,
                            style: vpTextStyle.body14?.copyWith(
                              color: vpColor.textSecondary,
                            ),
                          ),
                          TooltipIcon().paddingOnly(top: 4, left: 4),
                        ],
                      ).paddingRight12(),
                      SizedBox(
                        width: 190,
                        height: 36,
                        child: BlocBuilder<WealthPlanCubit, WealthPlanState>(
                          bloc: widget.wealthPlanCubit,
                          buildWhen:
                              (previous, current) =>
                                  previous.valueInvestmentTime !=
                                      current.valueInvestmentTime ||
                                  previous.showErrorInvestmentTime !=
                                      current.showErrorInvestmentTime,
                          builder: (context, state) {
                            return VPTextField.small(
                              inputType:
                                  state.showErrorInvestmentTime
                                      ? InputType.error
                                      : InputType.rest,
                              suffixIcon: (_) {
                                return Text(
                                  WealthStock.current.year,
                                  style: vpTextStyle.body14?.copyWith(
                                    color: vpColor.textTertiary,
                                  ),
                                ).paddingTop4();
                              },
                              controller:
                                  widget
                                      .wealthPlanCubit
                                      .textInvestmentTimeController,
                              keyboardType: TextInputType.phone,
                              hintText:
                                  WealthStock.current.hintTextEnterYearNumber,
                              maxLength: 3,
                              hintStyle: vpTextStyle.body14?.copyWith(
                                color: themeData.hintColor,
                              ),
                              autofocus: false,
                              onChanged: (value) {
                                if (value.isEmpty) {
                                  widget.wealthPlanCubit
                                      .calculateSliderInvestmentTimeWhenInput(
                                        value,
                                      );
                                  return;
                                }
                                if (int.parse(value) > 100) {
                                  widget
                                      .wealthPlanCubit
                                      .textInvestmentTimeController
                                      .text = '${state.valueInvestmentTime}';
                                  return;
                                }
                                widget.wealthPlanCubit
                                    .calculateSliderInvestmentTimeWhenInput(
                                      value,
                                    );
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ).paddingBottom12(),

                  /// Tần suất
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        WealthStock.current.withInvestmentFrequency,
                        style: vpTextStyle.body14?.copyWith(
                          color: vpColor.textSecondary,
                        ),
                      ).paddingRight12(),
                      BlocBuilder<WealthPlanCubit, WealthPlanState>(
                        bloc: widget.wealthPlanCubit,
                        buildWhen:
                            (previous, current) =>
                                previous.investmentFrequency !=
                                current.investmentFrequency,
                        builder: (context, state) {
                          return GestureDetector(
                            onTap: () {
                              AppKeyboardUtils.unFocusTextField();
                              PlanUtilsWidget.showBottomSheetInvestmentFrequency(
                                context,
                                widget.wealthPlanCubit,
                              );
                            },
                            child: Container(
                              width: 190,
                              height: 36,
                              decoration: BoxDecoration(
                                border: Border.all(color: vpColor.strokeNormal),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    state.investmentFrequency.title,
                                    style: vpTextStyle.body14?.copyWith(
                                      color: vpColor.textPrimary,
                                    ),
                                  ),
                                  const Icon(Icons.keyboard_arrow_down_rounded),
                                ],
                              ).paddingSymmetric(horizontal: 8, vertical: 4),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${WealthStock.current.periodicInvestmentStartDate}: ',
                        style: vpTextStyle.body14?.copyWith(
                          color: themeData.black,
                        ),
                      ).paddingRight12(),
                      GestureDetector(
                        onTap: () {
                          AppKeyboardUtils.unFocusTextField();
                          _onShowCalender(context);
                        },
                        behavior: HitTestBehavior.opaque,
                        child: Container(
                          width: 190,
                          height: 36,
                          decoration: BoxDecoration(
                            color: vpColor.backgroundElevation0,
                            border: Border.all(color: vpColor.strokeNormal),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: BlocBuilder<WealthPlanCubit, WealthPlanState>(
                            bloc: widget.wealthPlanCubit,
                            buildWhen:
                                (previous, current) =>
                                    previous.investmentStartDay !=
                                    current.investmentStartDay,
                            builder: (context, state) {
                              return Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Assets.icons.icCalendar
                                      .svg(color: vpColor.iconPrimary)
                                      .paddingRight8(),
                                  Text(
                                    AppTimeUtils.getDateTimeString(
                                      dateTime: state.investmentStartDay,
                                    ),
                                    style: vpTextStyle.body14?.copyWith(
                                      color: vpColor.textTertiary,
                                    ),
                                  ),
                                  const Spacer(),
                                  const Icon(Icons.keyboard_arrow_down_rounded),
                                ],
                              ).paddingSymmetric(horizontal: 8);
                            },
                          ),
                        ),
                      ),
                    ],
                  ).paddingTop12(),
                  BlocBuilder<WealthPlanCubit, WealthPlanState>(
                    bloc: widget.wealthPlanCubit,
                    buildWhen:
                        (previous, current) =>
                            previous.investmentFrequency !=
                                current.investmentFrequency ||
                            previous.investmentStartDay !=
                                current.investmentStartDay,
                    builder: (context, state) {
                      return state.investmentFrequency ==
                              InvestmentFrequency.monthly
                          ? RichText(
                            text: TextSpan(
                              style: vpTextStyle.body14?.copyWith(
                                color: vpColor.textPrimary,
                              ),
                              text:
                                  '${WealthStock.current.monthlyInvestmentDay} ',
                              children: [
                                TextSpan(
                                  text: '${state.investmentStartDay!.day}.',
                                  style: vpTextStyle.subtitle14?.copyWith(
                                    color: vpColor.textPrimary,
                                  ),
                                ),
                              ],
                            ),
                          )
                          : RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  children: [
                                    TextSpan(
                                      text: ' của các tháng ',
                                      style: vpTextStyle.body14?.copyWith(
                                        color: vpColor.textPrimary,
                                      ),
                                      children: [
                                        TextSpan(
                                          style: vpTextStyle.subtitle14
                                              ?.copyWith(
                                                color: vpColor.textPrimary,
                                              ),
                                          text:
                                              '${widget.wealthPlanCubit.buildInvestmentSchedule()}.',
                                        ),
                                      ],
                                    ),
                                  ],
                                  text: '${state.investmentStartDay!.day}',
                                  style: vpTextStyle.subtitle14?.copyWith(
                                    color: vpColor.textPrimary,
                                  ),
                                ),
                              ],
                              style: vpTextStyle.body14?.copyWith(
                                color: vpColor.textPrimary,
                              ),
                              text: 'Ngày đầu tư định kỳ hàng quý là ngày ',
                            ),
                          );
                    },
                  ).paddingTop16(),
                ],
              ),
            ],
          ),
        ),
        Divider(height: 2, color: vpColor.strokeDisable),
      ],
    );
  }

  void _onShowCalender(BuildContext context) {
    final now = DateTime.now();
    final defaultMinDay = DateTime(now.year, now.month, now.day + 1);
    final defaultMaxDay = DateTime(now.year + 5, now.month, now.day);
    final initialSelectedDate =
        widget.wealthPlanCubit.state.investmentStartDay ?? defaultMinDay;
    showAppCalendar(
      context,
      maxDay: defaultMaxDay,
      minDay: defaultMinDay,
      isRangeMode: false,
      initialSelectedDate: initialSelectedDate,
      callback:
          (param) => widget.wealthPlanCubit.updateInvestmentStartDay(param),
    );
  }
}

class TooltipPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()..color = vpColor.backgroundElevation2;

    final Path path =
        Path()
          ..addRRect(
            RRect.fromRectAndRadius(
              Rect.fromLTWH(0, 0, size.width, size.height),
              const Radius.circular(8),
            ),
          )
          ..moveTo(20, size.height)
          ..lineTo(26, size.height + 8)
          ..lineTo(32, size.height)
          ..close();

    canvas.drawShadow(path, vpColor.backgroundElevation2, 4, false);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class TooltipManager {
  static final TooltipManager _instance = TooltipManager._internal();

  factory TooltipManager() => _instance;

  TooltipManager._internal();

  OverlayEntry? _tooltipOverlay;

  void showTooltip(BuildContext context, Offset position) {
    removeTooltip(); // Remove existing tooltip before showing a new one

    final overlay = Overlay.of(context);

    _tooltipOverlay = OverlayEntry(
      builder:
          (context) => Scaffold(
            backgroundColor: Colors.transparent,
            body: Stack(
              children: [
                Positioned.fill(
                  child: GestureDetector(
                    onTap: removeTooltip, // Hide tooltip when tapping anywhere
                    behavior: HitTestBehavior.opaque,
                  ),
                ),
                Positioned(
                  left: position.dx - 15,
                  top: position.dy - 65,
                  child: CustomPaint(
                    painter: TooltipPainter(),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      width: 220,
                      decoration: BoxDecoration(
                        color: vpColor.backgroundElevation2,
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF2A3346).withOpacity(0.12),
                            offset: const Offset(0, 16),
                            blurRadius: 32,
                            spreadRadius: 0,
                          ),
                          BoxShadow(
                            color: const Color(0xFF2A3346).withOpacity(0.04),
                            offset: const Offset(0, 4),
                            blurRadius: 32,
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Text(
                        'Thời gian đầu tư tối thiểu từ 1 năm, tối đa là 100 năm.',
                        style: vpTextStyle.captionMedium?.copyWith(
                          color: vpColor.textPrimary,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
    );

    overlay.insert(_tooltipOverlay!);
  }

  void removeTooltip() {
    _tooltipOverlay?.remove();
    _tooltipOverlay = null;
  }
}

class TooltipIcon extends StatelessWidget {
  final GlobalKey iconKey = GlobalKey();

  TooltipIcon({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      key: iconKey,
      onTap: () {
        final RenderBox renderBox =
            iconKey.currentContext?.findRenderObject() as RenderBox;
        final Offset offset = renderBox.localToGlobal(Offset.zero);
        TooltipManager().showTooltip(context, offset);
      },
      child: Assets.icons.icWarningSuggest.svg(),
    );
  }
}
