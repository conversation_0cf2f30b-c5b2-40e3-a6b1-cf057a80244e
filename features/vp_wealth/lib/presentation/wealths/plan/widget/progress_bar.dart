import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:vp_common/widget/line_view.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/theme/theme_service.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_design_system/widget/button/vps_button.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/generated/l10n.dart';

class StepView extends StatefulWidget {
  const StepView({
    Key? key,
    required this.step,
    this.total = 3,
    this.space = 3,
    this.height = 3,
    this.onEndAnimation,
    this.revertAnimation = false,
    this.enableColor = ColorDefine.primary,
    this.disableColor = ColorDefine.gray300,
  }) : assert(step >= 1),
       assert(step <= total),
       super(key: key);

  final int total;

  final int step;

  final double space;

  final double height;

  final Color enableColor;

  final Color disableColor;

  final bool revertAnimation;

  final VoidCallback? onEndAnimation;

  @override
  State<StepView> createState() => _StepViewState();
}

class _StepViewState extends State<StepView> {
  double width = 0;

  double widthExact = 0;

  @override
  void initState() {
    super.initState();

    startAnimation();
  }

  void startAnimation() async {
    await Future.delayed(const Duration(milliseconds: 200));
    if (mounted) setState(() => width = widthExact);
  }

  void revertAnimation() {
    if (mounted && widget.revertAnimation && width == widthExact) {
      setState(() => width = 0);
    }
  }

  @override
  void didUpdateWidget(covariant StepView oldWidget) {
    if (widget.step != oldWidget.step) {
      width = 0;
      startAnimation();
    }

    if (widget.revertAnimation != oldWidget.revertAnimation) {
      revertAnimation();
    }

    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final widgets = <Widget>[];

        final maxWidth = constraints.maxWidth;

        widthExact = maxWidth - (widget.space * (widget.total - 1));

        widthExact /= widget.total;

        for (int i = 1; i < widget.total + 1; i++) {
          if (i < widget.step) {
            widgets.add(
              Expanded(
                child: LineView(
                  width: double.infinity,
                  height: widget.height,
                  color: widget.enableColor,
                ),
              ),
            );
          }

          if (i == widget.step) {
            widgets.add(
              Expanded(
                child: Stack(
                  children: [
                    LineView(
                      width: double.infinity,
                      height: widget.height,
                      color: widget.disableColor,
                    ),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: AnimatedContainer(
                        width: width,
                        height: widget.height,
                        onEnd: widget.onEndAnimation,
                        duration: const Duration(milliseconds: 300),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(3),
                          color: themeData.primary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          if (i > widget.step) {
            widgets.add(
              Expanded(
                child: LineView(
                  width: double.infinity,
                  height: widget.height,
                  color: widget.disableColor,
                ),
              ),
            );
          }

          if (i < widget.total) {
            widgets.add(
              LineView(
                width: widget.space,
                height: widget.height,
                color: themeData.white,
              ),
            );
          }
        }

        return Row(children: widgets);
      },
    );
  }
}

class BackAndNextView extends StatelessWidget {
  const BackAndNextView({
    Key? key,
    this.onBack,
    this.onNext,
    this.enableButton,
    this.padding = const EdgeInsets.symmetric(horizontal: SizeUtils.kSize16),
  }) : super(key: key);

  final VoidCallback? onBack;

  final VoidCallback? onNext;
  final bool? enableButton;

  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: SizeUtils.kSize16),
      child: VpsButton.primarySmall(
        alignment: Alignment.centerRight,
        onPressed: onNext,
        disabled: enableButton ?? false,
        title: WealthStock.current.buttonContinue,
      ),
    );
  }
}

class StepBottomView extends StatefulWidget {
  const StepBottomView({
    Key? key,
    required this.step,
    this.total = 3,
    this.space = 3,
    this.height = 3,
    this.enableColor = ColorDefine.primary,
    this.disableColor = ColorDefine.gray300,
    this.executeCallbackWhenFinishRevert = false,
    this.onBack,
    this.enableButton = false,
    this.onNext,
  }) : super(key: key);

  final int total;

  final int step;

  final double space;

  final double height;

  final Color enableColor;

  final Color disableColor;

  final VoidCallback? onBack;

  final VoidCallback? onNext;

  final bool executeCallbackWhenFinishRevert;
  final bool enableButton;

  @override
  State<StepBottomView> createState() => _StepBottomViewState();
}

class _StepBottomViewState extends State<StepBottomView> {
  bool revertAnimation = false;

  bool back = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        StepView(
          total: widget.total,
          step: widget.step,
          space: widget.space,
          height: widget.height,
          revertAnimation: revertAnimation,
          enableColor: widget.enableColor,
          disableColor: widget.disableColor,
          onEndAnimation: onEndAnimation,
        ),
        const SizedBox(height: SizeUtils.kSize10),
        BackAndNextView(
          enableButton: widget.enableButton,
          onBack:
              widget.executeCallbackWhenFinishRevert && widget.onBack != null
                  ? handleOnBack
                  : widget.onBack,
          onNext: widget.onNext,
        ),
        const SizedBox(height: SizeUtils.kSize10),
      ],
    );
  }

  /// execute onback when end animation
  void onEndAnimation() {
    if (back) {
      back = false;

      revertAnimation = false;

      widget.onBack?.call();
    }
  }

  void handleOnBack() {
    back = true;

    if (widget.executeCallbackWhenFinishRevert) {
      setState(() => revertAnimation = true);
    }
  }
}

class BottomView extends StatelessWidget {
  const BottomView({this.lineHeight = 1, this.onBack, this.onNext, Key? key})
    : super(key: key);

  final double lineHeight;

  final VoidCallback? onBack;

  final VoidCallback? onNext;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        LineView(
          width: double.infinity,
          height: lineHeight,
          color: themeData.disabledColor,
        ),
        const SizedBox(height: SizeUtils.kSize10),
        BackAndNextView(onBack: onBack, onNext: onNext),
        const SizedBox(height: SizeUtils.kSize10),
      ],
    );
  }
}
