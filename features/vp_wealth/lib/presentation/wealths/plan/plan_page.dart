import 'dart:async';

import 'package:flutter/material.dart';
import 'package:vp_common/utils/money_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart' hide ShimmerWidget;
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/data/utils/app_keyboard_utils.dart';
import 'package:vp_wealth/data/utils/padding_extends.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/wealths/plan/cubit/wealth_plan_cubit.dart';
import 'package:vp_wealth/presentation/wealths/plan/info_plan_page.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/plan_widget/build_wealth_name_widget.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/plan_widget/initial_investment_amount_widget.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/plan_widget/investment_category_widget.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/plan_widget/investment_time_widget.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/remove_wealth_dialog.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/text_suggest_widget.dart';
import 'package:vp_wealth/presentation/wealths/widgets/shimmer_widget.dart';
import 'package:vp_wealth/router/wealth_router.dart';

class PlanPage extends StatefulWidget {
  final PlanModel? plan;

  const PlanPage({super.key, this.plan});

  @override
  State<PlanPage> createState() => _PlanPageState();
}

class _PlanPageState extends State<PlanPage> {
  late WealthPlanCubit _wealthPlanCubit;
  final FocusNode _initialInvestmentAmountFocusNode = FocusNode();
  final FocusNode _periodicInvestmentAmountFocusNode = FocusNode();
  final ScrollController _controllerCategoryInvestment = ScrollController();

  PlanModel? get _planModel => widget.plan;

  @override
  void initState() {
    _wealthPlanCubit = context.read<WealthPlanCubit>();
    super.initState();
    _wealthPlanCubit
        .onInitialData(_planModel)
        .then((value) => _handleJumpToCategoryInvestment());
    _initialInvestmentAmountFocusNode.addListener(() {
      _wealthPlanCubit.onShowSuggestInitialInvestmentMoney(
        _initialInvestmentAmountFocusNode.hasFocus,
      );
    });
    _periodicInvestmentAmountFocusNode.addListener(() {
      _wealthPlanCubit.onShowSuggestPeriodicInvestmentMoney(
        _periodicInvestmentAmountFocusNode.hasFocus,
      );
    });
  }

  @override
  void dispose() {
    _controllerCategoryInvestment.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: GestureDetector(
        onTap: TooltipManager().removeTooltip,
        child: VPScaffold(
          backgroundColor: vpColor.backgroundElevation0,
          appBar: VPAppBar.flows(
            title: WealthStock.current.wealthPlan,
            leading: _closePlan,
          ),
          body: BlocProvider.value(
            value: _wealthPlanCubit,
            child: GestureDetector(
              onTap: () => AppKeyboardUtils.unFocusTextField(),
              child: SafeArea(
                child: Column(
                  children: [
                    Divider(
                      color: vpColor.backgroundElevationMinus1,
                      height: 8,
                      thickness: 8,
                    ),
                    Expanded(
                      child: ListView(
                        children: [
                          // (1)
                          _buildWealthName,
                          // (2)
                          _initialInvestmentAmount,
                          // (3)
                          _investmentTime,
                          // (4)
                          _investmentCategory,
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          bottomNavigationBar: _buildBottomNavigator(),
        ),
      ),
    );
  }

  Widget get _buildWealthName {
    return BuildWealthNameWidget(wealthPlanCubit: _wealthPlanCubit);
  }

  Widget get _initialInvestmentAmount {
    return InitialInventmentWidget(
      wealthPlanCubit: _wealthPlanCubit,
      initialInvestmentAmountFocusNode: _initialInvestmentAmountFocusNode,
      periodicInvestmentAmountFocusNode: _periodicInvestmentAmountFocusNode,
    );
  }

  Widget get _investmentTime {
    return InvestmentTimeWidget(wealthPlanCubit: _wealthPlanCubit);
  }

  Widget get _investmentCategory {
    return InvestmentCategoryWidget(
      wealthPlanCubit: _wealthPlanCubit,
      controllerCategoryInvestment: _controllerCategoryInvestment,
    );
  }

  void _handleJumpToCategoryInvestment() {
    WidgetsBinding.instance.addPostFrameCallback(
      (_) => Future.delayed(const Duration(seconds: 1)).then((value) {
        if (!_controllerCategoryInvestment.hasClients) return;
        double maxScrollExtent =
            _controllerCategoryInvestment.position.maxScrollExtent;
        int length = _wealthPlanCubit.state.listCategory.length;
        switch (_wealthPlanCubit
            .state
            .investmentCategorySelected
            ?.invPhilosophyType) {
          case 'CUSTOM':
            _controllerCategoryInvestment.animateTo(
              maxScrollExtent,
              duration: const Duration(milliseconds: 200),
              curve: Curves.bounceIn,
            );
            break;
          case 'GROWTH':
            _controllerCategoryInvestment.animateTo(
              maxScrollExtent * 3 / length,
              duration: const Duration(milliseconds: 200),
              curve: Curves.bounceIn,
            );
            break;
        }
      }),
    );
  }

  Widget get _closePlan {
    return InkWell(
      onTap: () => showDialogCancelPlan(context),
      child: Icon(Icons.close, color: vpColor.textPrimary).paddingTop4(),
    );
  }

  Widget _buildsuggestInitialInvestmentMoney() {
    return BlocBuilder<WealthPlanCubit, WealthPlanState>(
      bloc: _wealthPlanCubit,
      buildWhen:
          (previous, current) =>
              previous.showSuggestInitialInvestmentMoney !=
              current.showSuggestInitialInvestmentMoney,
      builder: (context, state) {
        if (!state.showSuggestInitialInvestmentMoney) {
          return const SizedBox();
        }
        return WidgetTextSuggest(
          isNeedUnFocus: false,
          textController:
              _wealthPlanCubit.textInitialInvestmentAmountController,
          onTap: () {
            _wealthPlanCubit.onCalculateExpectedPerformance();
            AppKeyboardUtils.dismissKeyboard();
          },
        );
      },
    );
  }

  Widget _buildSuggestPeriodicInvestmentMoney() {
    return BlocBuilder<WealthPlanCubit, WealthPlanState>(
      bloc: _wealthPlanCubit,
      buildWhen:
          (previous, current) =>
              previous.showSuggestPeriodicInvestmentMoney !=
              current.showSuggestPeriodicInvestmentMoney,
      builder: (context, state) {
        if (!state.showSuggestPeriodicInvestmentMoney) {
          return const SizedBox();
        }
        return WidgetTextSuggest(
          isNeedUnFocus: false,
          textController: _wealthPlanCubit.textMonthlyIncomeController,
          onTap: () {
            _wealthPlanCubit.changeShowErrorMonthlyIncome();
            _wealthPlanCubit.onCalculateExpectedPerformance();
            AppKeyboardUtils.dismissKeyboard();
          },
        );
      },
    );
  }

  Widget _buildBottomNavigator() {
    return SafeArea(
      bottom: MediaQuery.of(context).viewInsets.bottom <= 0,
      child: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 60, // Chiều cao vùng đổ bóng
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    spreadRadius: 0,
                    offset: const Offset(0, -12),
                  ),
                ],
              ),
            ),
          ),
          Container(
            decoration: BoxDecoration(color: vpColor.backgroundElevation0),
            child: Wrap(
              children: [
                Visibility(
                  visible: !(MediaQuery.of(context).viewInsets.bottom > 0),
                  child: Column(
                    children: [
                      BlocBuilder<WealthPlanCubit, WealthPlanState>(
                        bloc: _wealthPlanCubit,
                        buildWhen:
                            (previous, current) =>
                                previous.listChartDataCategoryPerformance !=
                                    current.listChartDataCategoryPerformance ||
                                previous.loading != current.loading ||
                                previous.showErrorInitialIncome !=
                                    current.showErrorInitialIncome,
                        builder: (context, state) {
                          return state.loading
                              ? const SizedBox(
                                height: 56,
                                child: ShimmerWidget(),
                              )
                              : _sotienkyvong(
                                state,
                              ).paddingSymmetric(horizontal: 16);
                        },
                      ),
                      SizedBox(
                        height: 2,
                        width: double.infinity,
                        child: ColoredBox(color: vpColor.strokeNormal),
                      ).paddingBottom16(),
                      BlocBuilder<WealthPlanCubit, WealthPlanState>(
                        bloc: _wealthPlanCubit,
                        builder: (context, state) {
                          return Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              VpsButton.teriatySmall(
                                onPressed: () {
                                  context.pop();
                                  context.push(
                                    WealthRouter.investmentGoalsPage,
                                    extra: widget.plan,
                                  );
                                },
                                disabled: false,
                                alignment: Alignment.center,
                                title: WealthStock.current.buttonBack,
                              ),

                              /// auto enable, required by PO team
                              VpsButton.primarySmall(
                                onPressed: () {
                                  _wealthPlanCubit.onCreateDraftPlan(
                                    (plan) => context.push(
                                      WealthRouter.infoPlanPage,
                                      extra: InfoPlanArguments(
                                        model: plan,
                                        cubit: _wealthPlanCubit,
                                      ),
                                    ),
                                  );
                                },
                                disabled: false,
                                // disabled:
                                //     !(!state.showErrorInvestmentTime &&
                                //         !state.showErrorMonthlyIncome),
                                alignment: Alignment.center,
                                title: WealthStock.current.wealContinue,
                              ),
                            ],
                          ).paddingHorizontal(16);
                        },
                      ).paddingBottom16(),
                    ],
                  ),
                ),
                _buildsuggestInitialInvestmentMoney(),
                _buildSuggestPeriodicInvestmentMoney(),
              ],
            ),
          ).paddingOnly(bottom: MediaQuery.of(context).viewInsets.bottom),
        ],
      ),
    );
  }

  Widget _sotienkyvong(WealthPlanState state) {
    return SizedBox(
      height: 56,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Assets.icons.euro.svg().paddingOnly(right: 8, bottom: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                WealthStock.current.expectedMoney,
                style: vpTextStyle.captionRegular?.copyWith(
                  color: vpColor.textTertiary,
                ),
              ),
              Text(
                state.listChartDataCategoryPerformance.isNotEmpty
                    ? MoneyUtils.formatMoney(
                      state.listChartDataCategoryPerformance.last.value
                          .toDouble(),
                    )
                    : '-đ',
                style: vpTextStyle.subtitle16?.copyWith(
                  color: vpColor.textPrimary,
                ),
              ),
            ],
          ).paddingTop4(),
          const Spacer(),
          GestureDetector(
            onTap: () {
              if (state.listChartDataCategoryPerformance.isNotEmpty &&
                  !state.showErrorMonthlyIncome &&
                  !state.showErrorInitialIncome) {
                context.push(
                  WealthRouter.categoryPerformancePage,
                  extra: _wealthPlanCubit,
                );
              }
            },
            child: Text(
              WealthStock.current.seeSample,
              style: vpTextStyle.subtitle14?.copyWith(
                color:
                    state.listChartDataCategoryPerformance.isNotEmpty &&
                            !state.showErrorMonthlyIncome &&
                            !state.showErrorInitialIncome
                        ? vpColor.textBrand
                        : vpColor.textDisabled,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
