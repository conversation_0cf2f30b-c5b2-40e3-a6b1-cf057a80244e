import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:vp_common/extensions/string_extensions.dart';
import 'package:vp_common/widget/vpbank_loading.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/enum/investment_frequency.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/data/utils/app_keyboard_utils.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/data/utils/padding_extends.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/place_order_bloc_map_import.dart';
import 'package:vp_wealth/presentation/wealths/plan/cubit/wealth_plan_cubit.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/remove_wealth_dialog.dart';

class InfoPlanArguments {
  final PlanModel model;
  final WealthPlanCubit cubit;

  const InfoPlanArguments({required this.model, required this.cubit});
}

class InfoPlanPage extends StatefulWidget {
  final InfoPlanArguments arg;

  const InfoPlanPage({super.key, required this.arg});

  @override
  State<InfoPlanPage> createState() => _InfoPlanPageState();
}

class _InfoPlanPageState extends State<InfoPlanPage> {
  PlanModel get _planModel => widget.arg.model;

  bool get _isHideRegistrationContract =>
      _cubit.state.requestRegistrationIdContract.isNullOrEmpty;

  WealthPlanCubit get _cubit => widget.arg.cubit;

  @override
  void initState() {
    super.initState();
    _cubit.onGetLinkContract();
    if (_isHideRegistrationContract) return;
    _cubit.onGetLinkRegistrationContract();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => AppKeyboardUtils.dismissKeyboard(),
      child: VPScaffold(
        backgroundColor: vpColor.backgroundElevation0,
        appBar: VPAppBar.flows(
          title: WealthStock.current.confirmPlan,
          leading: _closePlan,
        ),
        body: Container(
          color: vpColor.backgroundElevation0,
          child: Column(
            children: [
              Divider(
                color: vpColor.backgroundElevationMinus1,
                height: 8,
                thickness: 8,
              ),
              Expanded(
                child: ListView(
                  children: [
                    _targetInvestment,
                    _divider,
                    _listStockCategoryInvestment,
                    _divider,
                    _supportPerson,
                    _divider,
                    if (!_isHideRegistrationContract) ...[
                      _registrationContractAndTerm,
                      _divider,
                    ],
                    _contractAndTerm,
                    _divider,
                    // _actionContinue,
                  ],
                ),
              ),
            ],
          ),
        ),
        bottomNavigationBar: SafeArea(
          child: Wrap(
            children: [
              Container(
                padding: const EdgeInsets.only(
                  right: SizeUtils.kSize16,
                  top: SizeUtils.kSize10,
                ),
                child: Row(
                  children: [
                    BlocBuilder<WealthPlanCubit, WealthPlanState>(
                      bloc: _cubit,
                      buildWhen:
                          (previous, current) =>
                              previous.acceptContract != current.acceptContract,
                      builder: (context, state) {
                        return SizedBox(
                          height: SizeUtils.kSize24,
                          child: Checkbox(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4.0),
                            ),
                            side: MaterialStateBorderSide.resolveWith(
                              (states) => BorderSide(
                                color:
                                    state.acceptContract
                                        ? Colors.transparent
                                        : vpColor.strokeDisable,
                              ),
                            ),
                            activeColor: vpColor.iconBrand,
                            value: state.acceptContract,
                            onChanged: (value) {
                              _cubit.acceptContract(value);
                            },
                          ),
                        );
                      },
                    ),
                    Expanded(
                      child: Text(
                        'Bằng việc ấn nút “Xác nhận”, tôi đã đọc, hiểu rõ và đồng ý với các nội dung trên.',
                        style: vpTextStyle.captionSemiBold?.copyWith(
                          color: vpColor.textPrimary,
                        ),
                      ),
                    ),
                  ],
                ),
              ).paddingBottom12(),
              BlocBuilder<WealthPlanCubit, WealthPlanState>(
                bloc: _cubit,
                buildWhen:
                    (previous, current) =>
                        previous.showBrokerInfo != current.showBrokerInfo ||
                        previous.brokerInfo != current.brokerInfo ||
                        previous.messageValidateBroker !=
                            current.messageValidateBroker ||
                        previous.acceptContract != current.acceptContract,
                builder: (context, state) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      SizedBox(
                        height: 2,
                        width: double.infinity,
                        child: ColoredBox(color: vpColor.strokeNormal),
                      ).paddingBottom16(),
                      VpsButton.primarySmall(
                        onPressed: () => _cubit.onActivePlan(),
                        disabled: !statusButotnActive(state),
                        title: WealthStock.current.buttonContinue,
                      ).paddingRight16(),
                      // BackAndNextView(
                      //   onBack: () => Navigator.pop(context),
                      //   enableButton: !statusButotnActive(state),
                      //   onNext: () => _cubit.onActivePlan(),
                      // ),
                    ],
                  );
                },
              ).paddingBottom16(),
            ],
          ),
        ),
      ),
    );
  }

  bool statusButotnActive(WealthPlanState state) {
    if (state.showBrokerInfo) {
      return (state.brokerInfo != null &&
              (state.messageValidateBroker ?? '').isEmpty) &&
          state.acceptContract;
    } else {
      return (state.brokerInfo != null ||
              (state.messageValidateBroker ?? '').isEmpty) &&
          state.acceptContract;
    }
  }

  Widget get _closePlan {
    return InkWell(
      onTap: () => showDialogCancelPlan(context),
      child: Icon(Icons.close, color: vpColor.textPrimary).paddingTop4(),
    );
  }

  Widget get _targetInvestment {
    return Padding(
      padding: const EdgeInsets.all(SizeUtils.kSize16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            WealthStock.current.inforPlan,
            style: vpTextStyle.subtitle16?.copyWith(color: vpColor.textPrimary),
          ).paddingBottom12(),
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              _itemRowText(
                title: WealthStock.current.target,
                value: 'Gia tăng tài sản',
              ),
              _itemRowText(
                title: 'Số tiền đầu tư ban đầu',
                value: _planModel.initialInvestment?.valueText,
              ),
              _itemRowText(
                title: WealthStock.current.periodicInvestmentAmount,
                value: _planModel.investment.valueText,
              ),
              _itemRowText(
                title: WealthStock.current.investmentTime,
                value: '${_planModel.investmentTime.toInt()} năm',
              ),
              _itemRowText(
                title: WealthStock.current.investmentFrequency,
                value: _planModel.investmentFrequency.title,
              ),
              _itemRowText(
                title: 'Ngày đầu tư định kỳ',
                value: _planModel.startDate.split('/').first,
              ),
              // _itemRowText(
              //     title: getWealthLang(WealthKeyLang.startDateSuccess),
              //     value: _planModel.activeDate),
            ],
          ),
        ],
      ),
    );
  }

  Widget get _listStockCategoryInvestment {
    return Padding(
      padding: const EdgeInsets.all(SizeUtils.kSize16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            WealthStock.current.investmentCategory,
            style: vpTextStyle.subtitle16?.copyWith(color: vpColor.textPrimary),
          ).paddingBottom12(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                flex: 2,
                child: Text(
                  'Mã',
                  style: vpTextStyle.captionMedium?.copyWith(
                    color: vpColor.textTertiary,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  textAlign: TextAlign.end,
                  'Tỷ trọng',
                  style: vpTextStyle.captionMedium?.copyWith(
                    color: vpColor.textTertiary,
                  ),
                ),
              ),
              Expanded(
                flex: 4,
                child: Text(
                  'Số tiền ban đầu',
                  textAlign: TextAlign.end,
                  style: vpTextStyle.captionMedium?.copyWith(
                    color: vpColor.textTertiary,
                  ),
                ),
              ),
              Expanded(
                flex: 4,
                child: Text(
                  'Số tiền định kỳ',
                  textAlign: TextAlign.end,
                  style: vpTextStyle.captionMedium?.copyWith(
                    color: vpColor.textTertiary,
                  ),
                ),
              ),
            ],
          ),
          Column(
            children:
                _planModel.itemList
                    .map(
                      (e) => _itemRowStockInvestment(
                        name: e.symbol,
                        rate: e.allocation.toInt(),
                        moneyInitial:
                            ((_planModel.initialInvestment ?? 0) *
                                    e.allocation ~/
                                    100)
                                .valueText,
                        moneyPeriodic:
                            (_planModel.investment * e.allocation ~/ 100)
                                .valueText,
                      ),
                    )
                    .toList(),
          ).paddingBottom8(),
          _divider,
          _itemRowStockInvestment(
            rate: _caculateTotalRate(_planModel.itemList),
            name: 'Tổng',
            isTotal: true,
            moneyInitial: (_planModel.initialInvestment ?? 0).valueText,
            moneyPeriodic: _planModel.investment.valueText,
          ).paddingBottom12(),
          _warning(),
        ],
      ),
    );
  }

  int _caculateTotalRate(List<ItemData> listData) {
    int total = 0;
    for (var element in listData) {
      total += element.allocation.toInt();
    }
    return total;
  }

  Widget get _supportPerson {
    return Padding(
      padding: const EdgeInsets.all(SizeUtils.kSize16),
      child: BlocBuilder<WealthPlanCubit, WealthPlanState>(
        buildWhen:
            (previous, current) =>
                previous.brokerInfo != current.brokerInfo ||
                previous.messageValidateBroker !=
                    current.messageValidateBroker ||
                previous.showBrokerInfo != current.showBrokerInfo,
        bloc: _cubit,
        builder: (context, state) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Bạn có người giới thiệu?',
                    style: vpTextStyle.subtitle16?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                  ),
                  CupertinoSwitch(
                    value: state.showBrokerInfo,
                    onChanged: (value) => _cubit.changeShowBrokerInfo(value),
                  ),
                ],
              ),
              if (state.showBrokerInfo) const SizedBox(height: 12),
              Visibility(
                visible: state.showBrokerInfo,
                child: VPTextField.medium(
                  inputFormatters: [
                    FilteringTextInputFormatter.deny(RegExp(r'\s{0,}')),
                  ],
                  hintText: 'Nhập mã giới thiệu',
                  controller: _cubit.textAccNoBrokerController,
                  keyboardType: TextInputType.text,
                  maxLength: 6,
                  autofocus: false,
                  caption: (color) {
                    return Visibility(
                      visible:
                          !(state.messageValidateBroker.isNullOrEmpty &&
                              state.brokerInfo?.fullName == null),
                      child: Row(
                        children: [
                          state.messageValidateBroker.isNullOrEmpty
                              ? Assets.icons.successCircle.svg().paddingRight8()
                              : Assets.icons.errorTriangle
                                  .svg()
                                  .paddingRight8(),
                          Text(
                            '${state.messageValidateBroker.isNullOrEmpty ? state.brokerInfo?.fullName : state.messageValidateBroker}',
                            style: vpTextStyle.captionRegular?.copyWith(
                              color:
                                  state.messageValidateBroker.isNullOrEmpty
                                      ? vpColor.textBrand
                                      : vpColor.textAccentRed,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                  prefixIcon:
                      (color) =>
                          SizedBox(
                            width: 40,
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                '116C',
                                style: vpTextStyle.body14?.copyWith(
                                  color: vpColor.textTertiary,
                                ),
                              ),
                            ),
                          ).paddingLeft8(),
                  onChanged: (value) => _cubit.onValidateBrokerInfo(value),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget get _headerContractAndTerm {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: SizeUtils.kSize16),
      child: Column(
        children: [
          kSpacingHeight16,
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              'Điều khoản và điều kiện',
              style: vpTextStyle.subtitle16?.copyWith(
                color: vpColor.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget get _contractAndTerm {
    return BlocBuilder<WealthPlanCubit, WealthPlanState>(
      bloc: _cubit,
      buildWhen: (previous, current) => previous.contract != current.contract,
      builder: (context, state) {
        return ExpansionTile(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Đăng ký kế hoạch',
                style: vpTextStyle.subtitle16?.copyWith(
                  color: vpColor.textPrimary,
                ),
              ),
              // if (state.requestRegistrationIdContract != null)
              Opacity(
                opacity: state.requestRegistrationIdContract != null ? 1 : 0.5,
                child: InkWell(
                  onTap: () {
                    if (state.requestRegistrationIdContract != null) {
                      _cubit.downloadContract(context);
                    }
                  },
                  child: Assets.icons.icDownload.svg(
                    color: vpColor.iconPrimary,
                  ),
                ),
              ),
            ],
          ),
          shape: const Border(),
          tilePadding: const EdgeInsets.symmetric(
            horizontal: SizeUtils.kSize16,
          ),
          iconColor: themeData.primary,
          collapsedIconColor: vpColor.iconPrimary,
          children: <Widget>[
            SizedBox(
              height: MediaQuery.of(context).size.height / 2,
              child: FutureBuilder<Uint8List?>(
                future: _cubit.getContractData(),
                builder: (cxt, snapshot) {
                  if (snapshot.hasData && snapshot.data != null) {
                    return SfPdfViewer.memory(
                      snapshot.data!,
                      scrollDirection: PdfScrollDirection.horizontal,
                      onDocumentLoaded: (value) {},
                      onDocumentLoadFailed: (value) {},
                      canShowScrollHead: false,
                    );
                  } else if (snapshot.hasError) {
                    return const Center(child: VPBankLoading());
                  }
                  return const SizedBox.shrink();
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget get _registrationContractAndTerm {
    return BlocBuilder<WealthPlanCubit, WealthPlanState>(
      bloc: _cubit,
      buildWhen:
          (previous, current) =>
              previous.registrationContract != current.registrationContract,
      builder: (context, state) {
        return ExpansionTile(
          backgroundColor: vpColor.backgroundElevation0,
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Đăng ký sử dụng sản phẩm',
                style: vpTextStyle.subtitle16?.copyWith(
                  color: vpColor.textPrimary,
                ),
              ),
              Opacity(
                opacity: state.requestRegistrationIdContract != null ? 1 : 0.5,
                child: InkWell(
                  onTap: () {
                    if (state.requestRegistrationIdContract != null) {
                      _cubit.downloadRegistrationContract(context);
                    }
                  },
                  child: Assets.icons.icDownload.svg(
                    color: vpColor.iconPrimary,
                  ),
                ),
              ),
            ],
          ),
          shape: const Border(),
          tilePadding: const EdgeInsets.symmetric(
            horizontal: SizeUtils.kSize16,
          ),
          iconColor: themeData.primary,
          collapsedIconColor: vpColor.iconPrimary,
          children: <Widget>[
            Container(
              color: vpColor.backgroundElevation0,
              height: MediaQuery.of(context).size.height / 2,
              child: FutureBuilder<Uint8List?>(
                future: _cubit.getRegistrationContractData(),
                builder: (cxt, snapshot) {
                  if (snapshot.hasData && snapshot.data != null) {
                    return SfPdfViewer.memory(
                      snapshot.data!,
                      scrollDirection: PdfScrollDirection.horizontal,
                      onDocumentLoaded: (value) {},
                      onDocumentLoadFailed: (value) {},
                      canShowScrollHead: false,
                    );
                  } else if (snapshot.hasError) {
                    return const Center(child: VPBankLoading());
                  }
                  return const SizedBox.shrink();
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _itemRowStockInvestment({
    required String name,
    required String moneyInitial,
    required String moneyPeriodic,
    bool isTotal = false,
    num rate = 0,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              name,
              style: vpTextStyle.subtitle14?.copyWith(
                color: vpColor.textPrimary,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              textAlign: TextAlign.end,
              '$rate%',
              style: vpTextStyle.body14?.copyWith(color: vpColor.textPrimary),
            ),
          ),
          Expanded(
            flex: 4,
            child: Text(
              moneyInitial,
              textAlign: TextAlign.end,
              style: vpTextStyle.body14?.copyWith(color: vpColor.textPrimary),
            ),
          ),
          Expanded(
            flex: 4,
            child: Text(
              moneyPeriodic,
              textAlign: TextAlign.end,
              style: vpTextStyle.body14?.copyWith(color: vpColor.textPrimary),
            ),
          ),
        ],
      ),
    );
  }

  Widget _itemRowText({required String title, String? value}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: vpTextStyle.body14?.copyWith(color: vpColor.textTertiary),
        ),
        Text(
          value ?? '--',
          textAlign: TextAlign.end,
          style: vpTextStyle.subtitle14?.copyWith(color: vpColor.textPrimary),
        ),
      ],
    ).paddingBottom8();
  }

  Widget get _divider {
    return SizedBox(
      height: 2,
      width: double.infinity,
      child: ColoredBox(color: vpColor.strokeNormal),
    );
  }

  Widget _warning() {
    return Container(
      decoration: BoxDecoration(
        color: vpColor.backgroundAccentYellow.withOpacity(0.16),
        borderRadius: const BorderRadius.all(Radius.circular(8)),
      ),
      child: Row(
        children: [
          Assets.icons.dangerTriangle.svg().paddingRight8(),
          kSpacingWidth4,
          Flexible(
            child: Text(
              WealthStock.current.coutionInfoPlan,
              style: vpTextStyle.captionMedium?.copyWith(
                color: vpColor.textPrimary,
              ),
            ),
          ),
        ],
      ).paddingSymmetric(horizontal: 12, vertical: 12),
    );
  }
}
