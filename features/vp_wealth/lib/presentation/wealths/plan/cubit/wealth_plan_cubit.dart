import 'dart:typed_data';

import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/data/enum/investment_frequency.dart';
import 'package:vp_wealth/data/enum/symbol_type_enum.dart';
import 'package:vp_wealth/data/enum/target_fitler_enum.dart';
import 'package:vp_wealth/data/model/category/category_model.dart';
import 'package:vp_wealth/data/model/chart/chart_data_category_performance.dart';
import 'package:vp_wealth/data/model/contract/wealth_contract_model.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/data/utils/loading_utils.dart';
import 'package:vp_wealth/domain/entity/item_expected_performance.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/generated/assets.gen.dart' as wealthAssets;
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/model/broker_model.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/place_order_bloc_map_import.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/remove_wealth_dialog.dart';
import 'package:vp_wealth/router/wealth_router.dart';
import 'package:vp_wealth/wealth_data/wealth_data.dart';

part 'wealth_plan_state.dart';

class WealthPlanCubit extends Cubit<WealthPlanState> {
  WealthPlanCubit() : super(WealthPlanState());

  final TextEditingController textEditNamePlanController =
      TextEditingController(text: 'Kế hoạch của tôi');
  final TextEditingController textInitialInvestmentAmountController =
      TextEditingController();
  final TextEditingController textMonthlyIncomeController =
      TextEditingController();
  final TextEditingController textPeriodicInvestmentAmountController =
      TextEditingController();
  final TextEditingController textInvestmentTimeController =
      TextEditingController(text: '5');
  final TextEditingController textAccNoBrokerController =
      TextEditingController();

  Future<void> onInitialData(PlanModel? plan) async {
    onInitInvestmentStartDay();
    await onFetchListCategoryByType(plan);
    if (plan != null) {
      if ((plan.initialInvestment ?? 0) == 0) {
        textInitialInvestmentAmountController.text = '';
      } else {
        textInitialInvestmentAmountController.text =
            (plan.initialInvestment ?? 0).valueText.replaceAll(' đ', '');
        textInitialInvestmentAmountController
            .selection = TextSelection.collapsed(
          offset: textInitialInvestmentAmountController.text.length - 4,
        );
      }

      textMonthlyIncomeController.text = plan.investment.valueText.replaceAll(
        ' đ',
        '',
      );
      textMonthlyIncomeController.selection = TextSelection.collapsed(
        offset: textMonthlyIncomeController.text.length - 4,
      );
      textInvestmentTimeController.text = plan.investmentTime.toString();
      textEditNamePlanController.text = plan.name;
      emit(
        state.copyWith(
          wealthTarget: plan.goal,
          namePlan: plan.name.trim(),
          investmentFrequency: plan.investmentFrequency,
          investmentStartDay: plan.convertStartDateToDateTime,
          listStockCustomCategory: plan.itemList,
          copierID: plan.id,
          valueInvestmentTime: plan.investmentTime,
        ),
      );
      onCalculateExpectedPerformance();
    }
  }

  void editNamePlan(String name) {
    emit(state.copyWith(namePlan: name));
  }

  void resetNamePlan() {
    textEditNamePlanController.text =
        state.namePlan ?? '${WealthStock.current.plan} 01';
  }

  void changeWealthTarget(TargetFilterEnum wealthTarget) {
    emit(state.copyWith(wealthTarget: wealthTarget));
  }

  void onValidateEditNamePlan(String value) {
    if (value.trim().isNotEmpty) {
      emit(state.copyWith(showErrorEditNamePlan: false));
      return;
    }
    emit(state.copyWith(showErrorEditNamePlan: true));
  }

  void onShowSuggestInitialInvestmentMoney(bool isShow) {
    emit(state.copyWith(showSuggestInitialInvestmentMoney: isShow));
  }

  void onShowSuggestPeriodicInvestmentMoney(bool isShow) {
    emit(state.copyWith(showSuggestPeriodicInvestmentMoney: isShow));
  }

  void updateRateInvestment(double valueRateInvestment) {
    emit(state.copyWith(valueRateInvestment: valueRateInvestment));
    if (textPeriodicInvestmentAmountController.text.isEmpty) return;
    calculatePeriodicInvestmentAmount();
  }

  void updateInvestmentTime(num valueInvestmentTime) {
    if (valueInvestmentTime == 0) valueInvestmentTime = 1;
    textInvestmentTimeController.text = valueInvestmentTime.toInt().toString();
    emit(state.copyWith(valueInvestmentTime: valueInvestmentTime));
    onCalculateExpectedPerformance();
  }

  void calculateSliderInvestmentTimeWhenInput(String value) {
    if (value.isNotEmpty) {
      int valueInvestmentTime = int.parse(value);
      if (valueInvestmentTime < 1 || valueInvestmentTime > 100) {
        textInvestmentTimeController.text = '';
        emit(state.copyWith(valueInvestmentTime: 0));
        return;
      }
      emit(
        state.copyWith(
          valueInvestmentTime: valueInvestmentTime,
          showErrorInvestmentTime: value.isEmpty,
        ),
      );
      onCalculateExpectedPerformance();
      return;
    }
    emit(
      state.copyWith(
        valueInvestmentTime: 0,
        showErrorInvestmentTime: value.isEmpty,
      ),
    );
    onCalculateExpectedPerformance();
  }

  void calculateMonthlyIncome() {
    num periodicInvestmentAmount =
        num.tryParse(
          textPeriodicInvestmentAmountController.text.replaceAll(',', ''),
        ) ??
        0;
    num monthlyIncome =
        periodicInvestmentAmount / state.valueRateInvestment.toPrecision(2);

    textMonthlyIncomeController.text = monthlyIncome.toFormat3();

    emit(state.copyWith(monthlyIncome: monthlyIncome));
  }

  void calculateInitialInvestmentAmount() {
    emit(state.copyWith(showErrorInitialIncome: false));
    onCalculateExpectedPerformance();
  }

  void calculatePeriodicInvestmentAmount() {
    num monthlyIncome =
        num.tryParse(textMonthlyIncomeController.text.replaceAll(',', '')) ?? 0;
    num periodicInvestmentAmount =
        monthlyIncome * state.valueRateInvestment.toPrecision(2);

    textPeriodicInvestmentAmountController.text =
        periodicInvestmentAmount.toFormat3();
    emit(
      state.copyWith(
        periodicInvestmentAmount: periodicInvestmentAmount,
        showErrorMonthlyIncome: textMonthlyIncomeController.text.isEmpty,
      ),
    );
    onCalculateExpectedPerformance();
  }

  void updateInvestmentStartDay(DateTime investmentStartDay) {
    chooseInvestmentFrequency(
      state.investmentFrequency,
      investmentStartDay: investmentStartDay,
    );
    emit(state.copyWith(investmentStartDay: investmentStartDay));
    onCalculateExpectedPerformance();
  }

  void chooseInvestmentFrequency(
    InvestmentFrequency investmentFrequency, {
    DateTime? investmentStartDay,
  }) {
    AppKeyboardUtils.dismissKeyboard();
    if (investmentFrequency == InvestmentFrequency.quarterly) {
      int currentMonth =
          (investmentStartDay ?? state.investmentStartDay)!.month;
      List<List<int>> allNumbers = [
        [1, 4, 7, 10],
        [2, 5, 8, 11],
        [3, 6, 9, 12],
      ];
      final matchingNumbers = allNumbers.firstWhere(
        (numbers) => numbers.contains(currentMonth),
      );
      emit(state.copyWith(listMonthOfQuarterly: matchingNumbers));
    }
    emit(state.copyWith(investmentFrequency: investmentFrequency));
    onCalculateExpectedPerformance();
  }

  void chooseInvestmentCategory(CategoryModel item) async{

    final List<ItemData> data =
        item.masterPortfolios?.isNotEmpty ?? false
            ? item.masterPortfolios!.first.listItemData
            : state.listStockCustomCategory;

    final expectedProfit =
        data.fold<double>(
          0,
          (total, item) =>
              total +
              item.allocation *
                  (WealthData().allStocksWealth
                          .firstWhere(
                            (stock) => stock.symbol == item.symbol,
                            orElse:
                                () => ItemData(
                                  symbol: '',
                                  symbolType: SymbolTypeEnum.stock,
                                  allocation: 0,
                                ),
                          )
                          .rateOfReturn ??
                      0),
        ) /
        data.fold<double>(0, (total, item) => total + item.allocation);
    emit(
      state.copyWith(
        investmentCategorySelected: item,
        expectedProfit: expectedProfit,
      ),
    );
    onCalculateExpectedPerformance();
  }

  void onInitInvestmentStartDay() {
    DateTime initDay = DateTime.now().copyWith(day: DateTime.now().day + 1);
    emit(state.copyWith(investmentStartDay: initDay));
  }

  Future<void> onFetchListCategoryByType(PlanModel? plan) async {
    try {
      final listCategory =
          await GetIt.instance.get<WealthRepository>().getListCategoryByType();
      final categorySuggestion = await onGetSampleCategory();
      CategoryModel custom = CategoryModel(
        invPhilosophyType: 'CUSTOM',
        invPhilosophyTypeName: 'Tuỳ chỉnh',
      );

      double expectedProfit = (categorySuggestion?.masterPortfolios ?? [])
          .first
          .listItemData
          .fold<double>(
            0,
            (previousValue, element) =>
                previousValue +
                (element.allocation / 100) * (element.rateOfReturn ?? 0),
          );
      listCategory.add(custom);
      emit(
        state.copyWith(
          listCategory: listCategory,
          investmentCategorySelected:
              plan != null ? custom : categorySuggestion,
          expectedProfit: plan != null ? plan.expectedProfit : expectedProfit,
        ),
      );
    } catch (e) {
      showError(e);
    } finally {}
  }

  Future<CategoryModel?> onGetSampleCategory() async {
    try {
      final data =
          await GetIt.instance.get<WealthRepository>().getSampleCategory();
      return data;
    } catch (e) {
      showError(e);
      return null;
    } finally {}
  }

  void onCreateDraftPlan(Function(PlanModel plan) onContinue) async {
    try {
      final isCustom =
          state.listStockCustomCategory.isNotEmpty &&
          state.investmentCategorySelected?.invPhilosophyType == 'CUSTOM';
      final listStock =
          isCustom
              ? state.listStockCustomCategory
              : (state
                      .investmentCategorySelected
                      ?.masterPortfolios
                      ?.first
                      .listItemData ??
                  []);
      PlanModel plan = PlanModel(
        goal: state.wealthTarget,
        investmentFrequency: state.investmentFrequency,
        startDate:
            (state.investmentStartDay ?? DateTime.now()).formatToDdMmYyyy(),
        name: textEditNamePlanController.text.trim(),
        investment: textMonthlyIncomeController.text.volume.toInt(),
        initialInvestment:
            textInitialInvestmentAmountController.text.volume.toInt(),
        investmentTime: textInvestmentTimeController.text.volume.toInt(),
        itemList: listStock,
      );
      bool showErrorMonthlyIncome =
          !validateInvestMentAmountCreateDraftPlan(plan);

      bool showErrorInitialIncome =
          !validateInitialInvestmentCreateDraftPlan(plan);
      if (showErrorMonthlyIncome || showErrorInitialIncome) {
        showDialogWarningAmount(
          GetIt.instance<NavigationService>().navigatorKey.currentContext!,
          () {},
        );
        emit(
          state.copyWith(
            showErrorMonthlyIncome: showErrorMonthlyIncome,
            showErrorInitialIncome: showErrorInitialIncome,
          ),
        );
        return;
      }

      if (validateCreateDraftPlan(plan)) return;
      LoadingUtil.showLoading();
      if (state.copierID != null) {
        await GetIt.instance.get<WealthRepository>().updateDraftPLan(
          plan.copyWith(id: state.copierID),
        );
      } else {
        final result = await GetIt.instance
            .get<WealthRepository>()
            .createDraftPLan(plan);
        emit(state.copyWith(copierID: result));
      }

      final requestID = await GetIt.instance
          .get<WealthRepository>()
          .previewContract(plan.copyWith(id: state.copierID));
      final requestRegistrationID = await GetIt.instance
          .get<WealthRepository>()
          .previewRegistrationContract(plan.copyWith(id: state.copierID));
      LoadingUtil.hideLoading();
      onContinue(plan);
      emit(
        state.copyWith(
          planModel: plan,
          requestIdContract: requestID,
          requestRegistrationIdContract: requestRegistrationID,
        ),
      );
    } catch (e) {
      if (e is ResponseError) {
        showMessage(e.message, isSuccess: false);
      }
    } finally {
      LoadingUtil.hideLoading();
    }
  }

  void onValidateBrokerInfo(String accountNo) async {
    try {
      if (accountNo.isEmpty) {
        state.brokerInfo = null;
        emit(state.copyWith(messageValidateBroker: '', brokerInfo: null));
        return;
      }
      if (accountNo.length < 6) {
        state.brokerInfo = null;
        emit(
          state.copyWith(
            messageValidateBroker: 'Mã người giới thiệu không hợp lệ',
            brokerInfo: null,
          ),
        );
        return;
      }
      BrokerModel info = await GetIt.instance
          .get<WealthRepository>()
          .getBrokerInfo('116C$accountNo');
      emit(state.copyWith(brokerInfo: info, messageValidateBroker: ''));
    } catch (e) {
      if (e is ResponseError) {
        if (e.code == 'IABERR29') return;
        emit(
          state.copyWith(
            messageValidateBroker: e.message ?? '',
            brokerInfo: null,
          ),
        );
        return;
      }
      String? message = await getErrorMessage(e);
      emit(
        state.copyWith(messageValidateBroker: message ?? '', brokerInfo: null),
      );
    }
  }

  void onActivePlan() async {
    try {
      LoadingUtil.showLoading();
      int copierId = state.copierID ?? 0;
      String brokerNo = state.brokerInfo?.accountNo ?? '';

      if (!brokerNo.isNullOrEmpty) {
        await GetIt.instance.get<WealthRepository>().updateBrokerNo(
          copierId: copierId,
          brokerNo: brokerNo,
        );
      }

      var result = await GetIt.instance.get<WealthRepository>().activePlan(
        copierId: copierId,
      );
      if (result.isSuccess()) {
        LoadingUtil.hideLoading();
        bool isHaveInitialInvestmentAmount =
            textInitialInvestmentAmountController.text.isNotEmpty;
        GetIt.instance<NavigationService>().navigatorKey.currentContext!.push(
          WealthRouter.createPlanSuccessPage,
          extra: isHaveInitialInvestmentAmount,
        );
      }
    } catch (e) {
      LoadingUtil.hideLoading();
      showError(e);
    }
  }

  void closeNoteMyCategory() {
    emit(state.copyWith(showNoteMyCategory: false));
  }

  void updateCustomCategory(List<ItemData> listStock) {
    emit(
      state.copyWith(
        listStockCustomCategory: listStock,
        showErrorInitialIncome: false,
        showErrorMonthlyIncome: false,
      ),
    );
    chooseInvestmentCategory(state.listCategory.last);
  }

  String buildInvestmentSchedule() {
    return state.listMonthOfQuarterly.map((month) => ' $month').join(', ');
  }

  bool validateInitialInvestmentCreateDraftPlan(PlanModel plan) {
    if ((plan.initialInvestment ?? 0) != 0) {
      for (ItemData e in plan.itemList) {
        double investedAmount =
            (e.allocation / 100) * (plan.initialInvestment ?? 0);
        if (investedAmount < 100000) {
          return false;
        }
      }
    }
    return true;
  }

  bool validateInvestMentAmountCreateDraftPlan(PlanModel plan) {
    for (ItemData e in plan.itemList) {
      double investedAmount = (e.allocation / 100) * (plan.investment);
      if (investedAmount < 100000) {
        return false;
      }
    }
    return true;
  }

  bool validateCreateDraftPlan(PlanModel plan) {
    num minInvestmentAmount = 100000;
    num totalRate = 0;
    String errorMessage = '';

    bool isValidInitialInvestment() => (plan.initialInvestment ?? 0) > 0;
    bool isValidPlanName() => plan.name.isNotEmpty;
    bool isValidPeriodicInvestment() => plan.investment > 0;
    bool isValidInvestmentTime() => plan.investmentTime != 0;
    bool hasItemsInList() => plan.itemList.isNotEmpty;
    bool isAfterDateNow() => state.investmentStartDay!.isAfter(DateTime.now());

    if (!isValidPlanName()) {
      errorMessage = 'Tên kế hoạch không hợp lệ';
    } else if (!isValidInitialInvestment() &&
        (plan.initialInvestment ?? 0) != 0) {
      errorMessage = 'Số tiền đầu tư ban đầu không hợp lệ';
    } else if (!isValidPeriodicInvestment()) {
      errorMessage = 'Số tiền đầu tư định kỳ không hợp lệ';
    } else if (!isValidInvestmentTime()) {
      errorMessage = 'Thời gian đầu tư không hợp lệ';
    } else if (!hasItemsInList()) {
      errorMessage = 'Danh mục đầu tư không được để trống';
    } else if (!isAfterDateNow()) {
      errorMessage = 'Ngày bắt đầu đầu tư định kỳ phải lớn hơn ngày hiện tại';
    } else {
      for (var item in plan.itemList) {
        if (item.allocation == 0) {
          errorMessage = 'Tỷ trong mã chứng khoán phải lớn hơn 0';
          break;
        }
        totalRate += item.allocation;
      }

      if (errorMessage.isEmpty && totalRate != 100) {
        errorMessage = 'Tổng tỷ trọng danh mục phải bằng 100%';
      }
    }

    if (errorMessage.isNotEmpty) {
      showErrorMessage(errorMessage);
    }

    return errorMessage.isNotEmpty;
  }

  void onGetLinkContract() async {
    try {
      String requestID = state.requestIdContract ?? '';
      WealthContractModel result = await GetIt.instance
          .get<WealthRepository>()
          .getContractByRequestId(requestID);

      if (!result.presignedUrl.isNullOrEmpty) {
        emit(state.copyWith(contract: result));
      }
    } catch (e) {
      showError(e);
    }
  }

  void onGetLinkRegistrationContract() async {
    try {
      String requestID = state.requestRegistrationIdContract ?? '';
      WealthContractModel result = await GetIt.instance
          .get<WealthRepository>()
          .getContractByRequestId(requestID);

      if (!result.presignedUrl.isNullOrEmpty) {
        emit(state.copyWith(registrationContract: result));
      }
    } catch (e) {
      showError(e);
    }
  }

  Future<Uint8List?> getContractData() async {
    try {
      if (DownloadUtils().checkUrlValid(state.contract?.presignedUrl ?? '')) {
        Uint8List? data = await DownloadUtils().getUrlContent(
          state.contract?.presignedUrl ?? '',
        );
        if (data != null) {
          return data;
        }
      }
      return Future.error('');
    } catch (e) {
      return Future.error('');
    }
  }

  Future<Uint8List?> getRegistrationContractData() async {
    try {
      if (DownloadUtils().checkUrlValid(
        state.registrationContract?.presignedUrl ?? '',
      )) {
        Uint8List? data = await DownloadUtils().getUrlContent(
          state.registrationContract?.presignedUrl ?? '',
        );
        if (data != null) {
          return data;
        }
      }
      return Future.error('');
    } catch (e) {
      return Future.error('');
    }
  }

  void downloadContract(BuildContext context) async {
    final randomId = AppHelper().genXRequestID();
    final String tag = 'download_contract_$randomId';
    EasyDebounce.debounce(tag, const Duration(milliseconds: 1000), () async {
      try {
        showSnackBar(
          context,
          'đang tải',
          asset: 'FundKeyAssets.icDownload',
          colorAsset: themeData.black,
        );
        final String defaultFileName = (state.contract?.fileName ?? '')
            .replaceAll('.pdf', '');
        final downloadResult = await DownloadFileManager.instance.downloadFile(
          fileName: defaultFileName,
          extension: FileExtension.pdf,
          url: state.contract?.presignedUrl ?? '',
        );

        showSnackBar(
          context,
          downloadResult != null ? 'Tải thành công' : 'Có lỗi xảy ra',
          asset:
              downloadResult != null
                  ? wealthAssets.Assets.icons.successCircle.path
                  : Assets.icons.icFail.path,
        );
      } catch (e) {
        showSnackBar(context, 'Tải thất bại', asset: Assets.icons.icFail.path);
      }
    });
  }

  void downloadRegistrationContract(BuildContext context) async {
    final randomId = AppHelper().genXRequestID();
    final String tag = 'download_contract_$randomId';
    EasyDebounce.debounce(tag, const Duration(milliseconds: 1000), () async {
      try {
        showSnackBar(
          context,
          WealthStock.current.downloading,
          asset: wealthAssets.Assets.icons.icFunDownload.path,
          colorAsset: themeData.black,
        );
        final String defaultFileName =
            (state.registrationContract?.fileName ?? '').replaceAll('.pdf', '');
        final downloadResult = await DownloadFileManager.instance.downloadFile(
          fileName: defaultFileName,
          extension: FileExtension.pdf,
          url: state.registrationContract?.presignedUrl ?? '',
        );

        showSnackBar(
          context,
          downloadResult != null
              ? WealthStock.current.downloadSuccess
              : WealthStock.current.downloadFail,
          asset:
              downloadResult != null
                  ? wealthAssets.Assets.icons.successCircle.path
                  : Assets.icons.icFail.path,
        );
      } catch (e) {
        showSnackBar(
          context,
          WealthStock.current.downloadFail,
          asset: Assets.icons.icFail.path,
        );
      }
    });
  }

  void onCalculateExpectedPerformance() async {
    try {
      emit(state.copyWith(loading: true));
      PlanModel plan = PlanModel(
        goal: state.wealthTarget,
        investmentFrequency: state.investmentFrequency,
        startDate:
            (state.investmentStartDay ?? DateTime.now()).formatToDdMmYyyy(),
        name: textEditNamePlanController.text,
        investment: textMonthlyIncomeController.text.volume.toInt(),
        initialInvestment:
            textInitialInvestmentAmountController.text.volume.toInt(),
        investmentTime: textInvestmentTimeController.text.volume.toInt(),
        itemList: [],
        rateOfReturn: state.expectedProfit,
      );
      if (plan.investment <= 0 ||
          (plan.rateOfReturn ?? 0) <= 0 ||
          (plan.rateOfReturn ?? 0).isNaN ||
          plan.investmentTime == 0) {
        emit(
          state.copyWith(listChartDataCategoryPerformance: [], loading: false),
        );
        return;
      }
      final ExpectedPerformanceModel? result = await GetIt.instance
          .get<WealthRepository>()
          .calculateExpectedPerformance(plan);
      List<ChartDataCategoryPerformance> listExpectedPerformance = [];
      if (result != null) {
        for (var element in result.futureValues) {
          listExpectedPerformance.add(
            element.mapResponseToChartDataCategoryPerformance(),
          );
        }
      }
      emit(
        state.copyWith(
          listChartDataCategoryPerformance: listExpectedPerformance,
          currentAsset: result?.currentAmount ?? 0,
          loading: false,
        ),
      );
    } catch (e) {
      emit(state.copyWith(loading: false));
      showError(e);
    } finally {}
  }

  void acceptContract(bool? value) {
    emit(state.copyWith(acceptContract: value));
  }

  void changeShowBrokerInfo(bool value) {
    if ((state.messageValidateBroker ?? '').isNotEmpty) {
      textAccNoBrokerController.clear();
      emit(
        state.copyWith(
          showBrokerInfo: value,
          messageValidateBroker: '',
          brokerInfo: null,
        ),
      );
    } else {
      emit(state.copyWith(showBrokerInfo: value));
    }
  }

  void changeShowErrorMonthlyIncome() {
    emit(state.copyWith(showErrorMonthlyIncome: false));
  }
}
