import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/generated/assets.gen.dart';

class ItemStocksWidget extends StatelessWidget {
  const ItemStocksWidget({
    Key? key,
    this.showIcon = true,
    required this.model,
    required this.onPressed,
    required this.onTap,
  }) : super(key: key);
  final ItemData model;
  final bool showIcon;
  final VoidCallback? onPressed;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: SizeUtils.kSize16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    model.symbol,
                    style: vpTextStyle.body14?.copyWith(
                      color: themeData.gray700,
                    ),
                  ),
                  Text(
                    model.business ?? '',
                    style: vpTextStyle.captionRegular?.copyWith(
                      color: vpColor.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            if (showIcon) ...[
              kSpacingHeight8,
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF2A3346).withOpacity(0.03),
                      spreadRadius: -8,
                      blurRadius: 24,
                      offset: const Offset(0, 24),
                    ),
                    BoxShadow(
                      color: const Color(0xFF2A3346).withOpacity(0.03),
                      spreadRadius: -2.5,
                      blurRadius: 5,
                      offset: const Offset(0, 5),
                    ),
                    BoxShadow(
                      color: const Color(0xFF2A3346).withOpacity(0.04),
                      spreadRadius: -1.5,
                      blurRadius: 3,
                      offset: const Offset(0, 3),
                    ),
                    BoxShadow(
                      color: const Color(0xFF2A3346).withOpacity(0.04),
                      spreadRadius: -1,
                      blurRadius: 2,
                      offset: const Offset(0, 2),
                    ),
                    BoxShadow(
                      color: const Color(0xFF0E3F7E).withOpacity(0.06),
                      spreadRadius: 1,
                      blurRadius: 0,
                      offset: const Offset(0, 0),
                    ),
                    BoxShadow(
                      color: const Color(0xFF2A3346).withOpacity(0.03),
                      spreadRadius: -0.5,
                      blurRadius: 1,
                      offset: const Offset(0, 1),
                    ),
                  ],
                  color: vpColor.backgroundElevation1,
                ),
                child: Center(
                  child: Assets.icons.icAdd.svg(width: 16, height: 16),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
