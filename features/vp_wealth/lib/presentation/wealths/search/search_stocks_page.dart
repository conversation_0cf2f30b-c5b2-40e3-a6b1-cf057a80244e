import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/stock_order_entity.dart';
import 'package:vp_wealth/presentation/wealths/search/app_search/app_search_data.dart';
import 'package:vp_wealth/presentation/wealths/search/app_search/app_search_input.dart';
import 'package:vp_wealth/presentation/wealths/search/bloc/search_cubit.dart';
import 'package:vp_wealth/presentation/wealths/search/bloc/search_stock_bloc.dart';
import 'package:vp_wealth/presentation/wealths/search/widget/item_stocks_widget.dart';

class SearchStocksPage extends StatefulWidget {
  final List<ItemData> dataList;

  final Function(ItemData item) onAddList;

  const SearchStocksPage({
    Key? key,
    required this.dataList,
    required this.onAddList,
  }) : super(key: key);

  @override
  State<SearchStocksPage> createState() => _SearchStocksPageState();
}

class _SearchStocksPageState extends State<SearchStocksPage> {
  late final _bloc = context.read<SearchStockBloc>();

  List<ItemData> get _dataList => widget.dataList;

  void onNavigate(String symbol) {
    context.pop(symbol);
  }

  @override
  void initState() {
    super.initState();
    _bloc.dataChooses = List<ItemData>.from(_dataList);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ColoredBox(
        color: themeData.bgPopup,
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(top: SizeUtils.kSize16),
              child: BlocBuilder<SearchStockBloc, SearchState>(
                builder:
                    (_, state) => AppSearchInput(
                      state: state,
                      prefixIcon: Icon(
                        Icons.search_outlined,
                        color: themeData.black,
                        size: SizeUtils.kSizeIcon24,
                      ),
                      onChanged: _bloc.onChanged,
                      onClear: _bloc.onClear,
                      onSubmitted:
                          (StockOrderEntity entity) =>
                              onNavigate(entity.symbol),
                      hintText: 'Nhập mã cổ phiếu',
                    ),
              ),
            ),
            kSpacingHeight12,
            BlocBuilder<SearchStockBloc, SearchState>(
              builder: (_, state) {
                if (state.filterEmpty) return const SizedBox.shrink();
                return _infoNote;
              },
            ),
            //  kSpacingHeight8,
            Expanded(
              child: BlocBuilder<SearchStockBloc, SearchState<ItemData>>(
                builder: (_, state) {
                  return AppSearchData<ItemData>(
                    state: state,
                    itemBuilder:
                        (item) => ItemStocksWidget(
                          model: item,
                          onPressed: () {
                            context.read<SearchStockBloc>().addList(
                              context,
                              item,
                            );
                            widget.onAddList(item);
                          },
                          onTap: () => onNavigate(item.symbol),
                        ),
                    onRetry: _bloc.refresh,
                    filterEmpty: Column(
                      children: [
                        context.read<SearchStockBloc>().symbolExist
                            ? Assets.icons.existFilter.svg()
                            : Assets.icons.emptyBox.svg(),
                        Text(
                          context.read<SearchStockBloc>().symbolExist
                              ? WealthStock
                                  .current
                                  .emptyListSearchWhenSymbolExist
                              : WealthStock
                                  .current
                                  .emptyListSearchWhenNoResultMatching,
                          textAlign: TextAlign.center,
                          style: vpTextStyle.body14?.copyWith(
                            color: themeData.black,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            kSpacingHeight16,
          ],
        ),
      ),
    );
  }

  Widget get _infoNote {
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: SizeUtils.kSize8,
        horizontal: SizeUtils.kSize12,
      ),
      decoration: BoxDecoration(
        color: themeData.blue.withOpacity(0.16),
        borderRadius: BorderRadius.circular(SizeUtils.kRadius8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Assets.icons.icInfoCircle.svg(color: themeData.blue),
          kSpacingWidth8,
          Expanded(
            child: Text(
              WealthStock.current.symbolSuggestionByVPBankS,
              style: vpTextStyle.captionMedium?.copyWith(
                color: vpColor.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
