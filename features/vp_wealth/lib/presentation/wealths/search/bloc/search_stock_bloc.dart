import 'package:flutter/cupertino.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/presentation/wealths/search/bloc/search_cubit.dart';
import 'package:vp_wealth/wealth_data/wealth_data.dart';

class SearchStockBloc extends SearchCubit<ItemData> {
  SearchStockBloc()
    : super(
        queries: [(e) => e.symbol],
        sorts: [
          (e, filterText) => e.symbol.indexOf(filterText),
          (e, _) => e.symbol.length,
          (e, _) => e.symbol,
        ],
      );

  List<ItemData> dataChooses = [];

  void addList(BuildContext context, ItemData model) async {
    dataChooses.add(model);
    refresh();
  }

  bool get symbolExist => dataChooses
      .map((e) => e.symbol)
      .toList()
      .contains(state.query.toUpperCase());

  @override
  Future<List<ItemData>> getData() async {
    return WealthData().allStocksWealth
        .where(
          (e) => !dataChooses.map((e) => e.symbol).toList().contains(e.symbol),
        )
        .toList();
  }

  void updateQuery(String symbol) {
    emit(state.copyWith(query: symbol));
  }
}
