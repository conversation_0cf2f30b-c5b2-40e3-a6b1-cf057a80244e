import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/common/utils/stock_utils.dart';
import 'package:vp_wealth/presentation/place_order/model/securities_portfolio_responses_model.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/domain/entity/stock_portfolio_entity.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/domain/stock_common_repo.dart';

mixin StockAssetHelper {
  final commonRepo = GetIt.instance.get<StockCommonRepo>();

  SubAccountModel? get normalAccount =>
      GetIt.instance.get<SubAccountCubit>().normalAccount;

  SubAccountModel? get marginAccount =>
      GetIt.instance.get<SubAccountCubit>().marginAccount;

  List<SubAccountModel> get accounts =>
      GetIt.instance.get<SubAccountCubit>().subAccountsStock
        ..removeWhere((e) => e.id.isNullOrEmpty);

  Future<List<StockPortfolioEntity>> getStockHoldingBySubAccount(
    SubAccountType subAccountType, {
    bool handleDuplicate = false,
    bool getExactPriceFromIV = true,
  }) async {
    final ids = _getAccountIds(subAccountType);

    if (ids.isNullOrEmpty) return [];

    /// Lấy từ một tiều khoản
    if (ids.length == 1) {
      return commonRepo.getSecuritiesPortfolio(
        ids.first,
        getExactPriceFromIV: getExactPriceFromIV,
      );
    }

    /// lấy tất cả tiểu khoản
    final values = await Future.wait(
      ids.map(
        (e) => commonRepo.getSecuritiesPortfolio(e, getExactPriceFromIV: false),
      ),
      eagerError: true,
    );

    /// Sắp xếp lại mã
    var securities = values.fold<List<StockPortfolioEntity>>(
      [],
      (a, b) => [...a, ...b],
    );

    if (securities.isNullOrEmpty) return securities;

    if (getExactPriceFromIV) {
      try {
        final symbols = securities.map((e) => e.symbol).toSet().toList();

        /// lấy thông tin chi tiết về mã
        final stocks = await commonRepo.getStocks(symbols.symbolsFormat);

        securities =
            securities
                .map(
                  (e) => e.copyWith(
                    stocks.firstWhereOrNull(
                      (stock) => stock.symbol == e.symbol,
                    ),
                  ),
                )
                .toList();
      } catch (e, stackTrace) {
        debugPrintStack(stackTrace: stackTrace);
      }
    }

    if (handleDuplicate) {
      /// remove duplicate and handle new totalVol, costPrice
      return StockUtils.handleDuplicatedStockHolding(securities);
    }

    return securities;
  }

  Future<List<SecuritiesPortfolioResponsesModel>> getStockHoldingBySubAccountV2(
    SubAccountType subAccountType, {
    bool getExactPriceFromIV = true,
  }) async {
    final ids = _getAccountIds(subAccountType);

    if (ids.isNullOrEmpty) return [];

    /// Lấy từ một tiều khoản
    if (ids.length == 1) {
      return commonRepo.getSecuritiesPortfolioV2(
        ids.first,
        getExactPriceFromIV: getExactPriceFromIV,
      );
    }

    /// lấy tất cả tiểu khoản
    final values = await Future.wait(
      ids.map(
        (e) =>
            commonRepo.getSecuritiesPortfolioV2(e, getExactPriceFromIV: false),
      ),
      eagerError: true,
    );

    /// Sắp xếp lại mã
    final securities = values.fold<List<SecuritiesPortfolioResponsesModel>>(
      [],
      (a, b) => [...a, ...b],
    );

    if (!getExactPriceFromIV || securities.isNullOrEmpty) return securities;

    try {
      final symbols = securities.map((e) => e.symbol).toSet().toList();

      /// lấy thông tin chi tiết về mã
      final stocks = await commonRepo.getStocks(symbols.symbolsFormat);

      return securities
          .map(
            (e) => e.copyWith(
              stocks.firstWhereOrNull((stock) => stock.symbol == e.symbol),
            ),
          )
          .toList();
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }

    return securities;
  }

  List<String> _getAccountIds(SubAccountType subAccountType) {
    if (accounts.isNullOrEmpty) return [];

    if (subAccountType == SubAccountType.bond) return [];

    final ids = <String>[];

    final normalId = normalAccount?.id ?? '';
    final marginId = marginAccount?.id ?? '';

    if (subAccountType == SubAccountType.normal) {
      ids.addIf(normalId, addIf: () => normalId.hasData);
    }

    if (subAccountType == SubAccountType.margin) {
      ids.addIf(marginId, addIf: () => marginId.hasData);
    }

    if (subAccountType == SubAccountType.all) {
      ids
        ..addIf(normalId, addIf: () => normalId.hasData)
        ..addIf(marginId, addIf: () => marginId.hasData);
    }

    return ids;
  }

  List<StockPortfolioEntity> filterStockBySubAccount({
    required List<StockPortfolioEntity> stocks,
    required SubAccountType type,
    SubAccountModel? subAccDefault,
  }) {
    if (type == SubAccountType.all) {
      return StockUtils.handleDuplicatedStockHolding(stocks);
    }

    if (type == SubAccountType.normal || type == SubAccountType.margin) {
      final subAccount =
          GetIt.instance.get<SubAccountCubit>().getSubAccount(type) ??
          subAccDefault;

      return StockUtils.handleDuplicatedStockHolding(
        stocks.where((e) => e.accountId == subAccount!.id).toList(),
      );
    }

    return const [];
  }
}
