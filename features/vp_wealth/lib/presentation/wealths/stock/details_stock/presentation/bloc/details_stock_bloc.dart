import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/common/utils/stock_utils.dart';
import 'package:vp_wealth/data/enum/period.dart';
import 'package:vp_wealth/data/model/chart_price_entity.dart';
import 'package:vp_wealth/data/model/stock_detail_entity.dart';
import 'package:vp_wealth/data/utils/time_utils.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/exchange.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/edit_plan/stock_changed_properties.dart';
import 'package:vp_wealth/presentation/wealths/socket/investment_tool_socket_connect.dart';
import 'package:vp_wealth/presentation/wealths/socket/model/investment_tool_oddlot_stock_data.dart';
import 'package:vp_wealth/presentation/wealths/socket/model/investment_tool_socket_stock_data.dart';
import 'package:vp_wealth/presentation/wealths/socket/socket_subscriber.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/data/enum/timeline_type.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/data/models/chart_model.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/domain/entity/stock_portfolio_entity.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/presentation/bloc/mixin/stock_asset_helper.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/presentation/bloc/mixin/stream_check_mixin.dart';

part 'details_stock_event.dart';
part 'details_stock_state.dart';

List<ChartModel> listRelatedStocksData = [
  ChartModel(x: 1, y: 5),
  ChartModel(x: 2, y: 20),
  ChartModel(x: 3, y: 25),
  ChartModel(x: 4, y: 25),
  ChartModel(x: 5, y: 50),
  ChartModel(x: 6, y: 45),
  ChartModel(x: 7, y: 20),
  ChartModel(x: 8, y: 10),
  ChartModel(x: 9, y: 0),
  ChartModel(x: 10, y: 5),
  ChartModel(x: 11, y: 10),
  ChartModel(x: 12, y: 10),
  ChartModel(x: 13, y: 15),
];

class DetailsStockBloc extends Cubit<DetailsStockState>
    with StreamCheckMixin, StockAssetHelper {
  DetailsStockBloc() : super(DetailsStockInitial());

  late String symbol;

  // final _homeRepo = GetIt.instance.get<HomeRepository>();

  final _stockRepo = GetIt.instance.get<WealthRepository>();

  // final stockConfig = GetIt.instance.get<StockBuildConfigs>();

  List<StockPortfolioEntity>? _listStockHolding = [];

  String selectedChartType = '1D';

  String get theme {
    final context =
        GetIt.instance<NavigationService>().navigatorKey.currentContext;
    bool isLight = context!.read<ThemeCubit>().currentTheme == AppTheme.dark;
    return isLight ? 'light' : 'dark';
  }

  String getTradingViewUrl(String timeline, {bool fullScreen = false}) {
    return tradingWebviewStockDetail(
      symbol,
      timeline,
      theme: theme,
      fullScreen: fullScreen,
    );
  }

  final BehaviorSubject<bool> _stockLoadingController = BehaviorSubject();

  Stream<bool> get streamBasicDataLoading => _stockLoadingController.stream;

  /// PriceView
  final BehaviorSubject<bool> _priceViewController = BehaviorSubject();

  Stream<bool> get streamPriceViewController => _priceViewController.stream;

  /// Switch chart type
  final BehaviorSubject<bool> _isPriceChartType = BehaviorSubject.seeded(true);

  Stream<bool> get streamIsPriceChartType => _isPriceChartType.stream;

  /// Selected timeline type
  final BehaviorSubject<String> _selectedTimelineTradingView =
      BehaviorSubject.seeded(AppConstants.defaultChartTimeLine);

  Stream<String> get streamSelectedTimelineTradingView =>
      _selectedTimelineTradingView.stream;

  void sinkPriceView(bool value) {
    emitStreamData<bool>(_priceViewController, value);
  }

  //Stock detail
  final _stockItemController = BehaviorSubject<StockDetailEntity>();

  Stream<StockDetailEntity> get streamStockItem => _stockItemController.stream;

  final _closePriceController = BehaviorSubject<num?>();

  Stream<num?> get closePriceStream => _closePriceController.stream;

  num? get currentClosePrice => _closePriceController.valueOrNull;

  final _stockInfoController = BehaviorSubject<StockDetailEntity>();

  Stream<StockDetailEntity> get stockInfo => _stockInfoController.stream;

  //stock holding data
  final _stockPortfolioController = BehaviorSubject<StockPortfolioEntity?>();

  Stream<StockPortfolioEntity?> get streamStockPortfolio =>
      _stockPortfolioController.stream;

  Stream<bool> get streamWatchListLoading => _stockLoadingController.stream;

  SocketSubscriber? _chartSocketSubscriber;

  StreamSubscription? _chartSocketSubscription;

  SocketSubscriber<ISocketStockData>? _socketSubscriber;

  StreamSubscription? _socketSubscription;

  StockDetailEntity? get currentStockData => _stockItemController.valueOrNull;

  Exchange exchangeType = const Exchange('HOSE');

  String? stockType;

  bool get isChartDayShowing => selectedChartType == AppConstants.oneDay;

  double? get referencePrice => currentStockData?.referencePrice;

  bool get enableButtonSell => subAccountTypeHolding != null;

  List<StockPortfolioEntity> availableToTradeStock = [];

  SubAccountType? get subAccountTypeHolding {
    final subAccountTypeCanSell =
        availableToTradeStock
            .map(
              (e) => GetIt.instance.get<SubAccountCubit>().subAccountTypeFromId(
                e.accountId,
              ),
            )
            .toSet()
            .toList();
    if (subAccountTypeCanSell.isEmpty) return null;
    final defaultSubAccountType =
        GetIt.instance
            .get<SubAccountCubit>()
            .defaultSubAccount
            .toSubAccountType;
    if (subAccountTypeCanSell.contains(defaultSubAccountType)) {
      return defaultSubAccountType;
    }
    return subAccountTypeCanSell.first;
  }

  //create timer to handle socket data changed
  final Map<StockChangedProperties, Timer> _timers = {};
  final Map<StockChangedProperties, BehaviorSubject<bool>>
  stockChangedProperties = {
    StockChangedProperties.lastPrice: BehaviorSubject<bool>.seeded(false),
    StockChangedProperties.changePercent: BehaviorSubject<bool>.seeded(false),
    StockChangedProperties.changeValue: BehaviorSubject<bool>.seeded(false),
    StockChangedProperties.offerPrice1: BehaviorSubject<bool>.seeded(false),
    StockChangedProperties.offerPrice2: BehaviorSubject<bool>.seeded(false),
    StockChangedProperties.offerPrice3: BehaviorSubject<bool>.seeded(false),
    StockChangedProperties.offerVol1: BehaviorSubject<bool>.seeded(false),
    StockChangedProperties.offerVol2: BehaviorSubject<bool>.seeded(false),
    StockChangedProperties.offerVol3: BehaviorSubject<bool>.seeded(false),
    StockChangedProperties.bidPrice1: BehaviorSubject<bool>.seeded(false),
    StockChangedProperties.bidPrice2: BehaviorSubject<bool>.seeded(false),
    StockChangedProperties.bidPrice3: BehaviorSubject<bool>.seeded(false),
    StockChangedProperties.bidVol1: BehaviorSubject<bool>.seeded(false),
    StockChangedProperties.bidVol2: BehaviorSubject<bool>.seeded(false),
    StockChangedProperties.bidVol3: BehaviorSubject<bool>.seeded(false),
  };

  void init(String s, {num? initPrice}) {
    symbol = s;

    emitStreamData<num?>(_closePriceController, initPrice);

    _loadStockDetail();

    _listenDataChanged();

    _listenChartDataChange();
  }

  void updateChart(Period period) {
    final oldChartTypeSelected = selectedChartType;

    switch (period) {
      case Period.day:
        selectedChartType = AppConstants.oneDay;
        break;
      case Period.week:
        selectedChartType = AppConstants.oneWeek;
        break;
      case Period.month:
        selectedChartType = AppConstants.oneMonth;
        break;
      case Period.threeMonth:
        selectedChartType = AppConstants.threeMonth;
        break;
      case Period.year:
        selectedChartType = AppConstants.oneYear;
        break;
      default:
        selectedChartType = AppConstants.oneDay;
    }

    if (oldChartTypeSelected == selectedChartType && chartEntities.hasData) {
      return;
    }

    _loadingPriceChartData();
  }

  void _listenChartDataChange() {
    _chartSocketSubscriber?.dispose();
    _chartSocketSubscription?.cancel();

    _chartSocketSubscriber = ISocketConnect.instance.addListener(
      symbol,
      ISocketChannel.marketData,
    );

    _chartSocketSubscription = _chartSocketSubscriber!.streamData
        .map((e) => e as IMarketData)
        .listen((event) => _updateChartFromSocket(event));
  }

  void _listenDataChanged() {
    _socketSubscriber?.dispose();
    _socketSubscription?.cancel();

    _socketSubscriber = ISocketConnect().addListener(
      symbol,
      ISocketChannel.stockinfo,
    );

    _socketSubscription = _socketSubscriber!.streamData
        .map((e) => e as IStockInfoData)
        .listen((event) {
          _updateValueAndPercentChanged(event);
          _updateTop3TradingChangeFromSocket(event);
          _updateTotalTradingFromSocket(event);
          _updateChangePriceFromSocket(event);
        });
  }

  Future<void> _loadBasicData() async {
    await _loadHoldingPortfolio();
    await _loadingPriceChartData();
    emitStreamData<bool>(_stockLoadingController, false);
  }

  Future<void> _loadHoldingPortfolio() async {
    _listStockHolding?.clear();

    var stockHoldings = await getStockHoldingBySubAccount(
      SubAccountType.all,
      handleDuplicate: false,
    );

    availableToTradeStock =
        stockHoldings
            .where((e) => e.symbol == symbol && e.availableToTradeVol > 0)
            .toList();

    _listStockHolding = StockUtils.handleDuplicatedStockHolding(stockHoldings);

    final currentStockHolding = _listStockHolding?.firstWhereOrNull(
      (e) => e.symbol == symbol,
    );

    emitStreamData<StockPortfolioEntity?>(
      _stockPortfolioController,
      currentStockHolding,
    );
  }

  final chartEntities = <ChartPriceEntity>[];

  Future<void> _loadingPriceChartData() async {
    try {
      chartEntities.clear();

      emit(DetailChartLoadingState());

      List<ChartPriceEntity>? data;

      if (selectedChartType == '1D') {
        data = (await _stockRepo.getPriceCharts(symbol))[symbol];
      } else {
        data = await _stockRepo.getChartDetail(
          symbol: symbol,
          chartType: selectedChartType,
        );
      }

      /// get current time
      await TimeUtils().refresh();

      chartEntities.addAllIf(data, addIf: () => data != null);

      emit(DetailChartLoadedState(chartEntities));
    } catch (e) {
      emit(DetailChartErrorState());
    }
  }

  void _updateTotalTradingFromSocket(IStockInfoData data) {
    final currentData = _stockInfoController.valueOrNull;

    if (currentData == null) return;

    if (data.totalTrading != null && data.totalTradingValue != null) {
      //update stock data
      currentData.totalVolume = data.totalTrading!.toDouble();
      currentData.totalTradingValue = data.totalTradingValue!.toDouble();
      emitStreamData<StockDetailEntity>(_stockInfoController, currentData);
    } else if (data.totalTrading != null) {
      //update stock data
      currentData.totalVolume = data.totalTrading!.toDouble();
      emitStreamData<StockDetailEntity>(_stockInfoController, currentData);
    } else if (data.totalTradingValue != null) {
      //update stock data
      currentData.totalTradingValue = data.totalTradingValue!.toDouble();
      emitStreamData<StockDetailEntity>(_stockInfoController, currentData);
    }
  }

  DateTime get now => TimeUtils().now;

  void _updateChartFromSocket(IMarketData data) {
    final time = data.time;

    ///1D không quan tâm giá mở cửa
    const double openPrice = 0;

    final vol = data.volume?.toDouble();

    var closePrice = data.closePrice?.toDouble();

    if (selectedChartType != '1D') return;

    if (time == null || vol == null || closePrice == null) return;

    if (exchangeType == Exchange.hsx) {
      if (now.inATO() && currentStockData?.referencePrice != null) {
        closePrice = currentStockData!.referencePrice;
      }

      if (now.inATC() && chartEntities.lastOrNull?.close != null) {
        closePrice = chartEntities.lastOrNull!.close;
      }
    }

    if (exchangeType == Exchange.hnx) {
      if (now.inATC() && chartEntities.lastOrNull?.close != null) {
        closePrice = chartEntities.lastOrNull!.close;
      }
    }

    /// Nối điểm socket trả về vào chart
    chartEntities.add(
      ChartPriceEntity(
        x: chartEntities.length,
        time: time,
        close: closePrice,
        volume: vol,
        open: openPrice,
      ),
    );

    emit(DetailChartUpdateState(time));
  }

  void _updateChangePriceFromSocket(IStockInfoData data) {
    final currentData = _stockItemController.valueOrNull;

    if (data.closePrice == null || currentData == null) return;

    if (currentData.price != data.closePrice) {
      ///update stock data
      currentData.price = data.closePrice!.toDouble();

      emitStreamData<StockDetailEntity>(_stockItemController, currentData);

      emitStreamData<num?>(_closePriceController, currentData.price);

      ///show flash background
      _updateChangedProperty(StockChangedProperties.lastPrice);

      /// update chart
      if (chartEntities.lastOrNull?.close != data.closePrice) {
        emit(DetailChartUpdateState(DateTime.now()));
      }
    }

    ///update stock in portfolio
    final stockInPortfolio = _stockPortfolioController.valueOrNull;

    if (stockInPortfolio == null ||
        stockInPortfolio.basicPrice == data.closePrice)
      return;

    stockInPortfolio.basicPrice = data.closePrice!;

    emitStreamData(_stockPortfolioController, stockInPortfolio);
  }

  void _updateValueAndPercentChanged(IStockInfoData data) {
    StockDetailEntity? currentData = _stockItemController.valueOrNull;

    if (currentData == null) return;

    bool needUpdate = false;

    if (data.changePercent != currentData.changePercent &&
        data.changePercent != null) {
      currentData.changePercent = data.changePercent!.toDouble();
      needUpdate = true;
    }

    if (data.change != currentData.changeValue && data.change != null) {
      currentData.changeValue = data.change!.toDouble();
      needUpdate = true;
    }

    if (needUpdate) {
      //update stock data
      emitStreamData<StockDetailEntity>(_stockItemController, currentData);

      //show flash background
      _updateChangedProperty(StockChangedProperties.changeValue);
    }
  }

  void _updateTop3TradingChangeFromSocket(IStockInfoData data) {
    StockDetailEntity? currentData = _stockItemController.valueOrNull;

    if (currentData == null) return;

    bool needUpdateData = false;

    //Price info
    if (data.ceilingPrice != currentData.ceilingPrice &&
        data.ceilingPrice != null) {
      currentData.ceilingPrice = data.ceilingPrice!.toDouble();
      needUpdateData = true;
    }

    if (data.floorPrice != currentData.floorPrice && data.floorPrice != null) {
      currentData.floorPrice = data.floorPrice!.toDouble();
      needUpdateData = true;
    }

    if (data.referencePrice != currentData.referencePrice &&
        data.referencePrice != null) {
      currentData.referencePrice = data.referencePrice!.toDouble();
      needUpdateData = true;
    }

    //Bid side
    if (data.bidPrice1?.toString() != currentData.bidPrice1) {
      _updateChangedProperty(StockChangedProperties.bidPrice1);
      currentData.bidPrice1 = data.bidPrice1?.toString();
      needUpdateData = true;
    }

    if (data.bidPrice2 != currentData.bidPrice2) {
      _updateChangedProperty(StockChangedProperties.bidPrice2);
      currentData.bidPrice2 = data.bidPrice2;
      needUpdateData = true;
    }

    if (data.bidPrice3 != currentData.bidPrice3) {
      _updateChangedProperty(StockChangedProperties.bidPrice3);
      currentData.bidPrice3 = data.bidPrice3;
      needUpdateData = true;
    }

    if (data.bidVol1 != currentData.bidVol1) {
      _updateChangedProperty(StockChangedProperties.bidVol1);
      currentData.bidVol1 = data.bidVol1;
      needUpdateData = true;
    }

    if (data.bidVol2 != currentData.bidVol2) {
      _updateChangedProperty(StockChangedProperties.bidVol2);
      currentData.bidVol2 = data.bidVol2;
      needUpdateData = true;
    }

    if (data.bidVol3 != currentData.bidVol3) {
      _updateChangedProperty(StockChangedProperties.bidVol3);
      currentData.bidVol3 = data.bidVol3;
      needUpdateData = true;
    }

    //Offer Side
    if (data.offerPrice1 != currentData.offerPrice1) {
      _updateChangedProperty(StockChangedProperties.offerPrice1);
      currentData.offerPrice1 = data.offerPrice1;
      needUpdateData = true;
    }

    if (data.offerPrice2 != currentData.offerPrice2) {
      _updateChangedProperty(StockChangedProperties.offerPrice2);
      currentData.offerPrice2 = data.offerPrice2;
      needUpdateData = true;
    }

    if (data.offerPrice3 != currentData.offerPrice3) {
      _updateChangedProperty(StockChangedProperties.offerPrice3);
      currentData.offerPrice3 = data.offerPrice3;
      needUpdateData = true;
    }

    if (data.offerVol1 != currentData.offerVol1) {
      _updateChangedProperty(StockChangedProperties.offerVol1);
      currentData.offerVol1 = data.offerVol1;
      needUpdateData = true;
    }

    if (data.offerVol2 != currentData.offerVol2) {
      _updateChangedProperty(StockChangedProperties.offerVol2);
      currentData.offerVol2 = data.offerVol2;
      needUpdateData = true;
    }

    if (data.offerVol3 != currentData.offerVol3) {
      _updateChangedProperty(StockChangedProperties.offerVol3);
      currentData.offerVol3 = data.offerVol3;
      needUpdateData = true;
    }

    if (needUpdateData) {
      emitStreamData<StockDetailEntity>(_stockItemController, currentData);
    }
  }

  void _updateChangedProperty(StockChangedProperties property) {
    emitStreamData<bool>(stockChangedProperties[property]!, true);
    _invalidateAndRestartTimer(property);
  }

  void _invalidateAndRestartTimer(StockChangedProperties property) {
    const Duration duration = Duration(seconds: 1);
    Timer? timer = _timers[property];
    timer?.cancel();
    timer = Timer(duration, () {
      emitStreamData<bool>(stockChangedProperties[property]!, false);
    });
    _timers[property] = timer;
  }

  Future _loadStockDetail() async {
    try {
      emitStreamData<num?>(_closePriceController, null);

      emitStreamData<bool>(_stockLoadingController, true);

      // final listDataFromAPI =
      //     await GetIt.instance.get<WealthRepository>().getStockBySymbols(symbols);

      // // final values = await _homeRepo.getStockList(symbol);
      final values = await GetIt.instance
          .get<WealthRepository>()
          .getStockBySymbols([symbol]);

      if (values.isEmpty) {
        emitError(_stockLoadingController, '');

        return;
      }

      final stockDetailEntity = values.first;

      exchangeType = Exchange(stockDetailEntity.exchange ?? '');

      stockType = stockDetailEntity.stockType;

      emitStreamData<StockDetailEntity>(
        _stockInfoController,
        stockDetailEntity,
      );

      emitStreamData<StockDetailEntity>(
        _stockItemController,
        stockDetailEntity,
      );

      await _loadBasicData();
    } catch (e) {
      emitError(_stockLoadingController, e);
    }
  }

  Future onRefresh() => _loadStockDetail();

  @override
  Future<void> close() {
    _closePriceController.close();
    _chartSocketSubscriber?.dispose();
    _chartSocketSubscription?.cancel();
    _stockItemController.close();
    _stockInfoController.close();
    _stockLoadingController.close();
    _socketSubscriber?.dispose();
    _socketSubscription?.cancel();
    _stockPortfolioController.close();
    _priceViewController.close();
    _isPriceChartType.close();
    _selectedTimelineTradingView.close();
    for (var element in stockChangedProperties.values) {
      element.close();
    }
    return super.close();
  }

  void switchChartType() {
    _selectedTimelineTradingView.value = AppConstants.defaultChartTimeLine;
    emitStreamData<bool>(_isPriceChartType, !isPriceChart);
  }

  bool get isPriceChart => _isPriceChartType.value;

  updateTradingView(TimelineEnum timeline) {
    emitStreamData<String>(
      _selectedTimelineTradingView,
      timeline.timelineValue,
    );
  }

  TimelineEnum getTimelineSelected() {
    return valueToTimeline(_selectedTimelineTradingView.value);
  }

  String tradingWebviewStockDetail(
    String symbol,
    String interval, {
    String? theme,
    String timeFrame = '1D',
    bool fullScreen = false,
  }) {
    return 'https://neoinvest-uat-krx.vpbanks.com.vn/tradingview?symbol=$symbol&timeframe=$timeFrame&theme=${theme ?? 'light'}&is_iframe=true&is_full_screen=$fullScreen&interval=$interval';
  }
}
