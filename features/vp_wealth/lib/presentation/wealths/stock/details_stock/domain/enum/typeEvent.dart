


enum TypeEvent {
  InternalTransaction,
  BusinessResults,
  Listing,
  RightsIssue,
  StockDividend,
  CashDividend,
  Other;

  String get label {
    switch (this) {
      case InternalTransaction:
        return '<PERSON><PERSON><PERSON> dịch nội bộ';
      case BusinessResults:
        return '<PERSON>ết quả KD';
      case Listing:
        return '<PERSON>ê<PERSON> yết';
      case RightsIssue:
        return 'Quyền mua cổ phiếu';
      case StockDividend:
        return 'Trả cổ tức bằng cổ phiếu';
      case CashDividend:
        return 'Trả cổ tức bằng tiền';
      default:
        return '<PERSON>hác';
    }
  }
}
