class MainStockPathApi {
  //watchlist
  static const String watchlist = "/userdata/watchlists";

  //danh sách chứng khoán theo danh mục
  static const String instruments = "/invest/api/stockInfoByList";

  //danh sách chứng khoán hiện có theo tiểu khoản => danh mục nắm giữ
  static String securitiesPortfolio(String accountId) =>
      "/flex/inq/accounts/$accountId/securitiesPortfolio";

  //Lấy danh sách các mã chứng khoán đang nắm giữ
  static const String seholding = "/inq/seholding";

  // Lấy danh sách mã chứng khoán theo INDEX
  static String stockByIndex(String value) =>
      "/api/stockInfoByList?indexCode=$value";

  // Lấy danh sách trái phiếu riêng lẻ
  static String bondByIndex = "/invest/api/v2/bondInfoByList";

  //cổ phiếu tích cực
  static const String top10PositiveStock =
      "/invest/api/v2/top10/top10PositiveStock";

  //top mã tăng giá
  static const String top10PriceUp = "/invest/api/v2/top10/top10PriceUp";

  //top mã giảm giá
  static const String top10PriceDown = "/invest/api/v2/top10/top10PriceDown";

  //danh sách ngành
  static const String getIcbLevel = "/invest/api/v2/getIcbLevel";

  //danh sách cổ phiếu theo ngành
  static const String getListStockByIcbCode =
      "/invest/api/v2/getListStockByIcbCode";

  //danh sách chứng quyền
  static const String cwInfoByList = '/invest/api/v2/cwInfoByList';

  // Lích sự kiện cổ phiếu
  static const String eventList = '/invest/api/v2/eventList';
}
