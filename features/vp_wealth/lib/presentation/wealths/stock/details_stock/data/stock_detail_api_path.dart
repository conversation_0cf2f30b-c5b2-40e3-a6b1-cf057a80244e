class StockDetailApi {
  static const String chartLine = '/api/stock/getPriceChartLine';

  // currently don't need this api
  // static const String top3Trading = '/api/getTopTrading';

  static const String matchingHistoryBuyUpSellDown =
      '/invest/api/v2/matchingHistoryBuyUpSellDown';

  static const String companyInfo = '/invest/api/companyInfo';

  static const String priceChartHistory = '/invest/api/stock/getPriceChartTradingView';

  static const String newsByStock = '/invest/api/news/getByStock';
  static const String stockInfo = '/api/stockInfoByList';

  /// Chart buyUpSellDown detail Stock
  static const String buyUpSellDownByStock = '/invest/api/buyUpSellDown/ByStock';
  static const intradayMatchingHistory = '/invest/api/v2/intradayMatchingHistory';

  /// StockTagging
  static const searchTags = '/invest/tagging/api/tagging/searchTags';
  static const searchTickers = '/invest/tagging/api/tagging/searchTickers';

  /// file
  static const String getCompanyInformation = '/invest/api/v2/getCompanyInformation';
  static const String getCorporateShareholder =
      '/invest/api/v2/getCorporateShareholder';
  static const String getCompanyRelationship = '/invest/api/v2/getCompanyRelationship';
  static const String eventCalendar = '/invest/api/v2/eventCalendar';

  ///thống kê
  static const String getShareholderTransaction =
      '/invest/api/v2/getShareholderTransaction';
  static const String getPriceHistory = '/invest/api/v2/getPriceHistory';
  static const String getForeignTrading = '/invest/api/v2/getForeignTrading';
  static const String getPlaceOrder = '/invest/api/v2/getPlaceOrder';
  static const String getCompanyEquity = '/invest/api/v2/getCompanyEquity';
  static const String getCompanyDividendPayment =
      '/invest/api/v2/getCompanyDividendPayment';
  static const String getCompanyProfit =
      '/invest/api/v2/getCompanyProfit';
  static const String getCoveredWarrantInfo =
      '/invest/api/v2/getCoveredWarrantInfo';
}
