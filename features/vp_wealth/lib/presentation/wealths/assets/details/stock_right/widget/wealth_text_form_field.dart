import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';

class WealthTextFormField extends FormField<String> {
  final TextEditingController? textEditingController;
  final bool obscureText;

  WealthTextFormField({
    String? value,
    FocusNode? focusNode,
    Key? key,
    String? initialValue,
    String? title,
    String? labelText,
    Widget? action,
    String? hint,
    FormFieldSetter<String>? onSaved,
    FormFieldValidator<String>? validator,
    bool autoValidate = false,
    bool enabled = true,
    bool selected = false,
    bool readOnly = false,
    Widget? suffix,
    Widget? icon,
    Widget? prefix,
    bool filled = false,
    AutovalidateMode? autovalidateMode,
    this.obscureText = true,
    this.textEditingController,
    bool isPassword = false,
    bool isTextSameIconState = true,
    Widget? iconShowText,
    Widget? iconHideText,
    TextInputType? inputType,
    List<TextInputFormatter>? inputFormatters,
    int? maxLength,
    int? maxLine,
    onTap,
    bool myAutoValidate = false,
    bool alwaysValidate = false,
    ValueChanged<String>? onChanged,
    int? minLines,
    bool? autoFocus,
    TextStyle? hintStyle,
    TextStyle? titleStyle,
    TextStyle? errorStyle,
    bool? showCursor,
    Color? fillColor,
    EdgeInsetsGeometry? contentPadding,
    bool isInputDate = false,
    TextCapitalization? textCapitalization,
    TextInputAction? textInputAction,
    bool? interactiveSelection,
    TextAlign textAlign = TextAlign.start,
    ValueChanged<String>? onSubmitted,
    bool autocorrect = true,
    Color? colorBorder,
    Color? colorBorderDisable,
    TextStyle? textStyle,
  }) : super(
         key: key,
         onSaved: onSaved,
         initialValue: initialValue,
         autovalidateMode:
             alwaysValidate
                 ? AutovalidateMode.always
                 : myAutoValidate
                 ? AutovalidateMode.onUserInteraction
                 : AutovalidateMode.disabled,
         builder: (FormFieldState field) {
           AppTextFormFieldState state = field as AppTextFormFieldState;
           return Column(
             crossAxisAlignment: CrossAxisAlignment.start,
             children: [
               if (title != null)
                 Padding(
                   padding: const EdgeInsets.only(bottom: 6),
                   child: Row(
                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                     children: [
                       Text(
                         title,
                         style:
                             titleStyle ??
                             Theme.of(state.context).textTheme.bodyMedium!
                                 .copyWith(fontWeight: FontWeight.w600),
                       ),
                       if (action != null) action,
                     ],
                   ),
                 ),
               TextFormField(
                 keyboardAppearance: Brightness.dark,
                 showCursor: showCursor ?? true,
                 key: key,
                 validator: validator,
                 focusNode: focusNode,
                 readOnly: readOnly,
                 minLines: minLines ?? 1,
                 maxLines: maxLine ?? 1,
                 autofocus: autoFocus ?? false,
                 cursorColor: themeData.cursorTextField,
                 controller: textEditingController,
                 obscureText: isPassword ? state._obscureText : false,
                 style:
                     textStyle ??
                     vpTextStyle.body14?.copyWith(color: themeData.black),
                 keyboardType: inputType,
                 textCapitalization:
                     textCapitalization ?? TextCapitalization.none,
                 textInputAction: textInputAction,
                 inputFormatters: [
                   ...inputFormatters ?? [],
                   LengthLimitingTextInputFormatter(maxLength),
                 ],
                 onChanged: (text) {
                   state.didChange(text);
                   if (onChanged != null) {
                     onChanged(text);
                   }
                 },
                 onTap: onTap,
                 decoration: const InputDecoration()
                     .applyDefaults(
                       Theme.of(state.context).inputDecorationTheme,
                     )
                     .copyWith(
                       focusedBorder: OutlineInputBorder(
                         borderRadius: const BorderRadius.all(
                           Radius.circular(SizeUtils.kRadius8),
                         ),
                         borderSide: BorderSide(
                           width: 1,
                           color: colorBorder ?? themeData.borderBg,
                         ),
                       ),
                       errorStyle: errorStyle,
                       focusedErrorBorder: OutlineInputBorder(
                         borderRadius: const BorderRadius.all(
                           Radius.circular(SizeUtils.kRadius8),
                         ),
                         borderSide: BorderSide(width: 1, color: themeData.red),
                       ),
                       enabledBorder: OutlineInputBorder(
                         borderRadius: const BorderRadius.all(
                           Radius.circular(SizeUtils.kRadius8),
                         ),
                         borderSide: BorderSide(
                           width: 1,
                           color: colorBorder ?? themeData.borderInput,
                         ),
                       ),
                       disabledBorder: OutlineInputBorder(
                         borderRadius: const BorderRadius.all(
                           Radius.circular(SizeUtils.kRadius8),
                         ),
                         borderSide: BorderSide(
                           width: 1,
                           color: colorBorderDisable ?? themeData.borderDisable,
                         ),
                       ),
                       filled: true,
                       fillColor: fillColor ?? themeData.bgInput,
                       hintText: hint,
                       contentPadding:
                           contentPadding ??
                           const EdgeInsets.symmetric(
                             vertical: SizeUtils.kSize8,
                             horizontal: SizeUtils.kSize16,
                           ),
                       isDense: true,
                       hintStyle:
                           hintStyle ??
                           vpTextStyle.body14?.copyWith(
                             color: themeData.textHint,
                           ),
                       labelText: labelText,
                       suffixIcon:
                           suffix ??
                           (isInputDate
                               ? null
                               : isPassword
                               ? IconButton(
                                 iconSize: 20,
                                 icon:
                                     state._obscureText == isTextSameIconState
                                         ? iconHideText ??
                                             Icon(
                                               Icons.visibility_off,
                                               color:
                                                   Theme.of(
                                                     state.context,
                                                   ).iconTheme.color,
                                             )
                                         : iconShowText ??
                                             Icon(
                                               Icons.visibility,
                                               color:
                                                   Theme.of(
                                                     state.context,
                                                   ).iconTheme.color,
                                             ),
                                 onPressed: () {
                                   state.onChangeObscureText();
                                 },
                               )
                               : state.textEditingController.text.isNotEmpty &&
                                   readOnly
                               ? Padding(
                                 padding: const EdgeInsets.all(2.5),
                                 child: InkResponse(
                                   radius: 21,
                                   onTap: () {
                                     state.textEditingController.clear();
                                   },
                                   child: Icon(
                                     Icons.visibility_off,
                                     size: 16,
                                     // height: 13,
                                     color:
                                         Theme.of(
                                           state.context,
                                         ).iconTheme.color,
                                   ),
                                 ),
                               )
                               : null),
                       prefixIcon: prefix,
                       prefixIconConstraints: const BoxConstraints(
                         minWidth: 16,
                         minHeight: 16,
                       ),
                       suffixIconConstraints: const BoxConstraints(
                         minWidth: 16,
                         minHeight: 10,
                       ),
                       errorText: state.errorText,
                       enabled: enabled,
                       errorMaxLines: 3,
                     ),
                 enableInteractiveSelection: interactiveSelection ?? true,
                 textAlign: textAlign,
                 onFieldSubmitted: onSubmitted,
                 autocorrect: autocorrect,
               ),
             ],
           );
         },
       );

  @override
  AppTextFormFieldState createState() => AppTextFormFieldState();
}

class AppTextFormFieldState extends FormFieldState<String> {
  late TextEditingController textEditingController;

  late bool _obscureText;

  void onChangeObscureText() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  @override
  WealthTextFormField get widget => super.widget as WealthTextFormField;

  @override
  void initState() {
    super.initState();
    _obscureText = widget.obscureText;
    textEditingController =
        widget.textEditingController ?? TextEditingController();
    if (widget.initialValue != null) {
      textEditingController.text = widget.initialValue!;
    }
  }
}
