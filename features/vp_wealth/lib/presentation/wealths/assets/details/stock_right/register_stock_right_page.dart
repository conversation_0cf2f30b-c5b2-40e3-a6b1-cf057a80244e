import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_common/utils/app_number_format_utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_common/widget/vpbank_loading.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/lang/stock_localized_values.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/stock_right/widget/wealth_text_form_field.dart';

import '../../../../../data/model/assets/right_off_model.dart';
import '../../../../../data/utils/keyboard_mass.dart';
import '../../../../../data/utils/padding_extends.dart';
import '../../../../place_order/utils/place_order_utils.dart';
import '../tabs/money/widget/info_money_widget.dart';
import 'bloc/register_stock_right_bloc.dart';
import 'widget/error_widget.dart';

class RegisterStockRightArgument {
  final RightOffModel model;
  final int copierID;
  final String copierName;

  const RegisterStockRightArgument({
    required this.model,
    required this.copierID,
    required this.copierName,
  });
}

class RegisterStockRightPage extends StatelessWidget {
  const RegisterStockRightPage({Key? key, required this.argument})
    : super(key: key);
  final RegisterStockRightArgument argument;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<RegisterStockRightBloc>.value(
      value: RegisterStockRightBloc(argument.copierID, context),
      child: VPScaffold(
        backgroundColor: vpColor.backgroundElevation0,
        appBar: VPAppBar.flows(
          title: 'Đăng ký quyền mua',
          leading: InkWell(
            onTap: () => Navigator.pop(context),
            child: Icon(Icons.close, color: vpColor.textPrimary).paddingTop4(),
          ),
        ),
        body: SafeArea(
          child: Stack(
            children: [
              Column(
                children: [
                  Divider(
                    color: vpColor.backgroundElevationMinus1,
                    height: 8,
                    thickness: 8,
                  ),
                  Expanded(
                    child: BlocBuilder<
                      RegisterStockRightBloc,
                      RegisterStockRightState
                    >(
                      builder: (context, state) {
                        if (state is GetCinInfoFail) {
                          return ErrorNetworkWidget(
                            onPressed:
                                () => context
                                    .read<RegisterStockRightBloc>()
                                    .getCiInfo(context),
                            tryAgain: true,
                          );
                        } else {
                          return Column(
                            children: [
                              Expanded(
                                child: SingleChildScrollView(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.all(16),
                                        child: Column(
                                          children: [
                                            InfoMoneyWidget(
                                              title: 'Mã chứng khoán',
                                              amount:
                                                  argument.model.symbol ?? '',
                                            ),
                                            const InfoMoneyWidget(
                                              title: 'Tiểu khoản',
                                              amount: 'Tích sản',
                                            ),
                                            BlocBuilder<
                                              RegisterStockRightBloc,
                                              RegisterStockRightState
                                            >(
                                              builder:
                                                  (
                                                    context,
                                                    state,
                                                  ) => InfoMoneyWidget(
                                                    title: getStockLang(
                                                      StockKeyLang
                                                          .availableBalance,
                                                    ),
                                                    amount:
                                                        context
                                                                    .read<
                                                                      RegisterStockRightBloc
                                                                    >()
                                                                    .availableTransferOnline !=
                                                                null
                                                            ? (context
                                                                        .read<
                                                                          RegisterStockRightBloc
                                                                        >()
                                                                        .availableTransferOnline ??
                                                                    0)
                                                                .valueText
                                                            : '',
                                                    color:
                                                        vpColor.textPriceGreen,
                                                  ),
                                            ),
                                            InfoMoneyWidget(
                                              title: 'Giá',
                                              amount: AppNumberFormatUtils
                                                  .shared
                                                  .percentFormatter
                                                  .format(
                                                    (argument.model.price ??
                                                            0) /
                                                        1000,
                                                  ),
                                            ),
                                            BlocBuilder<
                                              RegisterStockRightBloc,
                                              RegisterStockRightState
                                            >(
                                              builder:
                                                  (
                                                    context,
                                                    state,
                                                  ) => InfoMoneyWidget(
                                                    title: 'KL tối đa được mua',
                                                    amount: AppNumberFormatUtils
                                                        .shared
                                                        .percentFormatter
                                                        .format(
                                                          argument
                                                              .model
                                                              .pendingQtty,
                                                        ),
                                                  ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      DividerWidget(
                                        color:
                                            vpColor.backgroundElevationMinus1,
                                        thickness: 2,
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.all(16.0),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                bottom: 8,
                                              ),
                                              child: Text(
                                                getStockLang(
                                                  StockKeyLang.numberSub,
                                                ),
                                                style: vpTextStyle.body14,
                                              ),
                                            ),

                                            BlocBuilder<
                                              RegisterStockRightBloc,
                                              RegisterStockRightState
                                            >(
                                              builder:
                                                  (context, state) => Form(
                                                    key:
                                                        context
                                                            .read<
                                                              RegisterStockRightBloc
                                                            >()
                                                            .volumeGlobalKey,
                                                    child: WealthTextFormField(
                                                      textEditingController:
                                                          context
                                                              .read<
                                                                RegisterStockRightBloc
                                                              >()
                                                              .volumeController,
                                                      hint: 'Nhập số lượng',
                                                      inputType:
                                                          TextInputType.none,
                                                      focusNode:
                                                          context
                                                              .read<
                                                                RegisterStockRightBloc
                                                              >()
                                                              .focusVolume,
                                                      validator:
                                                          (val) => context
                                                              .read<
                                                                RegisterStockRightBloc
                                                              >()
                                                              .validate(
                                                                context,
                                                                argument
                                                                        .model
                                                                        .rightBuyAvaIlaBle ??
                                                                    0,
                                                              ),
                                                      fillColor:
                                                          context
                                                              .read<
                                                                RegisterStockRightBloc
                                                              >()
                                                              .volumeFillColor,
                                                      interactiveSelection:
                                                          false,
                                                    ),
                                                  ),
                                            ),
                                            const SizedBox(height: 8),
                                            BlocBuilder<
                                              RegisterStockRightBloc,
                                              RegisterStockRightState
                                            >(
                                              builder:
                                                  (
                                                    context,
                                                    state,
                                                  ) => InfoMoneyWidget(
                                                    title: getStockLang(
                                                      StockKeyLang.intoMoney,
                                                    ),
                                                    amount:
                                                        context
                                                                    .read<
                                                                      RegisterStockRightBloc
                                                                    >()
                                                                    .money ==
                                                                0
                                                            ? '-đ'
                                                            : context
                                                                .read<
                                                                  RegisterStockRightBloc
                                                                >()
                                                                .money
                                                                .toMoney(),
                                                  ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              Container(
                                decoration: BoxDecoration(
                                  border: Border(
                                    top: BorderSide(
                                      width: 1,
                                      color: vpColor.strokeGray,
                                    ),
                                  ),
                                ),
                                padding: const EdgeInsets.symmetric(
                                  vertical: 16,
                                  horizontal: 16,
                                ),
                                child: BlocBuilder<
                                  RegisterStockRightBloc,
                                  RegisterStockRightState
                                >(
                                  builder:
                                      (
                                        context,
                                        state,
                                      ) => VpsButton.primarySmall(
                                        title: 'Tiếp theo',
                                        width: double.infinity,
                                        textAlign: TextAlign.center,
                                        disabled:
                                            !context
                                                .read<RegisterStockRightBloc>()
                                                .enable,
                                        onPressed:
                                            () => context
                                                .read<RegisterStockRightBloc>()
                                                .register(
                                                  context,
                                                  argument.model.caMastId,
                                                  argument.model.symbol ?? '',
                                                  argument.model.accountId ??
                                                      '',
                                                  context
                                                      .read<
                                                        RegisterStockRightBloc
                                                      >()
                                                      .volumeController
                                                      .text,
                                                ),
                                      ),
                                ),
                              ),
                            ],
                          );
                        }
                      },
                    ),
                  ),
                  BlocBuilder<RegisterStockRightBloc, RegisterStockRightState>(
                    builder:
                        (context, state) => Visibility(
                          visible:
                              context
                                  .read<RegisterStockRightBloc>()
                                  .visibleKeyVolume,
                          child: KeyboardMass(
                            controller:
                                context
                                    .read<RegisterStockRightBloc>()
                                    .volumeController,
                            maxLength: 50,
                            onChange:
                                (val) => context
                                    .read<RegisterStockRightBloc>()
                                    .onChangeVolume(
                                      context,
                                      val,
                                      argument.model.price ?? 0,
                                    ),
                          ),
                        ),
                  ),
                ],
              ),
              BlocBuilder<RegisterStockRightBloc, RegisterStockRightState>(
                builder:
                    (context, state) => Visibility(
                      visible: context.read<RegisterStockRightBloc>().loading,
                      child: const VPBankLoadingWithText(text: Text('Loading')),
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
