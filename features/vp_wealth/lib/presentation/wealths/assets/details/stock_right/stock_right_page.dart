import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_number_format_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/app_refresh_view.dart';
import 'package:vp_design_system/widget/appbar/appbar_view.dart';
import 'package:vp_design_system/widget/scaffold/scaffold_view.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/lang/stock_localized_values.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/stock_right/register_stock_right_page.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/stock_right/widget/expansion_tile_widget.dart';
import 'package:vp_wealth/router/wealth_router.dart';

import '../../../../../common/utils/wealth_utils.dart';
import '../../../../../data/model/assets/item_assets_model.dart';
import '../../../../../data/model/assets/right_off_model.dart';
import '../../../../../data/utils/constains.dart';
import '../tabs/money/widget/info_money_widget.dart';
import 'bloc/stock_right_bloc.dart';
import 'widget/error_widget.dart';
import 'widget/stock_right_loading_widget.dart';

class StockRightPage extends StatefulWidget {
  final ItemAssetsModel model;

  const StockRightPage({Key? key, required this.model}) : super(key: key);

  @override
  State<StockRightPage> createState() => _StockRightPageState();
}

class _StockRightPageState extends State<StockRightPage> {
  final List<GlobalKey<ExpansionTileWidgetState>> _keys = [];

  ItemAssetsModel get _model => widget.model;

  late StockRightBloc bloc;

  @override
  void initState() {
    super.initState();

    bloc = StockRightBloc(context, _model);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<StockRightBloc>.value(
      value: bloc,
      child: VPScaffold(
        backgroundColor: vpColor.backgroundElevation0,
        appBar: VPAppBar.layer(title: getStockLang(StockKeyLang.stockRight)),
        body: SafeArea(
          child: ColoredBox(
            color: vpColor.backgroundElevationMinus1,
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: SizeUtils.kSize16,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  kSpacingHeight12,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Tiểu khoản',
                        style: vpTextStyle.body14?.copyWith(
                          color: vpColor.textPrimary,
                        ),
                      ),
                      Text(
                        'Tích sản',
                        style: vpTextStyle.subtitle14?.copyWith(
                          color: vpColor.textPrimary,
                        ),
                      ),
                    ],
                  ),
                  kSpacingHeight12,
                  Expanded(
                    child: BlocBuilder<StockRightBloc, StockRightState>(
                      builder: (context, state) {
                        if (state is StockRightLoading) {
                          return const StockRightLoadingWidget();
                        }

                        if (state is StockRightNoData) {
                          return PullToRefreshView(
                            onRefresh: () => bloc.onRefresh(context),
                            child: _listEmpty,
                          );
                        }

                        if (state is StockRightError) {
                          return ErrorNetworkWidget(
                            onPressed: () => bloc.getRightOffList(context),
                            tryAgain: true,
                          );
                        }

                        _keys.clear();

                        return PullToRefreshView(
                          onRefresh: () => bloc.onRefresh(context),
                          child: ListView.builder(
                            itemCount: bloc.listRightOff.length,
                            itemBuilder: (context, index) {
                              _keys.add(GlobalKey());
                              final model = bloc.listRightOff[index];
                              return _itemStockRight(model);
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _itemStockRight(RightOffModel model) {
    Color? colorText = model.isAllowRegister ? null : vpColor.textDisabled;
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (!model.isAllowRegister) return;
        context
            .push(
              WealthRouter.registerStockRightPage,
              extra: RegisterStockRightArgument(
                model: model,
                copierID: _model.copierId ?? -1,
                copierName: _model.copierName ?? '',
              ),
            )
            .then((value) {
              if (value != null) {
                context.read<StockRightBloc>().init(context);
              }
            });
      },
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: SizeUtils.kSize4),
        padding: const EdgeInsets.symmetric(
          horizontal: SizeUtils.kSize16,
          vertical: SizeUtils.kSize12,
        ),
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.all(Radius.circular(8)),
          color: vpColor.backgroundElevation0,
        ),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: SizeUtils.kSize4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    model.symbol ?? '',
                    style: vpTextStyle.subtitle16?.copyWith(
                      color: colorText ?? vpColor.textPrimary,
                    ),
                  ),
                  RichText(
                    text: TextSpan(
                      children: [
                        WidgetSpan(
                          child: Assets.icons.icRegisterStockRight.svg(
                            color: colorText,
                          ),
                        ),
                        TextSpan(
                          text: ' Đăng ký',
                          style: vpTextStyle.subtitle14?.copyWith(
                            color: colorText ?? vpColor.textBrand,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            InfoMoneyWidget(
              title: 'KL mua tối đa',
              color: colorText,
              amount: AppNumberFormatUtils.shared.percentFormatter.format(
                model.qtty,
              ),
            ),
            InfoMoneyWidget(
              title: getStockLang(StockKeyLang.registeredVolume),
              color: colorText,
              amount: AppNumberFormatUtils.shared.percentFormatter.format(
                model.regQtty,
              ),
            ),
            InfoMoneyWidget(
              title: 'KL còn lại',
              color: colorText,
              amount: AppNumberFormatUtils.shared.percentFormatter.format(
                model.pendingQtty,
              ),
            ),
            InfoMoneyWidget(
              title: 'Giá thực hiện',
              color: colorText,
              amount: AppNumberFormatUtils.shared.percentFormatter.format(
                (model.price ?? 0) / 1000,
              ),
            ),
            InfoMoneyWidget(
              title: 'Hạn cuối đăng ký mua',
              color: colorText,
              amount: model.reGistLastDate ?? '',
            ),
          ],
        ),
      ),
    );
  }

  Widget get _listEmpty {
    return Padding(
      padding: const EdgeInsets.only(top: SizeUtils.kSize56),
      child: SizedBox(
        width: double.infinity,
        child: Column(
          children: [
            Assets.icons.emptyStock.svg(),
            kSpacingHeight8,
            Text(
              'Không có quyền mua cổ phiếu.',
              style: vpTextStyle.body14?.copyWith(color: vpColor.textPrimary),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    bloc.close();
    super.dispose();
  }
}
