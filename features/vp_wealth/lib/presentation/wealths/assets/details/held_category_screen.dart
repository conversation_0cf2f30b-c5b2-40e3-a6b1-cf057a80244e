import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/router/wealth_router.dart';

import '../../../../common/utils/wealth_utils.dart';
import '../../../../data/model/assets/item_assets_model.dart';
import '../../../../data/utils/constains.dart';
import 'cubit/detail_assets_cubit.dart';
import 'tabs/category/assets_category_tab.dart';

class HeldCategoryScreen extends StatefulWidget {
  final ItemAssetsModel model;

  const HeldCategoryScreen({Key? key, required this.model}) : super(key: key);

  @override
  State<HeldCategoryScreen> createState() => _HeldCategoryScreenState();
}

class _HeldCategoryScreenState extends State<HeldCategoryScreen> {
  final DetailAssetsCubit _cubit = DetailAssetsCubit();

  ItemAssetsModel get _model => widget.model;

  @override
  void initState() {
    super.initState();
    _cubit.onFetchUnreadRight(_model.copierId ?? -1);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<DetailAssetsCubit>(
      create: (context) => _cubit,
      child: VPScaffold(
        appBar: VPAppBar.layer(
          title: 'Danh mục nắm giữ',
          actions: [_stockRight],
        ),
        body: Container(
          color: vpColor.backgroundElevationMinus1,
          child: AssetsCategoryTab(model: _model),
        ),
      ),
    );
  }

  Widget get _stockRight {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () =>
          context.push(WealthRouter.stockRightPage, extra: _model),
      child: Padding(
        padding: const EdgeInsets.only(right: SizeUtils.kSize12),
        child: BlocBuilder<DetailAssetsCubit, DetailAssetsState>(
          bloc: _cubit,
          buildWhen: (previous, current) =>
              previous.unreadRight != current.unreadRight,
          builder: (context, state) {
            return Row(
              children: [
                if (state.unreadRight > 0)
                  Container(
                    width: SizeUtils.kSize8,
                    height: SizeUtils.kSize8,
                    margin: const EdgeInsets.only(top: SizeUtils.kSize4),
                    decoration: BoxDecoration(
                      color: themeData.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                kSpacingWidth4,
                Text(
                  'Quyền mua',
                  textAlign: TextAlign.center,
                  style: vpTextStyle.body14?.copyWith(color: vpColor.textBrand),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
