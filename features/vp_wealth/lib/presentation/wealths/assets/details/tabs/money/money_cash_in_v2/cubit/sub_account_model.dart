// import 'package:collection/collection.dart';
// import 'package:vp_common/constants/app_constants.dart';
// import 'package:vp_common/constants/condition_command_enum.dart';
// import 'package:vp_core/vp_core.dart';
// import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/transfer/money_account_recived_obj.dart';
// // enum SubAccountType {
// //   normal,
// //   margin,
// //   bond,
// //   ps,
// //   all,
// // }

// class DerivativeAccountModel extends SubAccountModel {
//   String? dmastatus;

//   String? status;

//   bool get accountValid =>
//       !(status?.toLowerCase() == 'c' && dmastatus?.toLowerCase() == 'c');

//   bool get disableDepositButton =>
//       (id != null &&
//           (dmastatus?.toUpperCase() == 'P' ||
//               dmastatus?.toUpperCase() == 'D')) ||
//       (dmastatus?.toUpperCase() == 'C' && status?.toUpperCase() == 'D');

//   bool get enableDepositButton => !disableDepositButton;

//   DerivativeAccountModel.fromJson(Map<String, dynamic> json) {
//     id = json['afacctno'];
//     afacctnoext = 'Phái sinh';
//     enafacctnoext = 'Phái sinh';
//     status = json['status'];
//     dmastatus = json['dmastatus'];
//     accounttype = AppConstants.derivativeAccountType;
//   }
// }

// class SubAccountModel {
//   String? id;
//   String? afacctnoext;
//   String? enafacctnoext;
//   String? accounttype;

//   SubAccountModel({
//     this.id,
//     this.afacctnoext,
//     this.enafacctnoext,
//     this.accounttype,
//   });

//   SubAccountModel.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     afacctnoext = json['afacctnoext'];
//     enafacctnoext = json['enafacctnoext'];
//     accounttype = json['accounttype'];
//   }

//   bool isNormal() {
//     return accounttype == '.1';
//   }

//   bool isMargin() {
//     return accounttype == '.6';
//   }

//   bool isBond() {
//     return accounttype == '.3';
//   }

//   bool isTypeAll() {
//     return accounttype == '-1';
//   }

//   bool isDerivative() {
//     return accounttype == AppConstants.derivativeAccountType;
//   }

//   SubAccountType get toSubAccountType {
//     if (isTypeAll()) return SubAccountType.all;

//     if (isBond()) return SubAccountType.bond;

//     if (isNormal()) return SubAccountType.normal;

//     if (isMargin()) return SubAccountType.margin;

//     if (isDerivative()) return SubAccountType.derivative;

//     return SubAccountType.all;
//   }

//   String get fullName {
//     final name = afacctnoext!.toLowerCase();

//     return isTypeAll()
//         ? 'Tất cả tiểu khoản'
//         : "Tiểu khoản $name";
//   }

//   ConditionCommandSubAccountEnum? get subAccountFilterType {
//     if (isNormal()) {
//       return ConditionCommandSubAccountEnum.normal;
//     }
//     if (isMargin()) {
//       return ConditionCommandSubAccountEnum.margin;
//     }
//     return ConditionCommandSubAccountEnum.all;
//   }

//   MoneyAccountRecivedObj convertToAccountReceived() {
//     return MoneyAccountRecivedObj(
//       benefitAccount: id,
//       transfertype: MoneyTransferType.internal.name,
//       benefitName: accounttype,
//     );
//   }

  
// }

// extension SubAccountModelExt on List<SubAccountModel> {
//   SubAccountModel filterType(SubAccountType type,
//           {SubAccountModel? subAccDefault}) =>
//       firstWhere(
//         (e) => e.toSubAccountType == type,
//         orElse: () => subAccDefault ?? this[0],
//       );

//   SubAccountModel? filterById(String? id) =>
//       firstWhereOrNull((e) => e.id == id);
// }
