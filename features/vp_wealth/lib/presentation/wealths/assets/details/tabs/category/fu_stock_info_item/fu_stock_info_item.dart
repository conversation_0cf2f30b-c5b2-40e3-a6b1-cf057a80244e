import 'dart:async';
import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/theme/theme_service.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/model/stock_detail_entity.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/cubit/detail_assets_cubit.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/cubit/stock_sort_helper.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/edit_plan/stock_changed_properties.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/data/stock_helper.dart';

import 'fu_stock_info_item_bloc.dart';

class FuStockInfoItem extends StatefulWidget {
  final StockDisplayModeMixin displayModeMixin;

  final ValueChanged<StockDetailEntity>? onTap;

  final StockDetailEntity entity;

  final bool showPercent;

  final num? marketIndex;

  const FuStockInfoItem({
    required this.entity,
    required this.displayModeMixin,
    this.showPercent = false,
    this.marketIndex,
    this.onTap,
    Key? key,
  }) : super(key: key);

  @override
  State<FuStockInfoItem> createState() => _StockInfoItemState();
}

class _StockInfoItemState extends State<FuStockInfoItem> {
  final _bloc = FuStockTabMarketItemBloc();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback(
      (_) => _bloc.init(
        item: widget.entity,
        mixin: widget.displayModeMixin,
        marketIndex: widget.marketIndex,
      ),
    );
  }

  @override
  void dispose() {
    _bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTap != null ? () => widget.onTap!(widget.entity) : null,
      child: SizedBox(
        key: widget.key,
        child: StreamBuilder<StockDetailEntity>(
          stream: _bloc.streamStockItem,
          initialData: widget.entity,
          builder: (context, snapshot) {
            if (snapshot.data == null) {
              return const SizedBox.shrink();
            }
            StockDetailEntity item = snapshot.data!;

            final diff =
                item.diff == null
                    ? '-'
                    : item.diff!.getPriceFormatted(prefix: false);

            final changeValue =
                item.isItemDefault
                    ? '-'
                    : (item.changeValue ?? 0).getPriceFormatted(prefix: false);

            final percent =
                item.isItemDefault
                    ? '-%'
                    : (item.changePercent ?? 0).getChangePercentDisplay(
                      excludeZero: true,
                    );

            final color = item.colorByPrice;

            final List<double> size = [
              SizeColumnMarket.widthColumnVolume,
              SizeColumnMarket.widthColumnSymbol,
              SizeColumnMarket.widthColumnSymbol,
              SizeColumnMarket.widthColumnSymbol,
              SizeColumnMarket.widthColumnSymbol,
              SizeColumnMarket.widthColumnSymbol,
            ];

            return SingleChildScrollView(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: size[0],
                    child: AutoSizeText(
                      widget.entity.symbol,
                      textAlign: TextAlign.left,
                      minFontSize: 11,
                      maxLines: 1,
                      style: vpTextStyle.body14?.copyWith(
                        color: item.isItemDefault ? themeData.black : color,
                      ),
                    ),
                  ),
                  SizedBox(
                    width: size[1],
                    child: buildDisplayModeView(item, DisplayMode.price),
                  ),
                  Visibility(
                    visible: !widget.showPercent,
                    child: SizedBox(
                      width: size[2],
                      child: Text(
                        changeValue,
                        style: vpTextStyle.body14?.copyWith(
                          color: item.isItemDefault ? themeData.black : color,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ),
                  Visibility(
                    visible: widget.showPercent,
                    child: SizedBox(
                      width: size[3],
                      child: Text(
                        percent,
                        style: vpTextStyle.body14?.copyWith(
                          color: item.isItemDefault ? themeData.black : color,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ),
                  SizedBox(
                    width: size[4],
                    child: Text(
                      diff,
                      style: vpTextStyle.body14?.copyWith(
                        color: item.diffColor,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  SizedBox(
                    width: size[5],
                    child: buildDisplayModeView(item, DisplayMode.volume),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget buildDisplayModeView(StockDetailEntity item, DisplayMode displayMode) {
    return StreamBuilder<bool>(
      stream: _streamChangedProperty(displayMode),
      builder: (context, snapshot) {
        final priceChanged = snapshot.data ?? false;
        return Container(
          // padding: const EdgeInsets.only(left: SizeUtils.kSize8),
          height: SizeUtils.kSize40,
          alignment:
              displayMode == DisplayMode.price
                  ? Alignment.center
                  : Alignment.centerRight,
          color:
              priceChanged
                  ? (displayMode == DisplayMode.volume
                      ? themeData.yellow
                      : item.colorByPrice)
                  : ColorDefine.transparent,
          child: Text(
            displayValueByMode(displayMode, item),
            textAlign: TextAlign.right,
            style: (displayMode == DisplayMode.volume
                    ? vpTextStyle.body14
                    : vpTextStyle.body14)
                ?.copyWith(
                  color:
                      item.isItemDefault
                          ? themeData.black
                          : displayMode == DisplayMode.volume
                          ? item.totalVolume == 0
                              ? themeData.black
                              : priceChanged
                              ? ColorDefine.white
                              : themeData.text700
                          : priceChanged
                          ? ColorDefine.white
                          : item.colorByPrice,
                ),
          ),
        );
      },
    );
  }

  Stream<bool> _streamChangedProperty(DisplayMode mode) {
    switch (mode) {
      case DisplayMode.changePercent:
        return _bloc
            .stockChangedProperties[StockChangedProperties.changePercent]!
            .stream;
      case DisplayMode.volume:
        return _bloc
            .stockChangedProperties[StockChangedProperties.totalTrading]!
            .stream;
      default:
        return _bloc
            .stockChangedProperties[StockChangedProperties.lastPrice]!
            .stream;
    }
  }

  String displayValueByMode(DisplayMode mode, StockDetailEntity data) {
    if (data.isItemDefault) {
      return '-';
    }
    switch (mode) {
      case DisplayMode.changePercent:
        return data.changePercent!.getChangePercentDisplay(excludeZero: true);
      case DisplayMode.volume:
        return StockHelper.formatVol(data.totalVolume, formatDerivative: true);
      default:
        return data.closePrice.toStringAsFixed(2);
    }
  }
}
