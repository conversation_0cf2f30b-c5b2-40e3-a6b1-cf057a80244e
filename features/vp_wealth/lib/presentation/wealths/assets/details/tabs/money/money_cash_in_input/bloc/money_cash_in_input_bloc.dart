import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/money_domain/money_repository.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/money_cash_in/bank.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/money_cash_in/bank_url_authenticate.dart';
import 'package:vp_wealth/router/wealth_router.dart';

part 'money_cash_in_input_event.dart';
part 'money_cash_in_input_state.dart';

class MoneyCashInInputBloc
    extends Bloc<MoneyCashInInputEvent, MoneyCashInInputState> {
  MoneyCashInInputBloc() : super(const MoneyCashInInputState()) {
    on<MoneyCashInInputSelectedSubAccount>(
      _onMoneyCashInInputSelectedSubAccount,
    );
    on<MoneyCashInInputSubmitted>(_onMoneyCashInInputSubmitted);
    on<MoneyCashInInputPressed>(_onMoneyCashInInputPressed);
    on<MoneyCashInInputGetAccountTransfered>(
      _onMoneyCashInInputGetAccountTransfered,
    );
    on<MoneyCashInInputGetAccountBalance>(_onMoneyCashInInputGetAccountBalance);
  }

  void _onMoneyCashInInputSelectedSubAccount(
    MoneyCashInInputSelectedSubAccount event,
    Emitter<MoneyCashInInputState> emit,
  ) {
    emit(
      state.copyWith(
        selectSubAccount: event.selectedSubAccount,
        subAccountStatus: MoneyCashInSelectSubAccountStatus.valid,
      ),
    );
    // because validate for button by input money
    if (state.money.isNotEmpty) {
      validateScreen(emit);
    }
  }

  void validateScreen(Emitter<MoneyCashInInputState> emit) {
    var countError = 0;

    // check select sub account
    if (state.selectSubAccount == null) {
      emit(
        state.copyWith(
          subAccountStatus: MoneyCashInSelectSubAccountStatus.invalid,
        ),
      );
      countError++;
    } else {
      emit(
        state.copyWith(
          subAccountStatus: MoneyCashInSelectSubAccountStatus.valid,
        ),
      );
    }

    // check input money
    if (state.money.isEmpty) {
      emit(
        state.copyWith(inputMoneyStatus: MoneyCashInInputMoneyStatus.invalid),
      );
      countError++;
    } else if (state.money == '0') {
      emit(
        state.copyWith(inputMoneyStatus: MoneyCashInInputMoneyStatus.invalid),
      );
      countError++;
    } else if (int.parse(state.money.replaceAll(',', '')) >
        int.parse(state.balance)) {
      emit(
        state.copyWith(
          inputMoneyStatus: MoneyCashInInputMoneyStatus.invalidNoEnoughMoney,
        ),
      );
      countError++;
    } else {
      emit(state.copyWith(inputMoneyStatus: MoneyCashInInputMoneyStatus.valid));
    }

    // check button
    if (countError == 0) {
      emit(state.copyWith(status: MoneyCashInInputStatus.valid));
    } else {
      emit(state.copyWith(status: MoneyCashInInputStatus.invalid));
    }
  }

  Future<void> _onMoneyCashInInputSubmitted(
    MoneyCashInInputSubmitted event,
    Emitter<MoneyCashInInputState> emit,
  ) async {
    validateScreen(emit);
    if (state.status == MoneyCashInInputStatus.valid) {
      final context =
          GetIt.instance<NavigationService>().navigatorKey.currentContext!;
      try {
        showDialogLoading();
        final result = await GetIt.instance
            .get<MoneyRepository>()
            .postInitialCashInInput(
              amount: num.parse(state.money.replaceAll(',', '')),
              accountType: state.selectSubAccount!.accountType.name,
            );
        hideDialogLoading();
        if (result.isSuccess()) {
          context.push(
            WealthRouter.webViewLink,
            extra: BankUrlAuthenticate(
              location: result.data['location'],
              isOTPFromCashInput: true,
            ),
          );
          return;
        } else if (result.httpCode == 200 && result.status != 1) {
          showMessageError(result.message!);
        }
      } catch (e) {
        hideDialogLoading();
        showError(e);
      }
    }
  }

  void _onMoneyCashInInputPressed(
    MoneyCashInInputPressed event,
    Emitter<MoneyCashInInputState> emit,
  ) {
    emit(state.copyWith(money: event.pressNum));
    validateScreen(emit);
  }

  void _onMoneyCashInInputGetAccountTransfered(
    MoneyCashInInputGetAccountTransfered event,
    Emitter<MoneyCashInInputState> emit,
  ) {
    emit(state.copyWith(selectBank: event.selectedBank));
    final context =
        GetIt.instance<NavigationService>().navigatorKey.currentContext;
    context!.pop();
  }

  void _onMoneyCashInInputGetAccountBalance(
    MoneyCashInInputGetAccountBalance event,
    Emitter<MoneyCashInInputState> emit,
  ) async {
    try {
      showDialogLoading();
      final result =
          await GetIt.instance.get<MoneyRepository>().getAccountBalance();
      hideDialogLoading();

      emit(state.copyWith(balance: result.availableBalance));
    } catch (e) {
      emit(state.copyWith(balance: '0'));
      hideDialogLoading();
      showError(e);
    }
  }
}
