import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_common/widget/vpbank_loading.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/custom_widget/header_widget.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_common/lang/money_key_lang.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_common/lang/money_localized_values.dart';
import 'package:vp_wealth/presentation/wealths/plan/support_person/app_pincode_widget.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/progress_bar.dart';

import 'ParamRequestMB.dart';
import 'mb_otp_bloc.dart';

class MbOtpPage extends StatefulWidget {
  const MbOtpPage({
    Key? key,
    this.paramRequestMB,
  }) : super(key: key);

  // final String? sessionID;
  final ParamRequestMB? paramRequestMB;

  @override
  State<MbOtpPage> createState() => _MbPageState();
}

class _MbPageState extends State<MbOtpPage> {
  late MbOtpBloc _bloc;

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: BlocProvider(
        create: (context) => _bloc,
        child: Scaffold(
          body: SafeArea(
            child: Column(
              children: [
                Expanded(
                    child: _ContentView(
                  goToCashInMBBank:
                      widget.paramRequestMB?.goToCashInMBBank ?? false,
                )),
                const _Loading()
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _bloc = MbOtpBloc()..init(widget.paramRequestMB);
  }

  @override
  void dispose() {
    _bloc.dispose();
    super.dispose();
  }
}

class _ContentView extends StatelessWidget {
  final String? sessionId;
  final bool? goToCashInMBBank;

  // ignore: unused_element
  const _ContentView({Key? key, this.sessionId, this.goToCashInMBBank})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<MbOtpBloc>();

    return Column(
      children: [
        HeaderWidget(
          back: false,
          icon: Assets.icons.icClose.svg(),
          actionRight: () {
            Navigator.of(context).pop();
          },
          subTitle: getMoneyLang(MoneyKeyLang.assets),
          title: bloc.title,
        ),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(
                vertical: SizeUtils.kSize8, horizontal: SizeUtils.kSize16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                (goToCashInMBBank ?? false)
                    ? Text(WealthStock.current.systemSendOTP,
                        style: vpTextStyle.body16,
                        textAlign: TextAlign.start)
                    : RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                                text: WealthStock.current.mpOtp,
                                style: vpTextStyle.body14?.copyWith(height: 1.5)),
                            TextSpan(
                                text:
                                    ' ${('Session().iamUserInfo?.mobile' ?? '').replaceRange(3, 7, "****")}',
                                style: vpTextStyle.body14
                                    ?.copyWith(height: 1.5)),
                          ],
                        ),
                        textAlign: TextAlign.start),
                kSpacingHeight16,
                _Pincode(),
                BlocBuilder<MbOtpBloc, MbOtpState>(
                  buildWhen: (previous, current) =>
                      previous.messErr != current.messErr,
                  builder: (context, state) {
                    if (state.messErr.isEmpty) {
                      return const SizedBox();
                    }
                    return Padding(
                      padding: const EdgeInsets.only(top: 12),
                      child: AutoSizeText(state.messErr,
                          maxLines: 2,
                          style: vpTextStyle.body14
                              ?.copyWith(color: themeData.red),
                          textAlign: TextAlign.center),
                    );
                  },
                ),
                kSpacingHeight8,
                _TimerView(goToCashInMBBank: goToCashInMBBank ?? false),
              ],
            ),
          ),
        ),
        BlocBuilder<MbOtpBloc, MbOtpState>(
          // buildWhen: (previous, current) =>
          //     previous.enableButton != current.enableButton,
          builder: (context, state) {
            return StepBottomView(
                step: 2,
                total: 2,
                executeCallbackWhenFinishRevert: true,
                enableButton: state.enableButton,
                onBack: () => Navigator.pop(context),
                onNext: () {
                  if (state.timer > 0) {
                    if (goToCashInMBBank ?? false) {
                      bloc.confirmOtpCashIn(context);
                    } else {
                      bloc.confirmOtp(context);
                    }
                  }
                });
          },
        ),
        // _Loading()
      ],
    );
  }
}

class _TimerView extends StatelessWidget {
  final bool goToCashInMBBank;

  const _TimerView({super.key, required this.goToCashInMBBank});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MbOtpBloc, MbOtpState>(
      buildWhen: (previous, current) =>
          previous.isShowTimer != current.isShowTimer,
      builder: (context, state) {
        return state.isShowTimer
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '${WealthStock.current.systemSendOTP} ',
                    style: vpTextStyle.body14?.copyWith(
                      color: themeData.gray500,
                    ),
                  ),
                  TweenAnimationBuilder(
                    tween: Tween(begin: 120, end: 0.0),
                    duration: const Duration(seconds: 120),
                    builder: (_, dynamic value, child) {
                      Future.delayed(Duration.zero, () async {
                        context.read<MbOtpBloc>().emitTimer(value);
                      });
                      return Text(
                        '${value.toInt() < 10 ? '0${value.toInt()}' : value.toInt()}',
                        style: TextStyle(color: themeData.primary),
                      );
                    },
                  ),
                  Text(
                    ' s',
                    style: vpTextStyle.body14?.copyWith(
                      color: themeData.gray500,
                    ),
                  )
                ],
              )
            : const SizedBox.shrink();
      },
    );
  }
}

class _Pincode extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final bloc = context.read<MbOtpBloc>();
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: SizeUtils.kSize8),
      height: 40,
      child: AppPincodeWidget(
          controller: bloc.txtPincode,
          count: 8,
          onChanged: (value, count) {
            if (value.length < count) {
              bloc.emitMsgErr('');
            }
            bloc.setEnableButton(value.length == count);
          },
          onCompleted: (value) {
            // bloc.apiVerifyOTP();
          },
          beforeTextPaste: (value) {
            try {
              int otp = int.parse(value ?? '');
              return true;
            } catch (e) {
              return false;
            }
          }),
    );
  }
}

class _Loading extends StatelessWidget {
  const _Loading({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: SizeUtils.kSize8),
      child: BlocBuilder<MbOtpBloc, MbOtpState>(
        buildWhen: (previous, current) =>
            previous.isLoading != current.isLoading,
        builder: (context, state) {
          return state.isLoading
              ? const Center(
                  child: VPBankLoading(
                  size: SizeUtils.kSize40,
                ))
              : const SizedBox.shrink();
        },
      ),
    );
  }
}
