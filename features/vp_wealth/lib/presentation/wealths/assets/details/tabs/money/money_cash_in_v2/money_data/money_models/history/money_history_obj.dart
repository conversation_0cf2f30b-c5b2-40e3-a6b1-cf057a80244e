// <PERSON>ai giao dich la noi bo, chuyen tien ra hay chuyen tien vao
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_common/lang/money_key_lang.dart';


// Loai giao dich
enum MoneyTransferTypeEnum { internal, cashOut, cashIn, all }

// Loai tai khoan la tk thuong, tai khoan ky quy, hay tai khoan bank
enum MoneyTransferAccountEnum { normal, margin, all, bond, derivative }

extension SubAccountModelToItemSelect on SubAccountModel {
  MoneyTransferAccountEnum toIdentify() {
    if (isNormal) {
      return MoneyTransferAccountEnum.normal;
    }
    if (isMargin) {
      return MoneyTransferAccountEnum.margin;
    }
    if (isBond) {
      return MoneyTransferAccountEnum.bond;
    }
    return MoneyTransferAccountEnum.all;
  }

  String toTitle() {
    if (isNormal) {
      return MoneyKeyLang.accountNormal;
    }
    if (isMargin) {
      return MoneyKeyLang.accountMargin;
    }
    if (isBond) {
      return 'Trái phiếu';
    }
    return MoneyKeyLang.all;
  }

  String toFullName() {
    if (isNormal) {
      return MoneyKeyLang.accountNormalFullName;
    }

    if (isMargin) {
      return MoneyKeyLang.accountMarginFullName;
    }

    if (isBond) {
      return MoneyKeyLang.accountBondFullName;
    }

    return MoneyKeyLang.all;
  }
}

// Loai thoi gian filter
enum MoneyFilterTime {
  oneMonth,
  twoMonth,
  threeMonth,
  sixMonth,
  oneYear,
  custom
}

// class MoneyHistoryModalFilterObj extends Equatable {
//   ItemSelect? itemSelectedAccount;
//   ItemSelect? itemSelectedTime;
//   List<DateTime?> rangeDateSelected = [];

//   MoneyHistoryModalFilterObj();

//   // void setDefault
//   void setDefault(BuildContext context) {
//     itemSelectedAccount = ItemSelect(
//       title: getMoneyLang(
//         GetIt.instance.get<SubAccountCubit>().toTitle(),
//       ),
//       id: GetIt.instance.get<SubAccountCubit>().toIdentify(),
//     );

//     itemSelectedTime = ItemSelect(
//       title: '1 ${getMoneyLang(MoneyKeyLang.month)}',
//       id: MoneyFilterTime.oneMonth,
//     );
//   }

//   // Kiem tra xem co phai default hay khong
//   bool isValueDefault() {
//     return (itemSelectedAccount?.id ==
//             AppCache().defaultSubAccount.toIdentify()) &&
//         (itemSelectedTime?.id == MoneyFilterTime.oneMonth);
//   }

//   // Xoa du lieu da luu khi filter
//   void reset(BuildContext context) {
//     setDefault(context);
//     rangeDateSelected.clear();
//   }

//   // Lay startDate
//   DateTime getStartDate() {
//     return rangeDateSelected.length >= 2
//         ? rangeDateSelected[0] ?? DateTime.now()
//         : DateTime.now();
//   }

//   // Lay end date
//   DateTime getEndDate() {
//     return rangeDateSelected.length >= 2
//         ? rangeDateSelected[1] ?? DateTime.now()
//         : DateTime.now();
//   }

//   // Lay so thang can lay du lieu
//   int getMonthFilterData() {
//     Map<MoneyFilterTime, int> map = {
//       MoneyFilterTime.oneMonth: 1,
//       MoneyFilterTime.twoMonth: 2,
//       MoneyFilterTime.threeMonth: 3,
//       MoneyFilterTime.sixMonth: 6,
//       MoneyFilterTime.oneYear: 12,
//     };
//     return map[itemSelectedTime?.id] ?? 1;
//   }

//   @override
//   List<Object?> get props =>
//       [itemSelectedAccount, itemSelectedTime, rangeDateSelected];
// }

class MoneyHistoryRangeTimeObj {
  final List<DateTime?>? rangeTime;
  final bool? isShowRangeView;

  MoneyHistoryRangeTimeObj({this.rangeTime, this.isShowRangeView});
}
