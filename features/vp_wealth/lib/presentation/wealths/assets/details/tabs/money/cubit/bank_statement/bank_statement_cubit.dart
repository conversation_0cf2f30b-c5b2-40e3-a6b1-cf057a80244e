// Cubit để quản lý danh sách TransactionStateOfMoneyModel
import 'dart:async';

import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/data/model/assets/item_assets_model.dart';
import 'package:vp_wealth/data/model/money_statement_model.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';

import 'bank_statement_state.dart';

class BankStatementCubit extends Cubit<BankStatementState> {
  BankStatementCubit() : super(BankStatementInitState());

  late DateTime today = DateTime.now();

  late DateTime resetEndDate = today;

  late DateTime resetStartDate = DateTime(
    today.year,
    today.month - 3,
    today.day,
  );

  late DateTime startDate = resetStartDate;

  late DateTime endDate = resetEndDate;

  late DateTime maxDate = today;

  final moneyRepository = GetIt.instance.get<WealthRepository>();

  SubAccountModel subAccSelected = GetIt.instance.get<SubAccountCubit>().defaultSubAccount;

  List<SubAccountModel> stockAccounts = GetIt.instance.get<SubAccountCubit>().subAccountsStock;

  StreamSubscription? subscription;

  bool hasMore = true;

  bool get haveFilter =>
      !startDate.isSameDay(resetStartDate) || !endDate.isSameDay(resetEndDate);

  final moneyData = <MoneyStatementModel>[];

  List<MoneyStatementModel> get data =>
      moneyData.where((e) => (e.transactionNum ?? '').hasData).toList()..sort(
        (a, b) => b.busDate?.compareTo(a.busDate ?? DateTime.now()) ?? 0,
      );

  MoneyStatementModel get model =>
      moneyData.length > 1 ? moneyData[1] : MoneyStatementModel();

  num get beginingBalance => model.beginingBalance ?? 0;

  num get endingBalance => model.endingBalance ?? 0;

  num get arising => endingBalance - beginingBalance;

  double get totalCreditAmt =>
      data.isEmpty
          ? 0
          : data
              .map((e) => e.creditAmt ?? 0)
              .reduce((a, b) => a + b)
              .toDouble();

  double get totalDeditAmt =>
      data.isEmpty
          ? 0
          : (data
                  .map((e) => e.debitAmt ?? 0)
                  .reduce((a, b) => a + b)
                  .toDouble() *
              -1.0);

  void onSubAccountSelected(SubAccountModel account) {
    subAccSelected = account;

    // getCashStatementHist();
  }

  void onDatePicked(List<DateTime?> dates, ItemAssetsModel model) {
    if (dates.isEmpty || dates.every((e) => e == null)) return;

    if (dates.length == 2) {
      startDate = dates.first!;
      endDate = dates.last!;
    }

    if (dates.length == 1) {
      startDate = dates.first!;
      endDate = today;
    }

    emit(BankStatementSelectState(startDate, endDate));
    getCashStatementHist(model);
  }

  Future getCashStatementHist(ItemAssetsModel model) async {
    subscription?.cancel();
    moneyData.clear();
    hasMore = true;

    emit(BankStatementLoadingState());

    final accounts =
        subAccSelected.isTypeAll ? stockAccounts : [subAccSelected];

    final futures = accounts.map(
      (e) => moneyRepository.getCashStatementHist(
        subAccountId: model.accountNo.toString(),
        accountId: model.accountNo.toString(),
        fromDate: startDate.toDate(),
        toDate: endDate.toDate(),
      ),
    );

    final stream = Future.wait(futures).asStream();

    subscription =
        stream.listen(null)
          ..onData((data) {
            hasMore = false;

            for (final list in data) {
              moneyData.addAll(list);
            }

            /// sort data by time
            moneyData.sort((a, b) {
              if (a.busDate == null || b.busDate == null) return -1;

              if (a.busDate!.isAfter(b.busDate!)) return -1;

              if (a.busDate!.isBefore(b.busDate!)) return 1;

              return 0;
            });

            emit(BankStatementDataLoadedState());
          })
          ..onError((e, _) => emit(BankStatementErrorState(e)));
  }

  @override
  Future<void> close() {
    subscription?.cancel();
    return super.close();
  }

  // Hàm để tải dữ liệu
  void loadTransactions(ItemAssetsModel model) {
    emit(BankStatementSelectState(startDate, endDate));
    getCashStatementHist(model);
  }
}
