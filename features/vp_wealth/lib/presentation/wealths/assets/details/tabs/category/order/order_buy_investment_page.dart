import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_text_input_formatter_utils.dart';
import 'package:vp_common/utils/app_time_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_design_system/widget/appbar/appbar_view.dart';
import 'package:vp_design_system/widget/bottom_action/vp_bottom_action.dart';
import 'package:vp_design_system/widget/input_view/input_view.dart';
import 'package:vp_design_system/widget/scaffold/scaffold_view.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/model/assets/item_assets_model.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/data/utils/app_keyboard_utils.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/data/utils/padding_extends.dart';
import 'package:vp_wealth/data/utils/wealth_notification_dialog.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/place_order/utils/ext.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/category/order/cubit/order_buy_cubit.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/category/order/widget/item_order_investment_stock.dart';
import 'package:vp_wealth/presentation/wealths/order/investment_command/accept_command_page.dart';
import 'package:vp_wealth/presentation/wealths/order/investment_command/cubit/investment_command_cubit.dart';
import 'package:vp_wealth/presentation/wealths/widgets/utils/item_row_text_info_basic.dart';
import 'package:vp_wealth/router/wealth_router.dart';

class OrderBuyInvestmentPage extends StatefulWidget {
  final ItemAssetsModel model;

  const OrderBuyInvestmentPage({super.key, required this.model});

  @override
  State<OrderBuyInvestmentPage> createState() => _OrderBuyInvestmentPageState();
}

class _OrderBuyInvestmentPageState extends State<OrderBuyInvestmentPage> {
  final OrderBuyCubit _cubit = OrderBuyCubit();
  final FocusNode _initialInvestmentAmountFocusNode = FocusNode();

  ItemAssetsModel get _model => widget.model;

  @override
  void initState() {
    super.initState();
    _cubit.onLoadData(_model);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => AppKeyboardUtils.dismissKeyboard(),
      child: VPScaffold(
        appBar: VPAppBar.flows(title: 'Đặt lệnh mua'),
        body: Column(
          children: [
            kSpacingHeight8,
            Expanded(
              child: Container(
                color: vpColor.backgroundElevation0,
                child: ListView(
                  children: [
                    _infoBasicOrder,
                    _divider,
                    _initialInvestmentAmount,
                    Container(height: 1, color: vpColor.strokeNormal),
                    _listStockCategory,
                  ],
                ),
              ),
            ),
          ],
        ),
        bottomNavigationBar: _buildActionBottom,
      ),
    );
  }

  Widget get _infoBasicOrder {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: SizeUtils.kSize16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            children: [
              kSpacingHeight6,
              ItemRowTextInfoBasic(
                title: 'Ngày giao dịch',
                value: AppTimeUtils.getDateTimeString(dateTime: DateTime.now()),
              ),
              ItemRowTextInfoBasic(
                title: 'Số tài khoản giao dịch',
                value:
                    context.read<AuthCubit>().userInfo?.userinfo?.custodycd ??
                    '',
              ),
              const ItemRowTextInfoBasic(
                title: 'Loại lệnh',
                value: 'Lệnh thường',
              ),
              kSpacingHeight6,
            ],
          ),
        ],
      ),
    );
  }

  Widget get _initialInvestmentAmount {
    return Padding(
      padding: const EdgeInsets.all(SizeUtils.kSize16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 4.0),
                child: Text(
                  'Số tiền đầu tư',
                  style: vpTextStyle.body14?.copyWith(
                    color: vpColor.textTertiary,
                  ),
                ),
              ),
              const Spacer(),
              Expanded(
                flex: 3,
                child: Stack(
                  children: [
                    BlocBuilder<OrderBuyCubit, OrderBuyState>(
                      bloc: _cubit,
                      buildWhen:
                          (previous, current) =>
                              previous.showErrorEditInvestmentAmount !=
                              current.showErrorEditInvestmentAmount,
                      builder: (context, state) {
                        return VPTextField.small(
                          caption: (color) {
                            return Visibility(
                              visible: state.showErrorEditInvestmentAmount,
                              child: Row(
                                children: [
                                  Assets.icons.errorTriangle
                                      .svg()
                                      .paddingRight8(),
                                  Text(
                                    state.messageErrorEditInvestmentAmount,
                                    style: vpTextStyle.captionRegular?.copyWith(
                                      color: vpColor.textAccentRed,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                          enableInteractiveSelection: false,
                          inputType:
                              !state.showErrorEditInvestmentAmount
                                  ? InputType.rest
                                  : InputType.error,
                          keyboardType: TextInputType.phone,
                          inputFormatters: [CurrencyInputFormatter()],
                          focusNode: _initialInvestmentAmountFocusNode,
                          autofocus: false,
                          maxLength: 21,
                          hintText: WealthStock.current.hintTextEnterAmount,
                          onChanged:
                              (value) => _cubit.onValidateEditInvestmentAmount(
                                value,
                                _model,
                              ),
                          controller: _cubit.textInvestmentAmountController,
                        );
                      },
                    ),
                    Positioned(
                      right: 12,
                      top: 3,
                      child: Text(
                        ' đ',
                        style: vpTextStyle.body14?.copyWith(
                          color: vpColor.textPrimary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          BlocBuilder<OrderBuyCubit, OrderBuyState>(
            bloc: _cubit,
            buildWhen:
                (previous, current) =>
                    previous.availableBuyPower != current.availableBuyPower,
            builder: (context, state) {
              return RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: 'Sức mua khả dụng: ',
                      style: vpTextStyle.body14?.copyWith(
                        color: vpColor.textPrimary,
                      ),
                    ),
                    TextSpan(
                      text: state.availableBuyPower.valueText,
                      style: vpTextStyle.subtitle14?.copyWith(
                        color: vpColor.textAccentGreen,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget get _listStockCategory {
    return BlocBuilder<OrderBuyCubit, OrderBuyState>(
      bloc: _cubit,
      buildWhen: (previous, current) => previous.plan != current.plan,
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            kSpacingHeight16,
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: SizeUtils.kSize16,
              ),
              child: Text(
                WealthStock.current.investmentCategory,
                style: vpTextStyle.subtitle16?.copyWith(
                  color: vpColor.textPrimary,
                ),
              ),
            ),
            _listStock(),
            kSpacingHeight16,
          ],
        );
      },
    );
  }

  Widget _listStock() {
    return BlocBuilder<OrderBuyCubit, OrderBuyState>(
      bloc: _cubit,
      buildWhen:
          (previous, current) =>
              previous.plan != current.plan ||
              previous.investmentAmount != current.investmentAmount,
      builder: (context, state) {
        List<ItemData> listStock = state.plan?.itemList ?? [];
        if (listStock.isEmpty) {
          return const SizedBox.shrink();
        }
        return Column(
          children:
              listStock
                  .asMap()
                  .map(
                    (index, e) => MapEntry(
                      index,
                      ItemOrderInvestmentStock(
                        data: e,
                        periodicAmount: state.investmentAmount,
                      ),
                    ),
                  )
                  .values
                  .toList(),
        );
      },
    );
  }

  Widget get _buildActionBottom {
    return BlocBuilder<OrderBuyCubit, OrderBuyState>(
      bloc: _cubit,
      buildWhen:
          (previous, current) =>
              previous.showErrorEditInvestmentAmount !=
              current.showErrorEditInvestmentAmount,
      builder: (context, state) {
        return VPBottomActionView.twoButton(
          disableRightButton: state.showErrorEditInvestmentAmount,
          leftTextButton: WealthStock.current.buttonBack,
          rightTextButton: 'Tiếp tục',
          leftAction: () => Navigator.pop(context),
          rightAction:
              () => _cubit.onOrderSummary(
                (model) => context.push(
                  WealthRouter.acceptCommandPage,
                  extra: AcceptCommandArguments(
                    transactionTypeName: 'Đầu tư chủ động',
                    model: model,
                    cubit: InvestmentCommandCubit(),
                  ),
                ),
                () => _onDialogHaveNotEnoughMoneyInvestment(),
              ),
        );
      },
    );
  }

  void _onDialogHaveNotEnoughMoneyInvestment() {
    showNotifyDialog(
      barrierDismissible: false,
      context: context,
      title: 'Số tiền đầu tư chưa đủ',
      iconSize: 96,
      contentWidget: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          children: [
            TextSpan(
              text:
                  'Số tiền phân bổ đầu tư cho một CP trong danh mục tối thiểu là ',
              style: vpTextStyle.body14?.copyWith(color: vpColor.textSecondary),
            ),
            TextSpan(
              text: '100.000 đ',
              style: vpTextStyle.body14?.copyWith(color: vpColor.textBrand),
            ),
            TextSpan(
              text:
                  '. Quý khách vui lòng điều chỉnh số tiền đầu tư để thiết lập thành công.',
              style: vpTextStyle.body14?.copyWith(color: vpColor.textSecondary),
            ),
          ],
        ),
      ),
      image: Assets.icons.iconWarning.path,
      textButtonLeft: WealthStock.current.buttonClose,
      colorButtonRight: themeData.primary,
      colorButtonLeft: themeData.bgPopup,
      colorBorderButtonLeft: themeData.gray700,
      textStyleLeft: vpTextStyle.body16?.copyWith(color: themeData.gray700),
      onPressedLeft: () => Navigator.pop(context),
      // textButtonRight: 'Điều chỉnh lại',
      // onPressedRight: () => Navigator.pop(context),
    );
  }

  Widget get _divider {
    return SizedBox(
      height: 2,
      width: double.infinity,
      child: ColoredBox(color: themeData.buttonDisableBg),
    );
  }
}
