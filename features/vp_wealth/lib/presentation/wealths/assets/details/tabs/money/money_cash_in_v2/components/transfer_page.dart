import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:path_provider/path_provider.dart';
import 'package:vp_common/utils/app_log_utils.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/custom_widget/app_snackbar_utils.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/data/utils/download_util/base_download_file_manager.dart';
import 'package:vp_wealth/data/utils/download_util/download_file_manager.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/components/copy_utils.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_common/lang/money_key_lang.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_common/lang/money_localized_values.dart';

import '../cubit/money_cash_in_v2_cubit.dart';

class TransferPage extends StatefulWidget {
  const TransferPage({Key? key}) : super(key: key);

  @override
  State<TransferPage> createState() => _TransferPageState();
}

class _TransferPageState extends State<TransferPage> {
  GlobalKey globalKey = GlobalKey();

  Future<ByteData?> _capturePng(GlobalKey globalKey) async {
    RenderRepaintBoundary boundary =
        globalKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
    ui.Image image = await boundary.toImage(pixelRatio: 2.0);
    return await image.toByteData(format: ui.ImageByteFormat.png);
  }

  void _saveImageQr(globalKey, context) async {
    final bytes = await _capturePng(globalKey);
    final nameFile = DateTime.now().millisecondsSinceEpoch;
    await DownloadFileManager()
        .saveFileWithByteData(
          byteData: bytes!,
          fileName: 'qrcode_vpbanks_$nameFile',
          extension: FileExtension.png,
        )
        .then(
          (value) =>
              showSnackBar(context, getMoneyLang(MoneyKeyLang.downloadSuccess)),
        )
        .onError(
          (error, stackTrace) => showSnackBar(
            context,
            getMoneyLang(MoneyKeyLang.downloadFail),
            isSuccess: false,
          ),
        );
  }

  void _shareQrcode(globalKey) async {
    try {
      // final isSuccess = await SignUpPermissionUtils.checkPermissionFile();
      final isSuccess = true;
      if (isSuccess) {
        Directory? appDocDir;

        if (Platform.isAndroid) {
          appDocDir = await getExternalStorageDirectory();
        } else {
          appDocDir = await getApplicationDocumentsDirectory();
        }
        String appDocPath = appDocDir?.path ?? '';
        if (appDocPath.isEmpty) {
          return;
        }
        final nameFile = DateTime.now().millisecondsSinceEpoch;
        String pathSave = '$appDocPath/qrcode_vpbanks_$nameFile.png';
        final bytes = await _capturePng(globalKey);
        File(pathSave).writeAsBytes(bytes!.buffer.asUint8List());
        // FlutterShare.shareFile(
        //   title: 'Qrcode ${getMoneyLang(MoneyKeyLang.cashInMoney)}',
        //   filePath: pathSave,
        // );
      }
    } catch (e) {
      dlog(e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MoneyCashInV2Cubit, MoneyCashInV2State>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.all(SizeUtils.kSize16),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Text(
                    getMoneyLang(MoneyKeyLang.informationTransfer),
                    style: vpTextStyle.body14?.copyWith(color: themeData.black),
                  ),
                ),
                kSpacingHeight12,
                Text(
                  getMoneyLang(MoneyKeyLang.vpbankInfo),
                  style: vpTextStyle.body14?.copyWith(color: themeData.gray700),
                ),
                kSpacingHeight12,
                Container(
                  height: 72,
                  padding: const EdgeInsets.symmetric(
                    horizontal: SizeUtils.kSize12,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(SizeUtils.kSize8),
                    color: themeData.gray100,
                  ),
                  child: GestureDetector(
                    onTap: () {
                      copyToClipboard(context, state.shareCode);
                    },
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Assets.images.icLogoBank.image(height: 40, width: 40),
                        const SizedBox(width: SizeUtils.kSize16),
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'VPBANKS - ${'Session().userInfo?.userinfo?.fullname'}',
                                overflow: TextOverflow.ellipsis,
                                style: vpTextStyle.captionRegular?.copyWith(
                                  color: themeData.gray700,
                                ),
                              ),
                              Text(
                                state.shareCode,
                                overflow: TextOverflow.ellipsis,
                                style: vpTextStyle.body14,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: SizeUtils.kSize10),
                        CircleAvatar(
                          child: Assets.icons.icAddCopy.svg(),
                          radius: 16,
                          backgroundColor: themeData.bgMain,
                        ),
                      ],
                    ),
                  ),
                ),
                kSpacingHeight12,
                Center(
                  child: Text(
                    getMoneyLang(MoneyKeyLang.scanQR),
                    style: vpTextStyle.body14?.copyWith(
                      color: themeData.gray700,
                    ),
                  ),
                ),
                kSpacingHeight12,
                RepaintBoundary(
                  key: globalKey,
                  child: Center(
                    child: BlocBuilder<MoneyCashInV2Cubit, MoneyCashInV2State>(
                      buildWhen:
                          (previous, current) =>
                              previous.retryCount != current.retryCount,
                      builder: (context, state) {
                        return CachedNetworkImage(
                          width: 200,
                          height: 200,
                          imageUrl:
                              'https://img.vietqr.io/image/VPB-${state.shareCode}-qr_only.png',
                          errorWidget:
                              (_, __, ___) => GestureDetector(
                                onTap:
                                    () =>
                                        context
                                            .read<MoneyCashInV2Cubit>()
                                            .onPressRetryQrImage(),
                                child: Icon(Icons.abc, weight: 200),
                                // child: Assets.icons.icRetryQrCashin.svg(
                                //   width: 200,
                                //   height: 200,
                                // ),
                              ),
                        );
                      },
                    ),
                  ),
                ),
                kSpacingHeight12,
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    TextButton.icon(
                      onPressed: () => _saveImageQr(globalKey, context),
                      icon: Assets.icons.icDownload.svg(),
                      label: Text(
                        getMoneyLang(MoneyKeyLang.download),
                        style: vpTextStyle.captionRegular?.copyWith(
                          color: themeData.gray700,
                        ),
                      ),
                    ),
                    const SizedBox(width: 20),
                    TextButton.icon(
                      onPressed: () => _shareQrcode(globalKey),
                      // icon: Assets.icons.icShare.svg(),
                      icon: Icon(Icons.abc),
                      label: Text(
                        getMoneyLang(MoneyKeyLang.share),
                        style: vpTextStyle.captionRegular?.copyWith(
                          color: themeData.gray700,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
