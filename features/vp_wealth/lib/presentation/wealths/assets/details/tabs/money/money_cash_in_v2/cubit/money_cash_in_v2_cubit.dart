import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_log_utils.dart';
import 'package:vp_common/utils/navigation_service.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart' as core;
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/data/utils/app_keyboard_utils.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/mbbank/2.mb_otp/ParamRequestMB.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/money_domain/money_repository.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/money_cash_in/bank.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/money_cash_in/bank_url_authenticate.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/transfer/money_account_recived_obj.dart';
import 'package:vp_wealth/router/wealth_router.dart';

import '../components/partner_code_status.dart';

part 'money_cash_in_v2_state.dart';

class MoneyCashInV2Cubit extends Cubit<MoneyCashInV2State> {
  MoneyCashInV2Cubit() : super(const MoneyCashInV2State());
  final inputMoneyController = TextEditingController();
  final focusNode = FocusNode();

  void initData() {
    final selectSubAccount =
        GetIt.instance
            .get<SubAccountCubit>()
            .subAccountsAllNoCommissionHaveDerivativeAccount
            .first;
    final shareCode = generateShareCode(selectSubAccount);
    emit(
      state.copyWith(selectSubAccount: selectSubAccount, shareCode: shareCode),
    );
    loadingData();
  }

  void loadingData() async {
    emit(state.copyWith(isLoading: true));
    await loadListBankPartner(isLoading: false);
    emit(state.copyWith(isLoading: false));
  }

  String generateShareCode(SubAccountModel obj) {
    final customerCode = 'Session().userInfo?.userinfo?.custodycd';
    if (obj.isNormal == true) {
      return '0${customerCode}1';
    } else if (obj.isMargin == true) {
      return '0${customerCode}6';
    } else if (obj.isBond == true) {
      return '0${customerCode}3';
    } else if (obj.isDerivative == true) {
      return '0${customerCode}8';
    } else {
      return '';
    }
  }

  void setSubAccount(SubAccountModel obj) {
    final shareCode = generateShareCode(obj);
    emit(
      state.copyWith(
        selectSubAccount: obj,
        shareCode: shareCode,
        retryCount: state.retryCount + 1,
      ),
    );
  }

  Future<void> setTransferAccount(Bank bank) async {
    emit(state.copyWith(selectBank: bank, isSelectedBank: true, balance: null));
    if (bank.getPartnerCodeStatus() == PartnerCodeStatus.VPB) {
      await getAccountBalance();
    }
  }

  void onChangedMoney(String value, PartnerCodeStatus? partner) {
    if (partner == null) return;
    validInputMoney(partner);
  }

  void validInputMoney(PartnerCodeStatus? partner) {
    if (partner == null) return;

    final money = inputMoneyController.text;
    var countError = 0;
    if (money.isEmpty) {
      emit(
        state.copyWith(inputMoneyStatus: MoneyCashInInputMoneyStatus.invalid),
      );
      countError++;
    } else if (money == '0') {
      emit(
        state.copyWith(inputMoneyStatus: MoneyCashInInputMoneyStatus.invalid),
      );
      countError++;
    } else if (partner == PartnerCodeStatus.VPB &&
        int.parse(money.replaceAll(',', '')) > int.parse(state.balance)) {
      emit(
        state.copyWith(
          inputMoneyStatus: MoneyCashInInputMoneyStatus.invalidNoEnoughMoney,
        ),
      );
      countError++;
    } else {
      emit(state.copyWith(inputMoneyStatus: MoneyCashInInputMoneyStatus.valid));
    }

    // check button
    if (countError == 0) {
      emit(
        state.copyWith(
          money: num.parse(money.replaceAll(',', '')),
          status: MoneyCashInInputStatus.valid,
        ),
      );
    } else {
      emit(state.copyWith(status: MoneyCashInInputStatus.invalid));
    }
  }

  Future<void> getAccountBalance() async {
    try {
      emit(state.copyWith(isLoading: true));
      final result =
          await core.GetIt.instance.get<MoneyRepository>().getAccountBalance();
      emit(state.copyWith(isLoading: false));

      emit(state.copyWith(balance: result.availableBalance));
      focusNode.requestFocus();
    } catch (e) {
      emit(state.copyWith(balance: '0'));
      emit(state.copyWith(isLoading: false));
      showError(e);
    }
  }

  Future<void> loadListBankPartner({bool isLoading = true}) async {
    try {
      if (isLoading) {
        emit(state.copyWith(isLoading: true));
      }
      List<Bank> listBankActive = [];
      List<Bank> listBankInActive = [];
      final list =
          await core.GetIt.instance.get<MoneyRepository>().getListBankPartner();
      if (isLoading) {
        emit(state.copyWith(isLoading: false));
      }
      for (var element in list) {
        if (element.status == Status.ACTIVE.name) {
          listBankActive.add(element);
        }
        if (element.status == Status.INACTIVE.name) {
          listBankInActive.add(element);
        }
      }
      emit(
        state.copyWith(
          listBank: listBankInActive,
          listBankActive: listBankActive,
        ),
      );
    } catch (e) {
      showError(e);
      if (isClosed) return;
      if (isLoading) {
        emit(state.copyWith(isLoading: false));
      }
    }
  }

  Future<void> submitCashIn(PartnerCodeStatus? partner) async {
    AppKeyboardUtils.dismissKeyboard();
    validInputMoney(partner);

    if (state.status == MoneyCashInInputStatus.valid) {
      if (partner != PartnerCodeStatus.VPB) {
        handleApiCashInMB();
      } else {
        handleApiCashInVPB();
      }
    }
  }

  Future<void> handleApiCashInVPB() async {
    try {
      emit(
        state.copyWith(isLoading: true, status: MoneyCashInInputStatus.init),
      );
      final result = await core.GetIt.instance
          .get<MoneyRepository>()
          .postInitialCashInInput(
            amount: state.money,
            accountType: state.selectSubAccount!.accountType.name ?? '',
          );
      emit(
        state.copyWith(isLoading: false, status: MoneyCashInInputStatus.valid),
      );
      if (result.isSuccess()) {
        final context =
            core.GetIt.instance<NavigationService>()
                .navigatorKey
                .currentContext!;
        context.push(
          WealthRouter.webViewLinkV2,
          extra: BankUrlAuthenticate(
            location: result.data['location'],
            isOTPFromCashInput: true,
          ),
        );
      } else if (result.httpCode == 200 && result.status != 1) {
        showMessageError(result.message!);
      }
    } catch (e) {
      emit(
        state.copyWith(isLoading: false, status: MoneyCashInInputStatus.valid),
      );
      showError(e);
    }
  }

  Future<void> handleApiCashInMB() async {
    try {
      String transferAmount = state.money.toString();
      String remark = 'MB Nạp tiền chứng khoán';
      String accountType = state.selectSubAccount!.accountType.name ?? '';
      String disCountCode = '';
      emit(
        state.copyWith(isLoading: true, status: MoneyCashInInputStatus.init),
      );
      final result = await core.GetIt.instance
          .get<MoneyRepository>()
          .cashInRequestMB(
            transferAmount: transferAmount,
            remark: remark,
            accountType: accountType,
            disCountCode: disCountCode,
          );
      emit(
        state.copyWith(isLoading: false, status: MoneyCashInInputStatus.valid),
      );
      if (result.isSuccess()) {
        var requestID = result.data['requestId'];
        dlog("requestID log: $requestID");
        setClearInputCash();
        inputMoneyController.text = '';
        final context =
            core.GetIt.instance<NavigationService>()
                .navigatorKey
                .currentContext!;
        context.push(
          'MoneyRouter.mbOtpPage',
          extra: ParamRequestMB(
            requestID: requestID,
            goToCashInMBBank: true,
            accountName: state.selectBank!.accountName,
          ),
        );
      } else if (result.httpCode == 200 && result.status != 1) {
        showMessageError(result.message!);
      }
    } catch (e) {
      emit(
        state.copyWith(isLoading: false, status: MoneyCashInInputStatus.valid),
      );
      showError(e);
    }
  }

  void setClearInputCash() {
    emit(
      state.copyWith(
        balance: '0',
        selectBank: null,
        money: 0,
        isSelectedBank: false,
      ),
    );
    inputMoneyController.clear();
  }

  void onPressRetryQrImage() {
    emit(state.copyWith(retryCount: state.retryCount + 1));
  }
}

enum Status { ACTIVE, INACTIVE }
