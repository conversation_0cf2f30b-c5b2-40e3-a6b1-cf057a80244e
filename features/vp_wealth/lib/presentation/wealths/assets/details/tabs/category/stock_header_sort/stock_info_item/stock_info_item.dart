import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/theme/theme_service.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/data/model/assets/item_assets_model.dart';
import 'package:vp_wealth/data/model/stock_detail_entity.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/presentation/place_order/utils/place_order_utils.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/cubit/detail_assets_cubit.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/cubit/stock_sort_helper.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/category/fu_stock_info_item/fu_stock_info_item_bloc.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/edit_plan/stock_changed_properties.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/data/stock_helper.dart';

class StockInfoItem extends StatefulWidget {
  final StockDisplayModeMixin displayModeMixin;

  final StockDetailEntity entity;

  final Portfolio portfolio;

  final EdgeInsets? toastPadding;

  final ValueChanged<StockDetailEntity>? onTap;

  final SortViewType sortViewType;

  final bool showPercent;

  const StockInfoItem({
    required this.entity,
    required this.displayModeMixin,
    required this.portfolio,
    this.onTap,
    this.toastPadding,
    this.sortViewType = SortViewType.fiveRows,
    this.showPercent = false,
    Key? key,
  }) : super(key: key);

  @override
  State<StockInfoItem> createState() => _StockInfoItemState();
}

class _StockInfoItemState extends State<StockInfoItem> {
  final _bloc = FuStockTabMarketItemBloc();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback(
      (_) => _bloc.init(item: widget.entity, mixin: widget.displayModeMixin),
    );
  }

  @override
  void dispose() {
    _bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTap != null ? () => widget.onTap!(widget.entity) : null,
      child: SizedBox(
        key: widget.key,
        child: StreamBuilder<StockDetailEntity>(
          stream: _bloc.streamStockItem,
          initialData: widget.entity,
          builder: (context, snapshot) {
            if (snapshot.data == null) {
              return const SizedBox.shrink();
            }
            StockDetailEntity item = snapshot.data!;

            final percent =
                item.isItemDefault
                    ? '-%'
                    : (item.changePercent ?? 0).getChangePercentDisplay(
                      excludeZero: true,
                    );

            final color = item.colorByPriceWealth;

            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AutoSizeText(
                      widget.entity.symbol,
                      textAlign: TextAlign.left,
                      maxLines: 1,
                      style: vpTextStyle.subtitle16?.copyWith(
                        color: vpColor.textPrimary,
                      ),
                    ),
                    Row(
                      children: [
                        buildDisplayModeView(item, DisplayMode.price),
                        Text(
                          ' ($percent)',
                          style: vpTextStyle.subtitle14?.copyWith(
                            color: item.isItemDefault ? themeData.black : color,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                kSpacingHeight8,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Gíá vốn',
                      style: vpTextStyle.body14?.copyWith(
                        color: vpColor.textTertiary,
                      ),
                    ),
                    Text(
                      widget.portfolio.costPriceCalculate
                          .toDouble()
                          .getPriceFormatted(
                            currency: '',
                            convertToThousand: true,
                          ),
                      style: vpTextStyle.subtitle14?.copyWith(
                        color: vpColor.textPrimary,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ],
                ),
                kSpacingHeight8,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Khối lượng',
                      style: vpTextStyle.body14?.copyWith(
                        color: vpColor.textTertiary,
                      ),
                    ),
                    Text(
                      widget.portfolio.totalCalculate.volumeString,
                      style: vpTextStyle.subtitle14?.copyWith(
                        color: vpColor.textPrimary,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ],
                ),
                kSpacingHeight8,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Lãi/ lỗ dự kiến',
                      style: vpTextStyle.body14?.copyWith(
                        color: vpColor.textTertiary,
                      ),
                    ),
                    _buildDisplayLossProfit(
                      item,
                      widget.portfolio,
                      DisplayMode.price,
                    ),
                  ],
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget buildDisplayModeView(StockDetailEntity item, DisplayMode displayMode) {
    return StreamBuilder<bool>(
      stream: _streamChangedProperty(displayMode),
      builder: (context, snapshot) {
        final priceChanged = snapshot.data ?? false;
        return Container(
          alignment: Alignment.center,
          color: priceChanged ? item.colorByPrice : ColorDefine.transparent,
          child: Text(
            displayValueByMode(displayMode, item),
            textAlign: TextAlign.right,
            style: vpTextStyle.subtitle14?.copyWith(
              color:
                  item.isItemDefault
                      ? vpColor.textPrimary
                      : priceChanged
                      ? ColorDefine.white
                      : item.colorByPriceWealth,
            ),
          ),
        );
      },
    );
  }

  Widget _buildDisplayLossProfit(
    StockDetailEntity item,
    Portfolio portfolio,
    DisplayMode displayMode,
  ) {
    return StreamBuilder<bool>(
      stream: _streamChangedProperty(displayMode),
      builder: (context, snapshot) {
        final priceChanged = snapshot.data ?? false;
        context.read<DetailAssetsCubit>().sinkDetailAssets.add(item);
        return Container(
          alignment: Alignment.centerRight,
          color:
              priceChanged
                  ? portfolio.colorProfitLoss(item.closePrice)
                  : ColorDefine.transparent,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                '${portfolio.currentValue(item.closePrice).volumeString} đ',
                textAlign: TextAlign.right,
                style: vpTextStyle.subtitle14?.copyWith(
                  color: portfolio.colorProfitLoss(
                    item.closePrice,
                    priceChanged: priceChanged,
                  ),
                ),
              ),
              kSpacingWidth4,
              AutoSizeText(
                '(${portfolio.percentProfitLoss(item.closePrice).toPercent(addPrefixCharacter: false)})',
                style: vpTextStyle.subtitle14?.copyWith(
                  color: portfolio.colorProfitLoss(
                    item.closePrice,
                    priceChanged: priceChanged,
                  ),
                ),
                minFontSize: 5,
                maxLines: 1,
              ),
            ],
          ),
        );
      },
    );
  }

  Stream<bool> _streamChangedProperty(DisplayMode mode) {
    switch (mode) {
      case DisplayMode.changePercent:
        return _bloc
            .stockChangedProperties[StockChangedProperties.changePercent]!
            .stream;
      case DisplayMode.volume:
        return _bloc
            .stockChangedProperties[StockChangedProperties.totalTrading]!
            .stream;
      default:
        return _bloc
            .stockChangedProperties[StockChangedProperties.lastPrice]!
            .stream;
    }
  }

  String displayValueByMode(DisplayMode mode, StockDetailEntity data) {
    if (data.isItemDefault) {
      return '-';
    }
    switch (mode) {
      case DisplayMode.changePercent:
        return data.changePercent!.getChangePercentDisplay(excludeZero: true);
      case DisplayMode.volume:
        return StockHelper.formatVol(data.totalVolume);
      default:
        return data.closePrice.getPriceFormatted(convertToThousand: true);
    }
  }
}
