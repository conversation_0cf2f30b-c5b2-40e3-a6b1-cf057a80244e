// Loai giao dich la noi bo hay chuyen ngoai
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/common/utils/stock_utils.dart';

enum MoneyTransferType { internal, outernal }

// Danh sach tai khoan thu huong
class MoneyAccountRecivedObj {
  String? benefitAccount; //ID tai khoan nhan
  String? benefitName;
  String? benefitLisenceCode;
  String? benefitBankName;
  String? benefitBankCity;
  String? benefitBankBranch;
  String? bankcode;
  int? bankid;
  int? branchid;
  String? transfertype;
  String? autoid;

  MoneyAccountRecivedObj({
    this.benefitAccount,
    this.benefitName,
    this.benefitLisenceCode,
    this.benefitBankName,
    this.benefitBankCity,
    this.benefitBankBranch,
    this.bankid,
    this.branchid,
    this.transfertype,
    this.autoid,
    this.bankcode,
  });

  MoneyAccountRecivedObj.fromJson(Map<String, dynamic> json) {
    benefitAccount = json['benefitAccount'];
    benefitName = json['benefitName'];
    benefitLisenceCode = json['benefitLisenceCode'];
    benefitBankName = json['benefitBankName'] ?? json['bankName'];
    benefitBankCity = json['benefitBankCity'];
    benefitBankBranch = json['benefitBankBranch'];
    bankcode = json['bankcode'] ?? json['bankCode'];
    bankid = json['bankid'];
    branchid = json['branchid'];
    transfertype = json['transfertype'] ?? json['transferType'];
    autoid = json['autoId'];
  }

  bool isInternal() {
    return transfertype == MoneyTransferType.internal.name;
  }

  bool isOuternal() {
    return transfertype == MoneyTransferType.outernal.name;
  }

  String getTitle(BuildContext context) {
    if (isInternal()) {
      final account = GetIt.instance<SubAccountCubit>()
          .subAccountsAllNoCommissionHaveDerivativeAccount
          .firstWhereOrNull((element) => element.id == benefitAccount);
      return StockUtils.getTitleSubAccount(context, account);
    } else {
      return '$bankcode - $benefitName';
    }
  }
}
