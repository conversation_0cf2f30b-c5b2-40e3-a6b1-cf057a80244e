import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';
import 'package:socket_io_client/socket_io_client.dart' as io_client;
import 'package:tuple/tuple.dart';
import 'package:uuid/uuid.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/category/fu_stock_info_item/app_connection_utils.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/category/fu_stock_info_item/module_config.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/category/fu_stock_info_item/stock_info_model.dart';
import 'package:vp_wealth/presentation/wealths/socket/model/derivative_top_price_from_socket_model.dart';
import 'package:vp_wealth/presentation/wealths/socket/model/investment_tool_oddlot_stock_data.dart';
import 'package:vp_wealth/presentation/wealths/socket/model/stock_impact_model.dart';
import 'package:vp_wealth/presentation/wealths/socket/socket_channel.dart';
import 'package:vp_wealth/presentation/wealths/socket/socket_data_center.dart';
import 'package:vp_wealth/presentation/wealths/socket/socket_subscriber.dart';

class SocketDerivativeConnect {
  static final SocketDerivativeConnect instance =
      SocketDerivativeConnect._internal();

  factory SocketDerivativeConnect() => instance;

  SocketDerivativeConnect._internal();

  io_client.Socket? _socket;

  bool _forceDisconnect = false;

  final _pendingSubscribe = <Tuple2<String, String>>[];

  /// item1: channel name
  /// item2: symbol
  final Map<Tuple2<String, String>, List<SocketObservationPair>>
  _stockSubscribedList = {};

  Stream<num?> closePriceStream(String symbol) {
    return addListener(symbol, ISocketChannel.stockInfo).streamData
        .where((e) => e is DerivativeStockInfoData && e.closePrice != null)
        .map((a) => (a as DerivativeStockInfoData).closePrice);
  }

  Stream<num?> changeValueStream(String symbol) {
    return addListener(symbol, ISocketChannel.stockInfo).streamData
        .where((e) => e is DerivativeStockInfoData && e.change != null)
        .map((a) => (a as DerivativeStockInfoData).change);
  }

  Stream<num?> changePercentStream(String symbol) {
    return addListener(symbol, ISocketChannel.stockInfo).streamData
        .where((e) => e is DerivativeStockInfoData)
        .map((event) => (event as DerivativeStockInfoData).changePercent);
  }

  Stream<num?> totalVolumeStream(String symbol) {
    return addListener(symbol, ISocketChannel.stockInfo).streamData
        .where((e) => e is DerivativeStockInfoData && e.totalTrading != null)
        .map((a) => (a as DerivativeStockInfoData).totalTrading);
  }

  void connect() {
    _forceDisconnect = false;

    if (_socket != null && _socket!.connected) return;

    _initSocket();
    _connect();
  }

  void disconnect() {
    _forceDisconnect = true;

    if (_socket != null) {
      _socket?.dispose();
      _socket = null;
    }
  }

  /// only call this method when logout
  /// remove all listener and disconnect
  void destroy() {
    _stockSubscribedList.clear();
    _pendingSubscribe.clear();

    disconnect();
  }

  void _initSocket() {
    final url = DerivativeModuleConfig().socketUrl;

    _socket = io_client.io(
      url,
      io_client.OptionBuilder()
          .enableForceNew()
          .enableForceNewConnection()
          .enableAutoConnect()
          .enableReconnection()
          .setTransports(['websocket', 'polling'])
          .setPath('/div-stream')
          .build(),
    );
  }

  void _connect() {
    _socket?.on('connect', (_) {
      dlog('❤️❤️❤️❤️❤️ SOCKET IV CONNECTED');

      _resubscribeSocket();
      _subscribePendingChannel();
    });

    _socket?.onReconnect((data) {
      dlog('❤️❤️❤️❤️❤️ SOCKET IV RECONNECT');
      _resubscribeSocket();
      _subscribePendingChannel();
    });

    _socket?.on('connect_timeout', (_) {
      dlog('❤️❤️❤️❤️❤️ SOCKET IV CONNECT TIMEOUT');

      reconnect();
    });

    _socket?.on('error', (_) {
      dlog('❤️❤️❤️❤️❤️ SOCKET IV ERROR');
    });

    _socket?.on('reconnecting', (_) {
      dlog('❤️❤️❤️❤️❤️ SOCKET IV RECONNECTING');
    });

    _socket?.on('disconnect', (_) {
      dlog('⚠️⚠️⚠️⚠️⚠️ SOCKET IV DISCONNECT');

      reconnect();
    });

    _socket?.on('reconnect_failed', (_) {
      dlog('⚠️⚠️⚠️⚠️⚠️ SOCKET IV RECONNECT FAILED');
    });

    _socket?.on('reconnect_error', (_) {
      dlog('⚠️⚠️⚠️⚠️⚠️ SOCKET IV RECONNECT ERROR');
    });

    _socket?.on('reconnect_attempt', (_) {
      dlog('✨✨✨✨✨️ SOCKET IV RECONNECT ATTEMPT');
    });

    _socket?.on('message', _transformStockData);
  }

  void reconnect() async {
    if (_forceDisconnect) return;

    if (_socket != null) disconnect();

    final hasInternet = await DerivativeConnectionUtils().checkConnection(
      delay: 2,
    );

    if (hasInternet) connect();
  }

  void _subscribePendingChannel() {
    try {
      if (_pendingSubscribe.isNullOrEmpty) return;

      for (final channel in _pendingSubscribe) {
        _subscribeStock(channel: channel.item1, symbol: channel.item2);
      }

      _pendingSubscribe.clear();
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  SocketSubscriber addListener(String symbol, ISocketChannel channel) {
    final channelName = channel.nameChannel;

    final String uniqueId = const Uuid().v1();

    final pair = SocketObservationPair(
      uniqueId: uniqueId,
      dataSource: PublishSubject(),
      onDispose: () => _removeListener(symbol, channelName, uniqueId),
    );

    final observers = _stockSubscribedList[Tuple2(channelName, symbol)];

    if (observers.isNullOrEmpty) {
      _stockSubscribedList[Tuple2(channelName, symbol)] = [pair];
      _subscribeStock(channel: channelName, symbol: symbol);
    } else {
      _stockSubscribedList[Tuple2(channelName, symbol)]!.add(pair);
    }

    return pair.subscriber;
  }

  String get fu30VNCode => 'FUVN30';

  SocketSubscriber addListenerFU30VN(ISocketChannel channel) {
    // Stopwatch stopwatch = Stopwatch()..start();
    final channelName = channel.nameChannel;

    final String uniqueId = const Uuid().v1();

    final pair = SocketObservationPair(
      uniqueId: uniqueId,
      dataSource: PublishSubject(),
      onDispose: () => _removeListener(fu30VNCode, channelName, uniqueId),
    );

    final observers = _stockSubscribedList[Tuple2(channelName, fu30VNCode)];

    if (observers.isNullOrEmpty) {
      _stockSubscribedList[Tuple2(channelName, fu30VNCode)] = [pair];
      _subscribeStock(channel: channelName, symbol: fu30VNCode);
    } else {
      _stockSubscribedList[Tuple2(channelName, fu30VNCode)]!.add(pair);
    }
    // LoggerUtils.debug('addListenerFU30VN() executed in ${stopwatch.elapsed}');
    return pair.subscriber;
  }

  void _removeListener(String symbol, String channel, String uniqueId) {
    final observers = _stockSubscribedList[Tuple2(channel, symbol)];

    if (!observers.isNullOrEmpty) {
      observers!.removeWhere((e) => e.uniqueId == uniqueId);

      if (observers.isNullOrEmpty) {
        _stockSubscribedList.remove(Tuple2(channel, symbol));
        _unsubscribeStock(symbol: symbol, channel: channel);
      }
    }
  }

  void _unsubscribeStock({required String symbol, required String channel}) {
    if (_socket != null && _socket!.connected) {
      _socket?.emitWithAck('unsubscribe', {
        'marketId': symbol,
        'channel': channel,
      }, ack: (data) {});
    }
  }

  void _resubscribeSocket() {
    if (_stockSubscribedList.isEmpty) return;

    /// item1: channel
    /// item2: symbol
    for (final channel in _stockSubscribedList.keys) {
      _subscribeStock(
        channel: channel.item1,
        symbol: channel.item2,
        addPendingWhenNoConnect: false,
      );
    }
  }

  void _subscribeStock({
    required String symbol,
    required String channel,
    bool addPendingWhenNoConnect = true,
  }) {
    try {
      final tuple = Tuple2(channel, symbol);

      if (_socket != null && _socket!.connected) {
        _socket?.emitWithAck('join', {
          'marketId': symbol,
          'channel': channel,
        }, ack: (data) {});
      } else if (addPendingWhenNoConnect &&
          !_pendingSubscribe.contains(tuple)) {
        _pendingSubscribe.add(tuple);
      }
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  void _transformStockData(dynamic data) {
    try {
      // LoggerUtils.debug('Data Socket: $data');
      if (data == null || data is! Map<String, dynamic>) return;

      final channelValue = data['e'];

      final symbol = data['sb'] ?? data['mc'] ?? data['s'];

      Tuple2<String, String>? tuple;

      dynamic iSocketStockData;
      try {
        if (channelValue == ISocketChannel.marketData.nameSocket) {
          tuple = Tuple2(ISocketChannel.marketData.nameChannel, symbol);
          iSocketStockData = IMarketData.fromJson(data);
        } else if (channelValue == ISocketChannel.marketInfo.nameSocket) {
          // LoggerUtils.info("Market_Info: $data");
          tuple = Tuple2(ISocketChannel.marketInfo.nameChannel, symbol);
          iSocketStockData = IMarketInfoData.fromJson(data);
        } else if (channelValue == ISocketChannel.marketStockInfo.nameSocket ||
            channelValue == ISocketChannel.stockInfo.nameSocket) {
          // tuple =
          //     Tuple2(ISocketChannel.marketStockInfo.nameChannel, fu30VNCode);
          // // LoggerUtils.info("Market_stock_info: $data");
          // iSocketStockData = IStockInfoData.fromJson(data);
          //Because Type of market_stockinfo and stockInfo is same name;
          _handlePushMarketStockInfoNotify(tuple, data);
          _handlePushStockInfoNotify(tuple, data, symbol);
        } else if (channelValue == ISocketChannel.stockInfo.nameSocket) {
          tuple = Tuple2(ISocketChannel.stockInfo.nameChannel, symbol);
          iSocketStockData = DerivativeStockInfoData.fromJson(data);
          // LoggerUtils.info("Channel From Socket: $channelValue");
        } else if (channelValue == ISocketChannel.topNPrice.nameSocket) {
          tuple = Tuple2(ISocketChannel.topNPrice.nameChannel, symbol);
          iSocketStockData = DerivativeTopNPriceFromSocketModel.fromJson(data);
        } else if (channelValue == ISocketChannel.marketEvent.nameSocket) {
          tuple = Tuple2(ISocketChannel.marketEvent.nameChannel, symbol);
          // iSocketStockData = IMarketInfoData.fromJson(data);
        } else if (channelValue == ISocketChannel.marketVolume.nameSocket) {
          tuple = Tuple2(ISocketChannel.marketVolume.nameChannel, symbol);
          // iSocketStockData = IMarketInfoData.fromJson(data);
        }
        if (channelValue == ISocketChannel.stockImpact.nameSocket) {
          // LoggerUtils.info("stock_impact: $data");
          tuple = Tuple2(ISocketChannel.stockImpact.nameChannel, symbol);
          iSocketStockData = IStockImpact.fromJson(data);
        }
      } catch (e, s) {}
      final observers = _stockSubscribedList[tuple];

      if (observers.isNullOrEmpty || iSocketStockData == null) return;

      for (final observer in observers!) {
        observer.notifier.observer.add(iSocketStockData);
      }
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  void _handlePushMarketStockInfoNotify(
    Tuple2<String, String>? tuple,
    dynamic data,
  ) {
    tuple = Tuple2(ISocketChannel.marketStockInfo.nameChannel, fu30VNCode);
    // LoggerUtils.info("Market_stock_info: $data");
    final iSocketStockData = DerivativeStockInfoData.fromJson(data);
    _handlePushNotify(tuple, iSocketStockData);
  }

  void _handlePushStockInfoNotify(
    Tuple2<String, String>? tuple,
    dynamic data,
    String symbol,
  ) {
    tuple = Tuple2(ISocketChannel.stockInfo.nameChannel, symbol);
    final iSocketStockData = DerivativeStockInfoData.fromJson(data);
    _handlePushNotify(tuple, iSocketStockData);
  }

  void _handlePushNotify(
    Tuple2<String, String>? tuple,
    dynamic iSocketStockData,
  ) {
    final observers = _stockSubscribedList[tuple];

    if (observers.isNullOrEmpty || iSocketStockData == null) return;

    for (final observer in observers!) {
      observer.notifier.observer.add(iSocketStockData);
    }
  }
}
