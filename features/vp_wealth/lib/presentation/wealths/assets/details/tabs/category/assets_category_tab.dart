import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/model/enum/order_action.dart';
import 'package:vp_stock_common/model/place_order_args.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/model/assets/item_assets_model.dart';
import 'package:vp_wealth/data/model/stock_detail_entity.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/data/utils/padding_extends.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/place_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/place_order_bloc_map_import.dart';
import 'package:vp_wealth/presentation/place_order/presentation/router/place_order_arguments.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/cubit/detail_assets_cubit.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/category/stock_header_sort/stock_info_item/stock_info_item.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/category/stock_header_sort/stock_info_item/stock_list_cache_cubit.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/category/widget/stock_detail_bottomsheet.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/bottom_sheet_detail_category_chart.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/chart_invesment_category.dart';
import 'package:vp_wealth/router/wealth_router.dart';

class AssetsCategoryTab extends StatefulWidget {
  final ItemAssetsModel model;

  const AssetsCategoryTab({Key? key, required this.model}) : super(key: key);

  @override
  State<AssetsCategoryTab> createState() => _AssetsCategoryTabState();
}

class _AssetsCategoryTabState extends State<AssetsCategoryTab> {
  late DetailAssetsCubit _cubit;

  ItemAssetsModel get _model => widget.model;

  @override
  void initState() {
    _cubit = context.read<DetailAssetsCubit>();
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _onInitData();
    });
  }

  Future<void> _onInitData() async {
    _cubit.getStockBySymbols(_model);
  }

  @override
  void dispose() {
    _cubit.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => _cubit),
        BlocProvider<StockListCacheCubit>(create: (_) => StockListCacheCubit()),
      ],
      child: VPScaffold(
        backgroundColor: vpColor.backgroundElevationMinus1,
        body: SafeArea(
          child: PullToRefreshView(
            onRefresh: () => _onInitData(),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  kSpacingHeight12,
                  _totalNetAssets,
                  kSpacingHeight8,
                  if (_model.chartData.isNotEmpty &&
                      _model.totalMarketValue != 0)
                    _assetsChart,
                  kSpacingHeight4,
                  _listAssetsHeld,
                ],
              ),
            ),
          ),
        ),
        bottomNavigationBar: _orderInvestment,
      ),
    );
  }

  Widget get _totalNetAssets {
    return BlocBuilder<DetailAssetsCubit, DetailAssetsState>(
      bloc: _cubit,
      buildWhen:
          (previous, current) => previous.marketValue != current.marketValue,
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: SizeUtils.kSize16),
          child: Container(
            padding: const EdgeInsets.all(SizeUtils.kSize16),
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(8)),
              color: vpColor.backgroundElevation0,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Giá trị thị trường',
                  style: vpTextStyle.captionMedium?.copyWith(
                    color: vpColor.textSecondary,
                  ),
                ),
                Text(
                  state.marketValue.valueText,
                  style: vpTextStyle.headineBold6?.copyWith(
                    color: vpColor.textPrimary,
                  ),
                ),
                kSpacingHeight4,
                _infoBasicAssets,
              ],
            ),
          ),
        );
      },
    );
  }

  Widget get _infoBasicAssets {
    return BlocBuilder<DetailAssetsCubit, DetailAssetsState>(
      bloc: _cubit,
      buildWhen:
          (previous, current) =>
              previous.marketValue != current.marketValue ||
              previous.data != current.data,
      builder: (context, state) {
        return Column(
          children: [
            _itemColumnInfoAssets(
              title: 'Giá trị vốn',
              capitalValue: (state.data?.totalCapitalValue ?? 0).valueText,
            ),
            _itemColumnProfitLoss(
              title: 'Lãi/lỗ dự kiến',
              valueProfitLoss:
                  '${state.data?.totalCurrentValue.volumeString} đ',
              percentProfitLoss: (state.data?.totalPercentProfitLoss ?? 0)
                  .toPercent(addPrefixCharacter: false, truncateZero: true),
            ),
          ],
        );
      },
    );
  }

  Widget get _assetsChart {
    return BlocBuilder<DetailAssetsCubit, DetailAssetsState>(
      bloc: _cubit,
      buildWhen:
          (previous, current) =>
              previous.marketValue != current.marketValue ||
              previous.data != current.data,
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: SizeUtils.kSize16),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(8)),
              color: vpColor.backgroundElevation0,
            ),
            child:
                ChartInvestmentCategory(
                  chartData: _model.chartData,
                ).paddingLeft16(),
          ),
        );
      },
    );
  }

  Widget get _listAssetsHeld {
    return BlocBuilder<DetailAssetsCubit, DetailAssetsState>(
      bloc: _cubit,
      buildWhen:
          (previous, current) =>
              previous.listData != current.listData ||
              previous.sortType != current.sortType ||
              previous.data != current.data ||
              previous.sortStatus != current.sortStatus,
      builder: (context, state) {
        final sortedList = List.of(state.sortedList);
        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.only(bottom: SizeUtils.kSize24),
          itemBuilder: (cxt, index) {
            final item = sortedList[index];
            final portfolio = (state.data?.portfolio ?? []).firstWhere(
              (element) => element.symbol == item.symbol,
            );
            return Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: SizeUtils.kSize16,
              ),
              child: Container(
                margin: const EdgeInsets.symmetric(vertical: SizeUtils.kSize4),
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(Radius.circular(8)),
                  color: vpColor.backgroundElevation0,
                ),
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: SizeUtils.kSize16,
                        vertical: SizeUtils.kSize8,
                      ),
                      child: StockInfoItem(
                        key: ValueKey(item.symbol),
                        entity: item,
                        portfolio: portfolio,
                        displayModeMixin: _cubit,
                        toastPadding: const EdgeInsets.only(
                          left: SizeUtils.kSize32,
                          right: SizeUtils.kSize32,
                        ),
                        onTap: (item) {
                          openDetailBottomSheet(context, item, portfolio);
                        },
                      ),
                    ),
                    kSpacingHeight8,
                  ],
                ),
              ),
            );
          },
          itemCount: sortedList.length,
        );
      },
    );
  }

  Widget _itemColumnInfoAssets({
    required String title,
    required String capitalValue,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: vpTextStyle.body14?.copyWith(color: vpColor.textTertiary),
        ),
        Text(
          capitalValue,
          style: vpTextStyle.subtitle14?.copyWith(color: vpColor.textPrimary),
        ),
      ],
    );
  }

  Widget _itemColumnProfitLoss({
    required String title,
    required String valueProfitLoss,
    required String percentProfitLoss,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: vpTextStyle.body14?.copyWith(color: vpColor.textTertiary),
        ),
        kSpacingHeight4,
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: valueProfitLoss,
                style: vpTextStyle.subtitle14?.copyWith(
                  color: _model.colorProfitLoss,
                ),
              ),
              TextSpan(
                text: ' ($percentProfitLoss)',
                style: vpTextStyle.subtitle14?.copyWith(
                  color: _model.colorProfitLoss,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void openChartDetailBottomSheet(BuildContext context) {
    showModalBottomSheet(
      barrierColor: themeData.overlayBottomSheet,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return BottomSheetCategoryDetailChart(chartData: _model.chartData);
      },
    );
    // showModalBottomSheet(
    //   context: context,
    //   isScrollControlled: true,
    //   builder: (_) {
    //     return ChartDetailBottomSheet(
    //       title: getWealthLang(WealthKeyLang.investmentCategory),
    //       chartData: _model.chartData,
    //       numberColumn: 3,
    //     );
    //   },
    // );
  }

  void openDetailBottomSheet(
    BuildContext context,
    StockDetailEntity entity,
    Portfolio portfolio,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (_) {
        return StockDetailBottomSheet(
          entity: entity,
          portfolio: portfolio,
          totalMarketValue: _cubit.state.marketValue,
          onBuy: () => _navigatePlaceOrder(Order.buy, entity.symbol),
          onSell: () => _navigatePlaceOrder(Order.sell, entity.symbol),
        );
      },
    );
  }

  void _navigatePlaceOrder(Order order, String symbol) async {
    var result = await context.push(
      WealthRouter.placeOrder,
      extra: PlaceOrderArguments(
        symbol: symbol,
        order: order,
        wealthModel: _model,
      ),
    );
    if (result == null) return;
    _cubit.getStockBySymbols(_model);
  }

  Widget get _orderInvestment {
    return ColoredBox(
      color: vpColor.backgroundElevation1,
      child: Padding(
        padding: EdgeInsets.only(
          left: SizeUtils.kSize16,
          right: SizeUtils.kSize16,
          top: SizeUtils.kSize16,
          bottom:
              MediaQuery.of(context).padding.bottom > 0
                  ? MediaQuery.of(context).padding.bottom
                  : SizeUtils.kSize16,
        ),
        child: VpsButton.primarySmall(
          width: double.infinity,
          dismissKeyboardWhenTap: true,
          textAlign: TextAlign.center,
          title: 'Đặt lệnh đầu tư',
          onPressed:
              () => context.push(
                WealthRouter.orderBuyInvestmentPage,
                extra: _model,
              ),
        ),
      ),
    );
  }
}
