import 'package:bloc/bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/components/dialog_error.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/money_domain/money_repository.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/status_error.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_common/lang/money_key_lang.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_common/lang/money_localized_values.dart';
import 'package:vp_wealth/router/wealth_router.dart';

part 'link_mbbank_state.dart';

enum LinkTypeEnum { ACCOUNT, CARD }

class LinkMbBankCubit extends Cubit<LinkMbBankState> {
  LinkMbBankCubit() : super(const LinkMbBankState());
  final moneyRepository = GetIt.instance.get<MoneyRepository>();
  TextEditingController textEditingController = TextEditingController();

  void init() {
    getDataPersonal();
  }

  Future<void> requestLinkBank({String? path}) async {
    final context =
        GetIt.instance<NavigationService>().navigatorKey.currentContext;
    try {
      showDialogLoading(onWillPop: false);
      final res = await GetIt.instance.get<MoneyRepository>().requestLinkMB(
        linkType: LinkTypeEnum.ACCOUNT.name,
        sourceNumber: textEditingController.text.toString(),
        pathName: path,
      );
      hideDialogLoading();
      if (!res.isSuccess() && !StatusErrorCashIn().isCheckStatus(res.code)) {
        showDialogMessage(
          context!,
          getMoneyLang(MoneyKeyLang.titleErrorWebView),
          res.message.toString(),
        );
        return;
      }
      if (res.isSuccess() && StatusErrorCashIn().isCheckStatus(res.code)) {
        context!.push(WealthRouter.mbOtpPage);
      }
    } catch (e) {
      hideDialogLoading();
      showError(e);
    }
  }

  void updateAccountMb() {
    emit(
      state.copyWith(
        accountMb: textEditingController.text.trim(),
        enableButton: textEditingController.text.trim().isNotEmpty,
      ),
    );
  }

  void getDataPersonal() {
    var fullName = 'Session().iamUserInfo?.fullName';
    var phone = 'Session().iamUserInfo?.mobile';
    var idNumber = 'Session().iamUserInfo?.idNumber';
    emit(
      state.copyWith(
        fullName: fullName,
        personalPapers: idNumber,
        phone: phone,
      ),
    );
  }
}
