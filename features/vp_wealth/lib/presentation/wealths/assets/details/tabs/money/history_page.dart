import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sticky_headers/sticky_headers.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/custom_widget/listview_helper.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_design_system/widget/appbar/appbar_view.dart';
import 'package:vp_design_system/widget/scaffold/scaffold_view.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/model/assets/item_assets_model.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/stock_right/widget/error_widget.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/history/money_history_obj.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/widget/asset_history_filter.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/widget/transaction_widget.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/widget/command_history_loading_widget.dart';

import 'cubit/history_cubit.dart';

class WealthHistoryPage extends StatefulWidget {
  const WealthHistoryPage({super.key, required this.model});

  final ItemAssetsModel model;

  @override
  State<WealthHistoryPage> createState() => _WealthHistoryPageState();
}

class _WealthHistoryPageState extends State<WealthHistoryPage> {
  late HistoriesCubit _bloc;

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _bloc,
      child: VPScaffold(
        appBar: VPAppBar.layer(
          title: 'Lịch sử chuyển tiền',
          actions: [
            BlocBuilder<HistoriesCubit, HistoryState>(
              builder: (context, state) {
                return Padding(
                  padding: const EdgeInsets.only(right: SizeUtils.kSize12),
                  child: GestureDetector(
                    onTap: () => showModalFilter(),
                    child: Assets.icons.icFilter.svg(
                      colorFilter: ColorFilter.mode(
                        vpColor.iconPrimary,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
        body: SafeArea(
          child: ColoredBox(
            color: vpColor.backgroundElevationMinus1,
            child: Column(
              children: [
                kSpacingHeight12,
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: SizeUtils.kSize16,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Tiểu khoản',
                        style: vpTextStyle.body14?.copyWith(
                          color: vpColor.textPrimary,
                        ),
                      ),
                      Text(
                        'Tích sản',
                        style: vpTextStyle.body14?.copyWith(
                          color: vpColor.textPrimary,
                        ),
                      ),
                    ],
                  ),
                ),
                kSpacingHeight12,
                Expanded(
                  child: BlocBuilder<HistoriesCubit, HistoryState>(
                    builder: (context, state) {
                      if (state.loading) {
                        return const CommandHistoryLoadingWidget();
                      } else if (state.error && state.transactions.isEmpty) {
                        return ErrorNetworkWidget(
                          tryAgain: true,
                          onPressed: () => _bloc.onRefresh(),
                        );
                      } else {
                        if (state.transactions.isEmpty) {
                          return _transactionsEmpty;
                        }

                        return ListViewHelper(
                          noDataView: () => _transactionsEmpty,
                          physics: const AlwaysScrollableScrollPhysics(),
                          loadMore: () => _bloc.onLoadMore(),
                          hasMore: () => _bloc.hasLoadMore(),
                          refresh: () => _bloc.onRefresh(),
                          itemCount: () => state.transactions.length,
                          itemBuilder: (context, index) {
                            final group = state.transactions[index];
                            return StickyHeader(
                              header: Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: SizeUtils.kSize16,
                                  vertical: 8,
                                ),
                                child: ColoredBox(
                                  color: vpColor.backgroundElevationMinus1,
                                  child: Text(
                                    group.date,
                                    style: vpTextStyle.body14?.copyWith(
                                      color: vpColor.textPrimary,
                                    ),
                                  ),
                                ),
                              ),
                              content: Column(
                                children:
                                    group.transactions.map((transaction) {
                                      return TransactionCardWidget(
                                        transaction: transaction,
                                      );
                                    }).toList(),
                              ),
                            );
                          },
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /* -------- Show modal filter --------*/
  Future<void> showModalFilter() async {
    MoneyHistoryModalFilterObj? objFilter =
        await showModalBottomSheet<MoneyHistoryModalFilterObj>(
          barrierColor: themeData.overlayBottomSheet,
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (BuildContext context) {
            return Padding(
              padding: MediaQuery.of(context).viewInsets,
              child: AssetHistoryFilterWidget(
                objModalFilterSaved: _bloc.objFilterSaved,
              ),
            );
          },
        );

    if (objFilter != null) {
      _bloc.objFilterSaved = objFilter;
      // bloc.checkIconFilter();
      // String startDate = AppTimeUtils.formatTime(
      //     objFilter.getStartDate().toString(),
      //     format: AppTimeUtilsFormat.dateNormal);
      // String endDate = AppTimeUtils.formatTime(
      //     objFilter.getEndDate().toString(),
      //     format: AppTimeUtilsFormat.dateNormal);
      _bloc.onLoadData(isFilter: true);
    }
  }

  @override
  void initState() {
    super.initState();
    _bloc = HistoriesCubit()..init(widget.model);
  }

  String getStatus(String status) {
    if (status == '1') {
      return 'Thành công';
    } else if (status == '2') {
      return 'Đang xử lý';
    } else {
      return 'Thất bại';
    }
  }

  Color getColor(String status) {
    bool isDark = context.read<ThemeCubit>().currentTheme == AppTheme.dark;
    return (status == '1')
        ? themeData.primary.withOpacity(isDark ? 0.32 : 0.16)
        : themeData.red16.withOpacity(isDark ? 0.32 : 0.16);
  }

  Widget get _transactionsEmpty {
    return Padding(
      padding: const EdgeInsets.only(top: SizeUtils.kSize56),
      child: SizedBox(
        width: double.infinity,
        child: Column(
          children: [
            Assets.icons.emptyStock.svg(),
            kSpacingHeight8,
            Text(
              'Không có lịch sử chuyển tiền',
              style: vpTextStyle.body14?.copyWith(color: vpColor.textPrimary),
            ),
          ],
        ),
      ),
    );
  }
}
