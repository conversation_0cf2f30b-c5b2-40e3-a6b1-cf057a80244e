import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/bottom_sheet_item_widget.dart';
import 'package:vp_design_system/custom_widget/button_bottom_sheet.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_wealth/common/utils/stock_utils.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/widget/container_hepler.dart';

Future<SubAccountModel?> showSelectSubAccountBottomSheet(
  BuildContext context, {
  String? title,
  bool? selectAll,
  bool isAll = false,
  bool isDismissible = true,
  bool isHaveDerivative = false,
}) async {
  SubAccountModel? subAccount;

  List<SubAccountModel> _getSubAccounts() {
    if (isAll) {
      if (isHaveDerivative) {
        return GetIt.instance
            .get<SubAccountCubit>()
            .subAccountsAllNoCommissionHaveDerivativeAccount;
      } else {
        return GetIt.instance.get<SubAccountCubit>().subAccountsAllNoCommission;
      }
    } else {
      return GetIt.instance.get<SubAccountCubit>().subAccountsStock;
    }
  }

  final subAccounts = _getSubAccounts();
  await showModalBottomSheet(
    barrierColor: themeData.overlayBottomSheet,
    isDismissible: isDismissible,
    context: context,
    backgroundColor: Colors.transparent,
    builder: (context) {
      return StatefulBuilder(
        builder:
            (context, setState) => SafeArea(
              child: Container(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height / 2,
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: SizeUtils.kSize8,
                    vertical: SizeUtils.kSize8,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        decoration: ContainerHelper.decorationBottomAll(),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Visibility(
                              visible: selectAll ?? true,
                              child: BottomSheetItemWidget(
                                text: 'Tất cả tiểu khoản',
                                border: false,
                                onTap: () {
                                  setState(() {
                                    subAccount = SubAccountModel();
                                  });
                                  Navigator.pop(context, SubAccountModel());
                                },
                              ),
                            ),
                            ListView.builder(
                              itemCount: subAccounts.length,
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemBuilder: (context, index) {
                                return BottomSheetItemWidget(
                                  text: StockUtils.getTitleSubAccount(
                                    context,
                                    subAccounts[index],
                                  ),
                                  onTap: () {
                                    setState(() {
                                      subAccount = subAccounts[index];
                                    });
                                    Navigator.pop(context);
                                  },
                                  border:
                                      (index == 0) ? (selectAll ?? true) : true,
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: SizeUtils.kSize8),
                      ButtonBottomSheet(
                        color: themeData.red,
                        text: 'Huỷ',
                        onTap: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                ),
              ),
            ),
      );
    },
  );
  return subAccount;
}
