import 'dart:async';
import 'package:rxdart/rxdart.dart';
import 'package:vp_wealth/data/model/chart_price_entity.dart';
import 'package:vp_wealth/data/model/stock_detail_entity.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/cubit/detail_assets_cubit.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/category/fu_stock_info_item/derivative_connect_stock_info_socket_mixin.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/category/fu_stock_info_item/derivative_socket_connect.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/category/fu_stock_info_item/stock_info_model.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/edit_plan/stock_changed_properties.dart';
import 'package:vp_wealth/presentation/wealths/socket/mixin/iv_market_info_mixin.dart';
import 'package:vp_wealth/presentation/wealths/socket/model/investment_tool_oddlot_stock_data.dart';
import 'package:vp_wealth/presentation/wealths/socket/socket_subscriber.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/presentation/bloc/mixin/stream_check_mixin.dart';

class FuStockTabMarketItemBloc
    with
        StreamCheckMixin,
        DerivativeConnectStockInfoSocketMixin,
        IVMarketInfoMixin
    implements IDerivativeConnectStockInfoSocket, IVMarketInfoListener {
  late final StockDisplayModeMixin displayModeMixin;

  final BehaviorSubject<StockDetailEntity> _stockItemController =
      BehaviorSubject();

  Stream<StockDetailEntity> get streamStockItem => _stockItemController.stream;

  String get symbol => _stockItemController.valueOrNull?.symbol ?? '';

  SocketSubscriber? _priceSocketSubscriber;

  StreamSubscription? _priceSocketSubscription;

  num? marketIndex;

  //create timer to handle socket data changed
  final Map<StockChangedProperties, Timer> _timers = {};
  final Map<StockChangedProperties, BehaviorSubject<bool>>
      stockChangedProperties = {
    StockChangedProperties.lastPrice: BehaviorSubject<bool>.seeded(false),
    StockChangedProperties.totalTrading: BehaviorSubject<bool>.seeded(false),
  };

  void init({
    required StockDetailEntity item,
    required StockDisplayModeMixin mixin,
    List<ChartPriceEntity>? chartData,
    num? marketIndex,
  }) {
    SocketDerivativeConnect.instance.connect();

    this.marketIndex = marketIndex;

    displayModeMixin = mixin;

    emitStreamData<StockDetailEntity>(_stockItemController, item);

    _listenDataChanged();
  }

  void _listenDataChanged() {
    stockInfoSocketConnect(this, symbol);

    marketInfoSocketConnect(this, '30');
  }

  void _updateDiffChangeFromSocket(num? marketIndex) {
    final currentData = _stockItemController.valueOrNull;

    final closePrice = currentData?.closePrice;

    if (closePrice != null && marketIndex != null) {
      currentData?.diff = closePrice - marketIndex;
      emitStreamData<StockDetailEntity>(_stockItemController, currentData!);
    }
  }

  void _updateChangePercentFromSocket(DerivativeStockInfoData data) {
    final currentData = _stockItemController.valueOrNull;

    if (data.changePercent == null || currentData == null) return;

    if (currentData.changePercent != data.changePercent) {
      //update stock data
      currentData.changePercent = data.changePercent!.toDouble();

      emitStreamData<StockDetailEntity>(_stockItemController, currentData);

      //show flash background
      if (displayModeMixin.displayMode == DisplayMode.changePercent) {
        _updateChangedProperty(StockChangedProperties.changePercent);
      }
    }
  }

  void _updateChangeValueFromSocket(DerivativeStockInfoData data) {
    final currentData = _stockItemController.valueOrNull;

    if (data.change == null || currentData == null) return;

    currentData.changeValue = data.change!.toDouble();

    emitStreamData<StockDetailEntity>(_stockItemController, currentData);
  }

  void _updateChangePriceFromSocket(DerivativeStockInfoData data) {
    if (data.closePrice == null) return;

    final currentData = _stockItemController.valueOrNull;

    final closePrice = data.closePrice!.toDouble();

    if (currentData == null || currentData.price == closePrice) return;

    //update stock data
    currentData.price = closePrice;

    currentData.closePrice = closePrice;

    emitStreamData<StockDetailEntity>(_stockItemController, currentData);

    //show flash background
    if (displayModeMixin.displayMode == DisplayMode.price) {
      _updateChangedProperty(StockChangedProperties.lastPrice);
    }
  }

  void _updateVolumeChangeFromSocket(DerivativeStockInfoData data) {
    final currentData = _stockItemController.valueOrNull;

    if (data.totalTrading == null || currentData == null) return;

    if (currentData.totalVolume != data.totalTrading) {
      //update stock data
      currentData.totalVolume = data.totalTrading!.toDouble();

      emitStreamData<StockDetailEntity>(_stockItemController, currentData);

      _updateChangedProperty(StockChangedProperties.totalTrading);
    }
  }

  void _updateChangedProperty(StockChangedProperties property) {
    emitStreamData<bool>(stockChangedProperties[property]!, true);
    _invalidateAndRestartTimer(property);
  }

  void _invalidateAndRestartTimer(StockChangedProperties property) {
    const Duration duration = Duration(seconds: 1);
    Timer? timer = _timers[property];
    timer?.cancel();
    timer = Timer(duration, () {
      emitStreamData<bool>(stockChangedProperties[property]!, false);
    });
    _timers[property] = timer;
  }

  void dispose() {
    stockInfoSocketClose();
    closeMarketInfoSocket();
    _stockItemController.close();
    _priceSocketSubscriber?.dispose();
    _priceSocketSubscription?.cancel();

    for (var e in _timers.values) {
      e.cancel();
    }

    for (var element in stockChangedProperties.values) {
      element.close();
    }
  }

  @override
  void onChangeStockInfoFromSocket(DerivativeStockInfoData data) {
    _updateChangePriceFromSocket(data);
    _updateVolumeChangeFromSocket(data);
    _updateChangePercentFromSocket(data);
    _updateChangeValueFromSocket(data);
    _updateDiffChangeFromSocket(marketIndex);
  }

  @override
  void onMarketInfoDataChanged(IMarketInfoData data) {
    _updateDiffChangeFromSocket(marketIndex = data.marketIndex);
  }
}
