import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_input/bloc/money_cash_in_input_bloc.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_common/lang/money_key_lang.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_common/lang/money_localized_values.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/money_cash_in/money_cash_in_input_param.dart';

import 'bank_item.dart';

class SelectTransferAccount extends StatelessWidget {
  const SelectTransferAccount({Key? key, required this.params})
    : super(key: key);

  final MoneyCashInInputParam params;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MoneyCashInInputBloc, MoneyCashInInputState>(
      builder: (contextBloc, state) {
        return Padding(
          padding: kPadding[4]!,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                getMoneyLang(MoneyKeyLang.transferAccount),
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              kSpacingWidth20,
              Expanded(
                child: InkWell(
                  onTap:
                      () => showModalBottomSheet(
                        barrierColor: themeData.overlayBottomSheet,
                        context: context,
                        isScrollControlled: true,
                        builder: (context) {
                          return SizedBox(
                            height: MediaQuery.of(context).size.height * 0.5,
                            child: Column(
                              children: [
                                Container(
                                  margin: const EdgeInsets.only(top: 16),
                                  width: 56,
                                  height: 8,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    color: themeData.gray300,
                                  ),
                                ),
                                Container(
                                  padding: kPadding[4]!,
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    getMoneyLang(MoneyKeyLang.transferAccount),
                                    style: vpTextStyle.body16?.copyWith(
                                      color: Theme.of(context).focusColor,
                                    ),
                                  ),
                                ),
                                Expanded(
                                  child: ListView.builder(
                                    shrinkWrap: true,
                                    itemBuilder: (context, index) {
                                      return GestureDetector(
                                        onTap:
                                            () => contextBloc
                                                .read<MoneyCashInInputBloc>()
                                                .add(
                                                  MoneyCashInInputGetAccountTransfered(
                                                    params.listBank[index],
                                                  ),
                                                ),
                                        child: Container(
                                          margin: kPadding[3]!,
                                          padding: kPadding[4]!,
                                          decoration: BoxDecoration(
                                            border: Border(
                                              bottom: BorderSide(
                                                color: themeData.gray100,
                                                width: 1,
                                              ),
                                            ),
                                          ),
                                          child: BankItem(
                                            title:
                                                '${params.listBank[index].shortName} - ${params.listBank[index].accountNumber} - ${'Session().userInfo!.userinfo!.fullname'}',
                                            subTitle:
                                                '${params.listBank[index].fullName}',
                                            logo: Assets.images.icLogoBank.path,
                                          ),
                                        ),
                                      );
                                    },
                                    itemCount: params.listBank.length,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      if (state.selectBank != null)
                        Expanded(
                          child: Text(
                            '${state.selectBank!.shortName} ${state.selectBank!.accountNumber} ${'Session().userInfo!.userinfo!.fullname'}',
                            style: Theme.of(context).textTheme.bodyLarge,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.right,
                          ),
                        )
                      else
                        Expanded(
                          child: Text(
                            '${params.selectedBank.shortName} ${params.selectedBank.accountNumber} ${'Session().userInfo!.userinfo!.fullname'}',
                            style: Theme.of(context).textTheme.bodyLarge,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.right,
                          ),
                        ),
                      kSpacingWidth16,
                      const Icon(Icons.arrow_forward_ios, size: 15),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
