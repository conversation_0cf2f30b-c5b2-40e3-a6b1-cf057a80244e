import 'dart:async';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/category/fu_stock_info_item/derivative_socket_connect.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/category/fu_stock_info_item/stock_info_model.dart';
import 'package:vp_wealth/presentation/wealths/socket/socket_channel.dart';
import 'package:vp_wealth/presentation/wealths/socket/socket_subscriber.dart';


mixin DerivativeConnectStockInfoSocketMixin {
  late final StreamSubscription stockInfoStreamSubscription;

  late final SocketSubscriber stockInfoSocketSubscriber;

  void stockInfoSocketConnect(
      IDerivativeConnectStockInfoSocket listener, String symbol) {
    stockInfoSocketSubscriber = SocketDerivativeConnect().addListener(
      symbol,
      ISocketChannel.stockInfo,
    );
    stockInfoStreamSubscription = stockInfoSocketSubscriber.streamData
        .where((e) => e is DerivativeStockInfoData && e.symbol == symbol)
        .listen(
            (e) => listener.onChangeStockInfoFromSocket(e as DerivativeStockInfoData));
  }

  void stockInfoSocketClose() {
    stockInfoStreamSubscription.cancel();
    stockInfoSocketSubscriber.dispose;
  }
}

abstract class IDerivativeConnectStockInfoSocket {
  void onChangeStockInfoFromSocket(DerivativeStockInfoData data);
}
