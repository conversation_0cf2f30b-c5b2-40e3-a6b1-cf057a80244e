import 'package:flutter/material.dart';
import 'package:vp_common/extensions/price_exts.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/custom_widget/base_bottom_sheet.dart';
import 'package:vp_design_system/widget/button/vps_button.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/model/assets/item_assets_model.dart';
import 'package:vp_wealth/data/model/stock_detail_entity.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/presentation/place_order/utils/ext.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/widget/content_expansion_widget.dart';

class StockDetailBottomSheet extends StatelessWidget {
  const StockDetailBottomSheet({
    Key? key,
    required this.entity,
    required this.portfolio,
    required this.totalMarketValue,
    this.onSell,
    this.onBuy,
  }) : super(key: key);

  final StockDetailEntity entity;

  final Portfolio portfolio;

  final VoidCallback? onSell;

  final VoidCallback? onBuy;

  final num totalMarketValue;

  @override
  Widget build(BuildContext context) {
    final titleStyle = vpTextStyle.body14?.copyWith(
      color: vpColor.textTertiary,
    );

    final valueStyle = vpTextStyle.subtitle14?.copyWith(
      color: vpColor.textPrimary,
    );

    return BaseBottomSheet(
      children: [
        /// Mã chứng khoán
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle,
          title: 'Mã chứng khoán',
          value: entity.symbol,
        ),

        /// Tổng khối lượng CK
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle,
          title: 'Tổng khối lượng CK',
          value: portfolio.totalCalculate.toString(),
        ),

        /// Giá vốn trung bình
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle,
          title: 'Giá vốn trung bình',
          value: portfolio.costPriceCalculate.toDouble().getPriceFormatted(
            currency: '',
            convertToThousand: true,
          ),
        ),

        /// Gốc đầu tư
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle,
          title: 'Gốc đầu tư',
          value: portfolio.capitalValue.valueText,
        ),

        /// Giá trị thị trường
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle,
          title: 'Giá trị thị trường',
          value: portfolio.marketValue(portfolio.closePrice ?? 0).valueText,
        ),

        /// Lãi/lỗ (dự kiến)
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle?.copyWith(
            color: portfolio.colorProfitLoss(portfolio.closePrice ?? 0),
          ),
          title: 'Lãi/lỗ dự kiến',
          value:
              '${portfolio.currentValue(portfolio.closePrice ?? 0).volumeString}đ (${portfolio.percentProfitLoss(portfolio.closePrice ?? 0).toPercent(addPrefixCharacter: false)})',
        ),
        const SizedBox(height: 16),
        Container(height: 1, color: vpColor.strokeNormal),
        const SizedBox(height: 16),
        //    Divider(color: themeData.divider, thickness: 1, height: 48),

        /// Khối lượng CK khả dụng
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle,
          padding: EdgeInsets.zero,
          value: (portfolio.trade ?? 0).volumeString,
          title: 'Khối lượng CK khả dụng',
        ),

        /// CK chờ về
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle,
          value:
              'T0: ${(portfolio.receivingT0 ?? 0).volumeString} T1: ${(portfolio.receivingT1 ?? 0).volumeString} T2: ${(portfolio.receivingT2 ?? 0).volumeString}',
          title: 'CK chờ về',
        ),

        /// CK chờ giao dich & Quyền chờ về
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle,
          title: 'Quyền chờ về',
          value: (portfolio.totalWFT ?? 0).volumeString,
        ),

        /// CK bị hạn chế
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle,
          title: 'CK bị hạn chế',
          value: portfolio.stockRestricted.volumeString,
        ),

        /// Tỉ trọng trong danh mục
        ContentExpansionWidget(
          titleStyle: titleStyle,
          textStyle: valueStyle,
          title: 'Tỉ trọng trong danh mục',
          value: ((portfolio.marketValue(portfolio.closePrice ?? 0) /
                      totalMarketValue) *
                  100)
              .toPercent(addPrefixCharacter: false),
        ),
        kSpacingHeight8,

        /// build bottom actions view
        buildActionBottom(context),
      ],
    );
  }

  Widget buildActionBottom(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: SizeUtils.kSize8),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          kSpacingWidth8,
          Expanded(
            child: VpsButton.primaryDangerSmall(
              onPressed: onSell,
              title: 'Bán',
            ),
          ),
          kSpacingWidth8,
          Expanded(
            child: VpsButton.primarySmall(onPressed: onBuy, title: 'Mua'),
          ),
          kSpacingWidth8,
        ],
      ),
    );
  }
}
