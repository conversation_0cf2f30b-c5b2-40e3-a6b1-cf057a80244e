import 'package:dio/dio.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_common/widget/log_api/models/app_base_response.dart';
import 'package:vp_wealth/data/model/money_statement_model.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/money_domain/money_repository.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/on_boarding_reponse.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/webview/webview_status.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/history/money_cash_transfer_hist_obj.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/money_cash_in/account_balance.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/money_cash_in/bank.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/money_cash_in/bank_register_initialize_param.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/money_cash_in/bank_url_authenticate.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/overview/money_cash_in_out_obj.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/overview/money_summary_account_obj.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/transfer/money_account_recived_obj.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/transfer/money_check_transfer_obj.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/transfer/money_ciinfo_obj.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/transfer/money_fee_atm_obj.dart';

import 'money_models/transfer/money_transfer_obj.dart';
import 'money_path_api.dart';

class MoneyRepositoryImpl extends MoneyRepository {
  final Dio _restDio;
  final Dio _baseDio;
  final Dio _emoneiDio;

  MoneyRepositoryImpl({
    required Dio restDio,
    required Dio baseDio,
    required Dio emoneiDio,
  }) : _restDio = restDio,
       _baseDio = baseDio,
       _emoneiDio = emoneiDio;

  // Tong quan tien
  @override
  Future<MoneySummaryAccountObj?> getMoneySummaryAccount(
    String idSubAccount,
  ) async {
    try {
      final response = await _restDio.get(
        MoneyPathAPI.pathSummaryAccount(idSubAccount),
      );

      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess()) {
        return MoneySummaryAccountObj.fromJson(result.data);
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  // API CashInOut lay du lieu de hien thi chart
  @override
  Future<List<MoneyCashInOutResponseObj?>> getCashInOut({
    String? idSubAccount,
    String? fromDate,
    String? toDate,
  }) async {
    try {
      final response = await _restDio.get(
        MoneyPathAPI.pathCashInOut(
          idSubAccount: idSubAccount,
          fromDate: fromDate,
          toDate: toDate,
        ),
      );

      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess() && result.data is List) {
        return (result.data as List)
            .map((jsonObj) => MoneyCashInOutResponseObj.fromJson(jsonObj))
            .toList();
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  // Lay so tien toi da
  @override
  Future<MoneyCiinfoObj?> ciinfo(String subAccount) async {
    try {
      final response = await _restDio.get(
        MoneyPathAPI.pathCiinfoTransfer(subAccount),
      );

      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess() && result.data is List) {
        for (var jsonObj in result.data) {
          return MoneyCiinfoObj.fromJson(jsonObj);
        }
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
    return null;
  }

  // Lay phi chuyen tien
  @override
  Future<MoneyFeeAmtObj?> feeAmt(String subAccount, num amount) async {
    try {
      final response = await _restDio.get(
        MoneyPathAPI.pathExternalTransferFee(subAccount, amount),
      );

      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess()) {
        return MoneyFeeAmtObj.fromJson(result.data);
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  // Kiem tra giao dich chuyen ngoai de genOTP
  @override
  Future<MoneyCheckTransferResponseObj?> checkTransfer(
    String subAccount,
    MoneyCheckTransferRequestObj param, {
    bool isInternal = true,
  }) async {
    try {
      final response = await _restDio.post(
        isInternal
            ? MoneyPathAPI.pathCheckInternalTransfer(subAccount)
            : MoneyPathAPI.pathCheckExternalTransfer(subAccount),
        data: param.toJson(),
      );

      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess()) {
        return MoneyCheckTransferResponseObj.fromJson(result.data);
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  // Chuyen tien noi bo
  @override
  Future<AppBaseResponse?> internalTransfer(
    String subAccount,
    MoneyTransferRequestObj param,
  ) async {
    try {
      final response = await _restDio.post(
        MoneyPathAPI.pathInternalTransfer(subAccount),
        data: param.toJsonInternal(),
      );
      return AppBaseResponse.fromJson(response.data);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  // Chuyen tien ra ngoai
  @override
  Future<AppBaseResponse?> externalTransfer(
    String subAccount,
    MoneyTransferRequestObj param,
  ) async {
    try {
      final response = await _restDio.post(
        MoneyPathAPI.pathExternalTransfer(subAccount),
        data: param.toJsonOuternal(),
      );

      return AppBaseResponse.fromJson(response.data);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  // Chuyen tien ra ngoai Phái Sinh
  @override
  Future<BEBaseResponse?> externalTransferDerivative(
    String subAccount,
    MoneyTransferRequestObj param,
  ) async {
    try {
      final response = await _baseDio.post(
        MoneyPathAPI.pathExternalTransferDerivative(subAccount),
        data: param.toJsonOuternalDerivative(),
        options: Options(
          headers: {
            'x-request-id': AppHelper().genXRequestID(),
            'custodyCd': 'Session().userInfo?.userinfo?.custodycd' ?? '',
          },
        ),
      );

      return BEBaseResponse.fromJson(response.data);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  // Lay danh sach tai khoan thu huong
  @override
  Future<List<MoneyAccountRecivedObj>> transferAccountList(
    String subAccount,
  ) async {
    try {
      final response = await _restDio.get(
        MoneyPathAPI.pathTransferAccountList(subAccount),
      );

      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess() && result.data is List) {
        return (result.data as List)
            .map((jsonObj) => MoneyAccountRecivedObj.fromJson(jsonObj))
            .toList();
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  // Lay danh sach tai khoan thu huong Phai Sinh
  @override
  Future<List<MoneyAccountRecivedObj>> transferAccountDerivativeList(
    String subAccount,
  ) async {
    try {
      final response = await _baseDio.get(
        MoneyPathAPI.pathTransferAccountDerivativeList(subAccount),
        queryParameters: {
          'transferType': 'outernal',
          'pageSize': 20,
          'pageIndex': 1,
        },
        options: Options(
          headers: {
            'x-request-id': AppHelper().genXRequestID(),
            'custodyCd': 'Session().userInfo?.userinfo?.custodycd' ?? '',
          },
        ),
      );

      final result = BEBaseResponse.fromJson(response.data);

      if (result.isSuccess() && result.data is List) {
        return (result.data as List)
            .map((jsonObj) => MoneyAccountRecivedObj.fromJson(jsonObj))
            .toList();
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  // API lich su chuyen tien
  @override
  Future<List<MoneyCashTransferHistResponseObj>> cashTransferHist(
    String subAccount,
    String fromdate,
    String toDate,
  ) async {
    try {
      final response = await _restDio.get(
        MoneyPathAPI.pathCashTransferHist(subAccount, fromdate, toDate),
      );

      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess() && result.data is List) {
        return (result.data as List)
            .map(
              (jsonObj) => MoneyCashTransferHistResponseObj.fromJson(jsonObj),
            )
            .where((obj) => ['C', 'F', 'E'].contains(obj.rmstatus))
            .toList();
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<List<MoneyStatementModel>> getCashStatementHist({
    required String subAccountId,
    required String fromDate,
    required String toDate,
  }) async {
    try {
      final response = await _restDio.get(
        MoneyPathAPI.getCashStatementHist(
          idSubAccount: subAccountId,
          fromDate: fromDate,
          toDate: toDate,
        ),
      );

      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess() && result.data is List) {
        return (result.data as List)
            .map((e) => MoneyStatementModel.fromJson(e))
            .toList();
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<OnboardingResponse> getCheckLinkRelationWithVPBank() async {
    try {
      String requestId = AppHelper().genXRequestID();

      final response = await _baseDio.get(
        MoneyPathAPI.pathCheckLinkRelationWithVPBank(),
        options: Options(headers: {'x-request-id': requestId}),
      );

      return OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<List<Bank>> getListBankPartner() async {
    try {
      String requestId = AppHelper().genXRequestID();

      final response = await _baseDio.get(
        MoneyPathAPI.pathListBankPartner(),
        options: Options(headers: {'x-request-id': requestId}),
      );

      final result = OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMapList(response.data),
      );

      if (result.isSuccess() && result.data is List) {
        return (result.data as List).map((e) => Bank.fromJson(e)).toList();
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<dynamic> postConfirmLinkRelationWithVPBank() async {
    try {
      String requestId = AppHelper().genXRequestID();

      final response = await _baseDio.get(
        MoneyPathAPI.pathConfirmLinkRelationWithVPBank(),
        options: Options(headers: {'x-request-id': requestId}),
      );

      BankUrlAuthenticate? obj;
      if (DataHelper.getMap(response.data) != null) {
        obj = BankUrlAuthenticate.fromJson(DataHelper.getMap(response.data));
      }

      return OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: obj,
      );
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<dynamic> postInitialLinkRelationWithVPBank(
    BankRegisterInitializeParam bankRegister,
  ) async {
    try {
      String requestId = AppHelper().genXRequestID();

      final response = await _baseDio.post(
        MoneyPathAPI.pathInitialLinkRelationWithVPBank(),
        data: bankRegister.toJson(),
        options: Options(headers: {'x-request-id': requestId}),
      );

      BankUrlAuthenticate? obj;
      if (DataHelper.getMap(response.data) != null) {
        obj = BankUrlAuthenticate.fromJson(DataHelper.getMap(response.data));
      }

      return OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: obj,
      );
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<dynamic> postLogStepLinkRelationWithVPBank({
    required String step,
    required String status,
  }) async {
    try {
      String requestId = AppHelper().genXRequestID();

      StatusWebView statusWebView = StatusWebView(status: status, step: step);

      final response = await _baseDio.post(
        MoneyPathAPI.pathLogStepLinkRelationWithVPBank(),
        data: statusWebView.toJson(),
        options: Options(headers: {'x-request-id': requestId}),
      );

      var result = OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );

      if (result.isSuccess()) {
        return result;
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<dynamic> putRegisterLinkRelationWithVPBank() async {
    try {
      String requestId = AppHelper().genXRequestID();

      final response = await _baseDio.put(
        MoneyPathAPI.pathRegisterLinkRelationWithVPBank(),
        options: Options(
          headers: {'x-request-id': requestId},
          receiveTimeout: const Duration(seconds: 200),
        ),
      );

      final result = OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );

      if (result.isSuccess()) {
        return result;
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future putDeregister({required String idBank}) async {
    try {
      String requestId = AppHelper().genXRequestID();

      final response = await _baseDio.put(
        MoneyPathAPI.pathDeregisterWithVPBank(),
        options: Options(
          headers: {'x-request-id': requestId},
          receiveTimeout: const Duration(seconds: 300),
        ),
      );

      return OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future putDeregisterMBBank({required String idBank}) async {
    try {
      String requestId = AppHelper().genXRequestID();

      final response = await _baseDio.post(
        MoneyPathAPI.pathDeregisterWithMBBank(),
        options: Options(
          headers: {'x-request-id': requestId},
          receiveTimeout: const Duration(seconds: 300),
        ),
      );

      return OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<AccountBalance> getAccountBalance() async {
    try {
      String requestId = AppHelper().genXRequestID();

      final response = await _baseDio.get(
        MoneyPathAPI.pathGetAccountBalance(),
        options: Options(headers: {'x-request-id': requestId}),
      );

      final result = OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );

      if (result.isSuccess()) {
        return AccountBalance.fromJson(result.data as Map<String, dynamic>);
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<OnboardingResponse> postInitialCashInInput({
    required num amount,
    required String accountType,
  }) async {
    try {
      String requestId = AppHelper().genXRequestID();

      final Map<String, dynamic> form = {
        "referenceNumber": requestId,
        "amount": amount,
        "accountType": accountType,
      };

      final response = await _baseDio.post(
        MoneyPathAPI.pathInitialCashInInput(),
        data: form,
        options: Options(headers: {'x-request-id': requestId}),
      );

      return OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<OnboardingResponse> putSumbitCashInInput(String requestCashId) async {
    try {
      String requestId = AppHelper().genXRequestID();

      final response = await _baseDio.put(
        MoneyPathAPI.pathSubmitCashInInput(requestCashId),
        options: Options(headers: {'x-request-id': requestId}),
      );

      return OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<OnboardingResponse> confirmLinkMB({required String otp}) async {
    try {
      final Map<String, dynamic> form = {"otp": otp};

      final response = await _baseDio.post(
        MoneyPathAPI.pathConfirmLinkMB(),
        data: form,
      );

      return OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<OnboardingResponse> requestLinkMB({
    required String linkType,
    required String sourceNumber,
    String? pathName,
    String? releaseDate,
  }) async {
    try {
      final Map<String, dynamic> form = {
        "linkType": linkType,
        "sourceNumber": sourceNumber,
        "releaseDate": releaseDate,
      };

      final response = await _baseDio.post(
        pathName ?? MoneyPathAPI.pathRequestLinkMB(),
        data: form,
      );

      return OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<OnboardingResponse> cashInRequestMB({
    required String transferAmount,
    required String remark,
    required String accountType,
    required String disCountCode,
  }) async {
    try {
      String requestId = AppHelper().genXRequestID();

      final Map<String, dynamic> form = {
        "transferAmount": transferAmount,
        "remark": remark,
        "accountType": accountType,
        "disCountCode": disCountCode,
      };

      final response = await _baseDio.post(
        MoneyPathAPI.pathCashInRequestMB(),
        data: form,
        options: Options(headers: {'x-request-id': requestId}),
      );

      return OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<OnboardingResponse> cashInConfirmMB({
    required String otpRequest,
    required String requestId,
  }) async {
    try {
      final Map<String, dynamic> form = {
        "otpRequest": otpRequest,
        "requestId": requestId.toString(),
      };

      final response = await _baseDio.post(
        MoneyPathAPI.pathCashInConfirmMB(),
        data: form,
        options: Options(
          headers: {'x-request-id': AppHelper().genXRequestID()},
        ),
      );

      return OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BEBaseResponse> saveCustomerShareInfo(String spvCode) async {
    try {
      String requestId = AppHelper().genXRequestID();

      final response = await _emoneiDio.post(
        MoneyPathAPI.saveCustomerShareInfo(),
        data: {'spvCode': spvCode},
        options: Options(headers: {'x-request-id': requestId}),
      );

      return BEBaseResponse.fromJson(response.data);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BEBaseResponse> getSessionID() async {
    try {
      String requestId = AppHelper().genXRequestID();

      final response = await _emoneiDio.get(
        MoneyPathAPI.getSessionID(),
        options: Options(headers: {'x-request-id': requestId}),
      );

      return BEBaseResponse.fromJson(response.data);
    } catch (e) {
      throw HandleError.from(e);
    }
  }
}
