import 'package:dio/dio.dart';
import 'package:vp_common/error/handle_error.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/utils/app_helper.dart';
import 'package:vp_common/widget/log_api/models/app_base_response.dart';
import 'package:vp_wealth/common/decoders/base_decoder.dart' as baseDecoder;
import 'package:vp_wealth/common/decoders/base_decoder.dart';
import 'package:vp_wealth/common/decoders/base_response_interface.dart';
import 'package:vp_wealth/common/utils/string_extensions.dart';
import 'package:vp_wealth/data/model/money_statement_model.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/money_data/money_models/profit/derivative_available_cash_on_hand.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/money_data/money_models/profit/derivative_sub_account_model.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/money_data/money_models/profit/profit_lost_history_model.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/money_data/money_models/profit/securities_overtime_model.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/money_data/money_models/profit/securities_statement_history_model.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/money_data/money_models/profit/share_infor_model.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/money_data/money_models/profit/spv_information_model.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/money_data/money_models/transfer/money_transfer_obj.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/money_domain/money_repository.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/on_boarding_reponse.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/webview/webview_status.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/money_cash_in/account_balance.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/money_cash_in/bank.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/money_cash_in/bank_register_initialize_param.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/money_cash_in/bank_url_authenticate.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/overview/money_cash_in_out_obj.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/overview/money_summary_account_obj.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/param_models/deposit_cash_on_hand_params.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/transfer/money_account_recived_obj.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/transfer/money_check_transfer_obj.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/transfer/money_ciinfo_obj.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/transfer/money_fee_atm_obj.dart';

import 'money_models/history/money_cash_transfer_hist_obj.dart';
import 'money_path_api.dart';

class MoneyRepositoryImpl extends MoneyRepository {
  final Dio _restDio;
  final Dio _baseDio;
  final Dio _emoneiDio;
  final Dio _openApiDio;

  MoneyRepositoryImpl({
    required Dio restDio,
    required Dio baseDio,
    required Dio emoneiDio,
    required Dio openApiDio,
  }) : _restDio = restDio,
       _baseDio = baseDio,
       _emoneiDio = emoneiDio,
       _openApiDio = openApiDio;

  // Tong quan tien
  @override
  Future<MoneySummaryAccountObj?> getMoneySummaryAccount(
    String idSubAccount,
  ) async {
    try {
      final response = await _restDio.get(
        MoneyPathAPI.pathSummaryAccount(idSubAccount),
      );
      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess()) {
        return MoneySummaryAccountObj.fromJson(result.data);
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  // API CashInOut lay du lieu de hien thi chart
  @override
  Future<List<MoneyCashInOutResponseObj?>> getCashInOut({
    String? idSubAccount,
    String? fromDate,
    String? toDate,
  }) async {
    try {
      final response = await _restDio.get(
        MoneyPathAPI.pathCashInOut(
          idSubAccount: idSubAccount,
          fromDate: fromDate,
          toDate: toDate,
        ),
      );

      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess() && result.data is List) {
        return (result.data as List)
            .map((jsonObj) => MoneyCashInOutResponseObj.fromJson(jsonObj))
            .toList();
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  // Lay so tien toi da
  @override
  Future<MoneyCiinfoObj?> ciinfo(String subAccount) async {
    try {
      final response = await _restDio.get(
        MoneyPathAPI.pathCiinfoTransfer(subAccount),
      );
      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess() && result.data is List) {
        for (var jsonObj in result.data) {
          return MoneyCiinfoObj.fromJson(jsonObj);
        }
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
    return null;
  }

  // Lay phi chuyen tien
  @override
  Future<MoneyFeeAmtObj?> feeAmt(String subAccount, num amount) async {
    try {
      final response = await _restDio.get(
        MoneyPathAPI.pathExternalTransferFee(subAccount, amount),
      );
      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess()) {
        return MoneyFeeAmtObj.fromJson(result.data);
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  // Kiem tra giao dich chuyen ngoai de genOTP
  @override
  Future<MoneyCheckTransferResponseObj?> checkTransfer(
    String subAccount,
    MoneyCheckTransferRequestObj param, {
    bool isInternal = true,
  }) async {
    try {
      final response = await _restDio.post(
        isInternal
            ? MoneyPathAPI.pathCheckInternalTransfer(subAccount)
            : MoneyPathAPI.pathCheckExternalTransfer(subAccount),
        data: param.toJson(),
      );
      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess()) {
        return MoneyCheckTransferResponseObj.fromJson(result.data);
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  // Chuyen tien noi bo
  @override
  Future<AppBaseResponse?> internalTransfer(
    String subAccount,
    MoneyTransferRequestObj param,
  ) async {
    try {
      final response = await _restDio.post(
        MoneyPathAPI.pathInternalTransfer(subAccount),
        data: param.toJsonInternal(),
      );
      return AppBaseResponse.fromJson(response.data);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  // Chuyen tien ra ngoai
  @override
  Future<AppBaseResponse?> externalTransfer(
    String subAccount,
    MoneyTransferRequestObj param,
  ) async {
    try {
      final response = await _restDio.post(
        MoneyPathAPI.pathExternalTransfer(subAccount),
        data: param.toJsonOuternal(),
      );
      return AppBaseResponse.fromJson(response.data);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  // Chuyen tien ra ngoai Phái Sinh
  @override
  Future<BEBaseResponse?> externalTransferDerivative(
    String subAccount,
    MoneyTransferRequestObj param,
  ) async {
    try {
      final response = await _baseDio.post(
        MoneyPathAPI.pathExternalTransferDerivative(subAccount),
        data: param.toJsonOuternalDerivative(),
        options: Options(
          headers: {
            'x-request-id': AppHelper().genXRequestID(),
            'custodyCd': 'Session().userInfo?.userinfo?.custodycd' ?? '',
          },
        ),
      );
      return BEBaseResponse.fromJson(response.data);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  // Lay danh sach tai khoan thu huong
  @override
  Future<List<MoneyAccountRecivedObj>> transferAccountList(
    String subAccount,
  ) async {
    try {
      final response = await _restDio.get(
        MoneyPathAPI.pathTransferAccountList(subAccount),
      );
      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess() && result.data is List) {
        return (result.data as List)
            .map((jsonObj) => MoneyAccountRecivedObj.fromJson(jsonObj))
            .toList();
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  // Lay danh sach tai khoan thu huong Phai Sinh
  @override
  Future<List<MoneyAccountRecivedObj>> transferAccountDerivativeList(
    String subAccount,
  ) async {
    try {
      final response = await _baseDio.get(
        MoneyPathAPI.pathTransferAccountDerivativeList(subAccount),
        queryParameters: {
          'transferType': 'outernal',
          'pageSize': 20,
          'pageIndex': 1,
        },
        options: Options(
          headers: {
            'x-request-id': AppHelper().genXRequestID(),
            'custodyCd': 'Session().userInfo?.userinfo?.custodycd' ?? '',
          },
        ),
      );
      final result = BEBaseResponse.fromJson(response.data);

      if (result.isSuccess() && result.data is List) {
        return (result.data as List)
            .map((jsonObj) => MoneyAccountRecivedObj.fromJson(jsonObj))
            .toList();
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  // API lich su chuyen tien
  @override
  Future<List<MoneyCashTransferHistResponseObj>> cashTransferHist(
    String subAccount,
    String fromdate,
    String toDate,
  ) async {
    try {
      final response = await _restDio.get(
        MoneyPathAPI.pathCashTransferHist(subAccount, fromdate, toDate),
      );
      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess() && result.data is List) {
        return (result.data as List)
            .map(
              (jsonObj) => MoneyCashTransferHistResponseObj.fromJson(jsonObj),
            )
            .where((obj) => ['C', 'F', 'E'].contains(obj.rmstatus))
            .toList();
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<List<MoneyStatementModel>> getCashStatementHist({
    required String subAccountId,
    required String fromDate,
    required String toDate,
  }) async {
    try {
      final response = await _restDio.get(
        MoneyPathAPI.getCashStatementHist(
          idSubAccount: subAccountId,
          fromDate: fromDate,
          toDate: toDate,
        ),
      );

      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess() && result.data is List) {
        return (result.data as List)
            .map((e) => MoneyStatementModel.fromJson(e))
            .toList();
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<OnboardingResponse> getCheckLinkRelationWithVPBank() async {
    try {
      String requestId = AppHelper().genXRequestID();

      final response = await _baseDio.get(
        MoneyPathAPI.pathCheckLinkRelationWithVPBank(),
        options: Options(headers: {'x-request-id': requestId}),
      );

      return OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<List<Bank>> getListBankPartner() async {
    try {
      String requestId = AppHelper().genXRequestID();

      final response = await _baseDio.get(
        MoneyPathAPI.pathListBankPartner(),
        options: Options(headers: {'x-request-id': requestId}),
      );

      final result = OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMapList(response.data),
      );

      if (result.isSuccess() && result.data is List) {
        return (result.data as List).map((e) => Bank.fromJson(e)).toList();
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<dynamic> postConfirmLinkRelationWithVPBank() async {
    try {
      String requestId = AppHelper().genXRequestID();

      final response = await _baseDio.get(
        MoneyPathAPI.pathConfirmLinkRelationWithVPBank(),
        options: Options(headers: {'x-request-id': requestId}),
      );

      BankUrlAuthenticate? obj;
      if (DataHelper.getMap(response.data) != null) {
        obj = BankUrlAuthenticate.fromJson(DataHelper.getMap(response.data));
      }

      return OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: obj,
      );
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<dynamic> postInitialLinkRelationWithVPBank(
    BankRegisterInitializeParam bankRegister,
  ) async {
    try {
      String requestId = AppHelper().genXRequestID();

      final response = await _baseDio.post(
        MoneyPathAPI.pathInitialLinkRelationWithVPBank(),
        data: bankRegister.toJson(),
        options: Options(headers: {'x-request-id': requestId}),
      );

      BankUrlAuthenticate? obj;
      if (DataHelper.getMap(response.data) != null) {
        obj = BankUrlAuthenticate.fromJson(DataHelper.getMap(response.data));
      }

      return OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: obj,
      );
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<dynamic> postLogStepLinkRelationWithVPBank({
    required String step,
    required String status,
  }) async {
    try {
      String requestId = AppHelper().genXRequestID();

      StatusWebView statusWebView = StatusWebView(status: status, step: step);

      final response = await _baseDio.post(
        MoneyPathAPI.pathLogStepLinkRelationWithVPBank(),
        data: statusWebView.toJson(),
        options: Options(headers: {'x-request-id': requestId}),
      );

      var result = OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );

      if (result.isSuccess()) {
        return result;
      } else {
        throw HandleError.from(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<dynamic> putRegisterLinkRelationWithVPBank() async {
    try {
      String requestId = AppHelper().genXRequestID();
      _baseDio.options.headers.addAll(<String, dynamic>{
        'x-request-id': requestId.toString(),
      });
      _baseDio.options.receiveTimeout = const Duration(seconds: 200);
      Response response = await _baseDio.put(
        MoneyPathAPI.pathRegisterLinkRelationWithVPBank(),
      );
      // BankUrlAuthenticate? obj;
      //
      // if(DataHelper.getMap(response.data)!=null){
      //   obj = BankUrlAuthenticate.fromJson(
      //       DataHelper.getMap(response.data));
      // }
      final result = OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );
      if (result.isSuccess()) {
        return result;
      } else {
        throw HandleError.from(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future putDeregister({required String idBank}) async {
    try {
      String requestId = AppHelper().genXRequestID();
      _baseDio.options.headers.addAll(<String, dynamic>{
        'x-request-id': requestId.toString(),
      });
      _baseDio.options.receiveTimeout = const Duration(seconds: 300);
      _baseDio.options.connectTimeout = const Duration(seconds: 300);
      Response response = await _baseDio.put(
        MoneyPathAPI.pathDeregisterWithVPBank(),
      );
      final result = OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );
      return result;
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future putDeregisterMBBank({required String idBank}) async {
    try {
      String requestId = AppHelper().genXRequestID();
      _baseDio.options.headers.addAll(<String, dynamic>{
        'x-request-id': requestId.toString(),
      });
      _baseDio.options.receiveTimeout = const Duration(seconds: 300);
      _baseDio.options.connectTimeout = const Duration(seconds: 300);
      Response response = await _baseDio.post(
        MoneyPathAPI.pathDeregisterWithMBBank(),
      );
      final result = OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );
      return result;
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<AccountBalance> getAccountBalance() async {
    try {
      String requestId = AppHelper().genXRequestID();
      _baseDio.options.headers.addAll(<String, dynamic>{
        'x-request-id': requestId.toString(),
      });
      Response response = await _baseDio.get(
        MoneyPathAPI.pathGetAccountBalance(),
      );
      final result = OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );
      if (result.isSuccess()) {
        return AccountBalance.fromJson(result.data as Map<String, dynamic>);
      } else {
        throw HandleError.from(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<OnboardingResponse> postInitialCashInInput({
    required num amount,
    required String accountType,
  }) async {
    try {
      String requestId = AppHelper().genXRequestID();
      _baseDio.options.headers.addAll(<String, dynamic>{
        'x-request-id': requestId.toString(),
      });

      final Map<String, dynamic> form = {
        "referenceNumber": requestId,
        "amount": amount,
        "accountType": accountType,
      };

      Response response = await _baseDio.post(
        MoneyPathAPI.pathInitialCashInInput(),
        data: form,
      );
      final result = OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );

      return result;
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<OnboardingResponse> putSumbitCashInInput(String requestCashId) async {
    try {
      String requestId = AppHelper().genXRequestID();
      _baseDio.options.headers.addAll(<String, dynamic>{
        'x-request-id': requestId.toString(),
      });
      Response response = await _baseDio.put(
        MoneyPathAPI.pathSubmitCashInInput(requestCashId),
      );
      final result = OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );

      return result;
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<List<ProfitLostHistoryModel>> getProfitLostHistory({
    required String subAccountId,
    required String fromDate,
    required String toDate,
    String? symbol,
  }) async {
    try {
      final response = await _restDio.get(
        MoneyPathAPI.getProfitLostHistory(
          idSubAccount: subAccountId,
          fromDate: fromDate,
          toDate: toDate,
          symbol: symbol.hasData ? symbol! : 'All',
        ),
      );

      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess() && result.data is List) {
        return (result.data as List)
            .map((e) => ProfitLostHistoryModel.fromJson(e))
            .toList();
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<OnboardingResponse> confirmLinkMB({required String otp}) async {
    try {
      final Map<String, dynamic> form = {"otp": otp};
      final response = await _baseDio.post(
        MoneyPathAPI.pathConfirmLinkMB(),
        data: form,
      );

      final result = OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );
      return result;
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<OnboardingResponse> requestLinkMB({
    required String linkType,
    required String sourceNumber,
    String? pathName,
    String? releaseDate,
  }) async {
    try {
      final Map<String, dynamic> form = {
        "linkType": linkType,
        "sourceNumber": sourceNumber,
        "releaseDate": releaseDate,
      };
      final response = await _baseDio.post(
        pathName ?? MoneyPathAPI.pathRequestLinkMB(),
        data: form,
      );

      final result = OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );
      return result;
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<OnboardingResponse> cashInRequestMB({
    required String transferAmount,
    required String remark,
    required String accountType,
    required String disCountCode,
  }) async {
    try {
      String requestId = AppHelper().genXRequestID();
      _baseDio.options.headers.addAll(<String, dynamic>{
        'x-request-id': requestId.toString(),
      });
      final Map<String, dynamic> form = {
        "transferAmount": transferAmount,
        "remark": remark,
        "accountType": accountType,
        "disCountCode": disCountCode,
      };
      final response = await _baseDio.post(
        MoneyPathAPI.pathCashInRequestMB(),
        data: form,
      );

      final result = OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );
      return result;
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<OnboardingResponse> cashInConfirmMB({
    required String otpRequest,
    required String requestId,
  }) async {
    try {
      // String requestId = AppHelper().genXRequestID();
      _baseDio.options.headers.addAll(<String, dynamic>{
        'x-request-id': AppHelper().genXRequestID(),
      });
      final Map<String, dynamic> form = {
        "otpRequest": otpRequest,
        "requestId": requestId.toString(),
      };
      final response = await _baseDio.post(
        MoneyPathAPI.pathCashInConfirmMB(),
        data: form,
      );

      final result = OnboardingResponse.fromJson(
        response.data,
        response.statusCode,
        data: DataHelper.getMap(response.data),
      );
      return result;
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<List<SecuritiesStatementHistoryModel>> getSecuritiesStatement({
    required String subAccountId,
    required String fromDate,
    required String toDate,
    String? symbol,
  }) async {
    try {
      final response = await _restDio.get(
        MoneyPathAPI.getSecuritiesStatement(
          idSubAccount: subAccountId,
          fromDate: fromDate,
          toDate: toDate,
          symbol: symbol.hasData ? symbol! : 'All',
        ),
      );

      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess() && result.data is List) {
        return (result.data as List)
            .map((e) => SecuritiesStatementHistoryModel.fromJson(e))
            .where((e) => e.busDate != null)
            .toList();
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<List<SecuritiesOverTimeModel>> getSecuritiesOverTime({
    required String subAccountId,
    required String fromDate,
    required String toDate,
    String? symbol,
  }) async {
    try {
      final response = await _restDio.get(
        MoneyPathAPI.getSecuritiesOverTime(
          idSubAccount: subAccountId,
          fromDate: fromDate,
          toDate: toDate,
          symbol: symbol.hasData ? symbol! : 'All',
        ),
      );

      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess() && result.data is List) {
        return (result.data as List)
            .map((e) => SecuritiesOverTimeModel.fromJson(e))
            .toList();
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e, stacktrace) {
      print(stacktrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<baseDecoder.BaseDecoder<List<DerivativeSubAccountModel>>>
  getDerivativeAccounts() async {
    try {
      final response = await _baseDio.get(MoneyPathAPI.getDerivativeAccounts());
      return baseDecoder.BaseDecoder(
        BEBaseResponse.fromJson(response.data) as BaseResponseInterface,
        decoder: DerivativeSubAccountModel.fromListJson,
      );
    } on DioError catch (e) {
      throw HandleError.from<CoreErrorResponse>(e);
    }
  }

  @override
  Future<BaseDecoder<DerivativeAvailableCashOnHand>>
  getDerivativeAvailableCashOnHand(String accountId) async {
    try {
      final response = await _baseDio.get(
        MoneyPathAPI.getDerivativeAvailableCashOnHand(accountId),
      );
      return baseDecoder.BaseDecoder(
        BEBaseResponse.fromJson(response.data) as BaseResponseInterface,
        decoder: DerivativeAvailableCashOnHand.fromJson,
      );
    } on DioError catch (e) {
      throw HandleError.from<CoreErrorResponse>(e);
    }
  }

  @override
  Future<BEBaseResponse> derivativeDepositCashOnHand(
    String accountId,
    DepositCashOnHandParams params,
  ) async {
    try {
      final response = await _baseDio.post(
        MoneyPathAPI.depositCashOnHand(accountId),
        data: params.toJson(),
        options: Options(
          headers: {'x-request-id': AppHelper().genXRequestID()},
        ),
      );
      return BEBaseResponse.fromJson(response.data);
    } on DioError catch (e) {
      throw HandleError.from<CoreErrorResponse>(e);
    }
  }

  @override
  Future<BEBaseResponse> derivativeWithDrawCashOnHand(
    String accountId,
    DepositCashOnHandParams params,
  ) async {
    try {
      final response = await _baseDio.post(
        MoneyPathAPI.withDrawCashOnHand(accountId),
        data: params.toJson(),
        options: Options(
          headers: {'x-request-id': AppHelper().genXRequestID()},
        ),
      );
      return BEBaseResponse.fromJson(response.data);
    } on DioError catch (e) {
      throw HandleError.from<CoreErrorResponse>(e);
    }
  }

  @override
  Future<List<SpvInformationReponse>> getSPVInformation() async {
    try {
      String requestId = AppHelper().genXRequestID();
      _emoneiDio.options.headers.addAll(<String, dynamic>{
        'x-request-id': requestId.toString(),
      });
      final response = await _emoneiDio.get(MoneyPathAPI.getSPVInfor());
      final result = BEBaseResponse.fromJson(response.data);

      if (result.isSuccess()) {
        if (result.data is List) {
          return (result.data as List)
              .map((e) => SpvInformationReponse.fromJson(e))
              .toList();
        } else {
          return [];
        }
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<ShareInfoReponse> checkCustomerShareInfo(String spvCode) async {
    try {
      String requestId = AppHelper().genXRequestID();
      _emoneiDio.options.headers.addAll(<String, dynamic>{
        'x-request-id': requestId.toString(),
      });
      final response = await _emoneiDio.post(
        MoneyPathAPI.checkCustomerShareInfo(),
        data: {'spvCode': spvCode},
      );
      final result = BEBaseResponse.fromJson(response.data);

      if (result.isSuccess()) {
        return ShareInfoReponse.fromJson(result.data);
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BEBaseResponse> saveCustomerShareInfo(String spvCode) async {
    try {
      String requestId = AppHelper().genXRequestID();
      _emoneiDio.options.headers.addAll(<String, dynamic>{
        'x-request-id': requestId.toString(),
      });
      final response = await _emoneiDio.post(
        MoneyPathAPI.saveCustomerShareInfo(),
        data: {'spvCode': spvCode},
      );

      final result = BEBaseResponse.fromJson(response.data);
      return result;
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BEBaseResponse> getSessionID() async {
    try {
      String requestId = AppHelper().genXRequestID();
      _openApiDio.options.headers.addAll(<String, dynamic>{
        'x-request-id': requestId.toString(),
      });
      final response = await _emoneiDio.get(MoneyPathAPI.getSessionID());

      final result = BEBaseResponse.fromJson(response.data);
      return result;
    } catch (e) {
      throw HandleError.from(e);
    }
  }
}
