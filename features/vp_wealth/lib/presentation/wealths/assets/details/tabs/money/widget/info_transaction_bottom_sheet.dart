import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/data/model/money_statement_model.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/place_order/gen/locale_keys.g.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/widget/info_money_widget.dart';

Future showInfoTransactionBottomSheet(
  BuildContext context,
  MoneyStatementModel model,
) async {
  return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    builder: (_) => InfoTransactionBottomSheet(model: model),
  );
}

class InfoTransactionBottomSheet extends StatelessWidget {
  const InfoTransactionBottomSheet({super.key, required this.model});

  final MoneyStatementModel model;

  @override
  Widget build(BuildContext context) {
    final increase = model.creditAmt != null && model.creditAmt! > 0;
    var money = (increase ? model.creditAmt : model.debitAmt) ?? 0.0;

    money = increase ? money : (money * -1.0);
    return BaseBottomSheet(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        kSpacingHeight16,
        InfoMoneyWidget(
          title: 'Thời gian',
          amount: model.busDate?.toDate() ?? '',
        ),
        kSpacingHeight8,
        InfoMoneyWidget(title: 'Tên giao dịch', amount: model.tltxdesc ?? ''),
        kSpacingHeight8,
        InfoMoneyWidget(
          title: 'Giao dịch',
          amount: money.toMoney(addCharacter: true),
          color: increase ? vpColor.textPriceGreen : vpColor.textPriceRed,
        ),
        kSpacingHeight8,
        InfoMoneyWidget(
          title: 'Số dư sau GD',
          amount: model.available?.toMoney() ?? '--đ',
        ),
        kSpacingHeight8,
        Text(
          'Nội dung',
          style: vpTextStyle.body16?.copyWith(color: themeData.gray700),
        ),
        Text(
          model.txDesc ?? '',
          style: vpTextStyle.body14?.copyWith(color: vpColor.textTertiary),
        ),
        kSpacingHeight24,
        VpsButton.primarySmall(
          width: double.infinity,
          textAlign: TextAlign.center,
          title: WealthStock.current.buttonClose,
          onPressed: () => context.pop(),
        ),
      ],
    );
  }

  Widget item({
    required String title,
    required String value,
    TextStyle? style,
  }) {
    return Row(
      children: [
        Text(
          title,
          style: vpTextStyle.body14?.copyWith(color: vpColor.textTertiary),
        ),
        kSpacingWidth16,
        Expanded(
          child: Text(
            value,
            style: style ?? vpTextStyle.body16,
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }
}
