import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_wealth/data/model/stock_detail_entity.dart';
import 'package:vp_wealth/presentation/wealths/socket/investment_tool_socket_connect.dart';
import 'package:vp_wealth/presentation/wealths/socket/model/investment_tool_oddlot_stock_data.dart';
import 'package:vp_wealth/presentation/wealths/socket/socket_subscriber.dart';

class StockListCacheCubit extends Cubit<String> {
  StockListCacheCubit() : super("");

  Map<String, SocketSubscriber> socketSubscriber = {};

  Map<String, StreamSubscription> priceSocketSubscription = {};

  Map<String, IStockInfoData> stockCache = {};

  // void listenSocketSocket(List<StockDetailEntity> listDataFromAPI) {
  //   for (var item in listDataFromAPI) {
  //     if (stockCache[item.symbol] == null) {
  //       final _priceSocketSubscription = ISocketConnect.instance.addListener(
  //         item.symbol,
  //         ISocketChannel.stockInfo,
  //       );
  //       socketSubscriber[item.symbol] = _priceSocketSubscription;
  //       final _priceSocketSubscriber = _priceSocketSubscription.streamData
  //           .listen((event) {
  //             final data = event as IStockInfoData;
  //             stockCache[item.symbol] = data;
  //           });
  //       priceSocketSubscription[item.symbol] = _priceSocketSubscriber;
  //     }
  //   }
  // }

  _disposeStream() {
    stockCache.clear();
    for (var item in priceSocketSubscription.values) {
      item.cancel();
    }
    for (var item in socketSubscriber.values) {
      item.dispose();
    }
  }

  @override
  Future close() async {
    _disposeStream();

    super.close();
  }
}
