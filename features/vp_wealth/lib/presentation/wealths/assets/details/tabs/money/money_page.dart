import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/model/assets/item_assets_model.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/data/utils/padding_extends.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/cubit/money_page/money_cubit.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/cubit/money_page/money_state.dart';
import 'package:just_the_tooltip/just_the_tooltip.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/widget/info_money_widget.dart';
import 'package:vp_wealth/router/wealth_router.dart';

class WealthMoneyPage extends StatefulWidget {
  const WealthMoneyPage({super.key, required this.model});

  final ItemAssetsModel model;

  @override
  State<WealthMoneyPage> createState() => WealthMoneyPageState();
}

class WealthMoneyPageState extends State<WealthMoneyPage> {
  late WealthMoneyCubit bloc;
  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: bloc,
      child: VPScaffold(
        backgroundColor: vpColor.backgroundElevationMinus1,
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: PullToRefreshView(
              onRefresh: () => bloc.getCashDetail(widget.model),
              child: ListView(
                children: [
                  BlocBuilder<WealthMoneyCubit, WealthMoneyState>(
                    builder: (context, state) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(SizeUtils.kSize16),
                            decoration: BoxDecoration(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(8),
                              ),
                              color: vpColor.backgroundElevation0,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Text(
                                          'Tổng tiền',
                                          style: vpTextStyle.captionMedium
                                              ?.copyWith(
                                                color: vpColor.textSecondary,
                                              ),
                                        ),
                                        kSpacingWidth4,
                                        JustTheTooltip(
                                          margin: const EdgeInsets.only(
                                            left: 16,
                                            right: 200,
                                          ),
                                          preferredDirection:
                                              AxisDirection.down,
                                          triggerMode: TooltipTriggerMode.tap,
                                          showDuration: const Duration(
                                            seconds: 1,
                                          ),
                                          backgroundColor: Colors.white,
                                          content: Padding(
                                            padding: const EdgeInsets.all(
                                              SizeUtils.kSize16,
                                            ),
                                            child: Text(
                                              style: vpTextStyle.captionRegular,
                                              'Tổng tiền =  Tiền mặt + Tiền bán cổ phiếu có thể ứng + Tiền cổ tức chờ về + Tiền phong tỏa - Tiền mua chưa thanh toán cổ phiếu',
                                            ),
                                          ),
                                          child: Assets.icons.icInfoCircleBlue
                                              .svg()
                                              .paddingOnly(bottom: 2),
                                        ),
                                      ],
                                    ),
                                    kSpacingHeight4,
                                    RichText(
                                      text: TextSpan(
                                        children: [
                                          TextSpan(
                                            text: (state.dataCashInfo
                                                        ?.netAsset()
                                                        .toMoney() ??
                                                    '0')
                                                .replaceAll('đ', ''),
                                            style: vpTextStyle.headineBold6
                                                ?.copyWith(
                                                  color: vpColor.textPrimary,
                                                ),
                                          ),
                                          TextSpan(
                                            text: ' đ',
                                            style: vpTextStyle.subtitle14
                                                ?.copyWith(
                                                  color: vpColor.textPrimary,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                VpsButton.primaryXsSmall(
                                  title: 'Nạp tiền',
                                  padding: const EdgeInsets.only(
                                    left: 8,
                                    right: 8,
                                    top: 4,
                                    bottom: 4,
                                  ),
                                  onPressed: () async {
                                    await context.push(
                                      WealthRouter.moneyTransferPage,
                                      extra: widget.model,
                                    );
                                    bloc.getCashDetail(widget.model);
                                  },
                                ),
                              ],
                            ),
                          ),
                          kSpacingHeight8,
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: SizeUtils.kSize16,
                              vertical: SizeUtils.kSize12,
                            ),
                            decoration: BoxDecoration(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(8),
                              ),
                              color: vpColor.backgroundElevation0,
                            ),
                            child: Column(
                              children: [
                                InfoMoneyWidget(
                                  title: 'Tiền có thể rút',
                                  color: vpColor.textAccentGreen,
                                  amount:
                                      num.parse(
                                        state.dataCashInfo?.baldefovd ?? '0',
                                      ).toMoney(),
                                ),
                                InfoMoneyWidget(
                                  title: 'Tiền mặt',
                                  amount:
                                      state.dataCashInfo?.ciBalance
                                          ?.toMoney() ??
                                      '0 đ',
                                ),
                                InfoMoneyWidget(
                                  title: 'Tiền bán chờ về',
                                  amount:
                                      state.dataCashInfo?.receivingAmt
                                          ?.toMoney() ??
                                      '0 đ',
                                ),
                                InfoMoneyWidget(
                                  title: 'Tiền cổ tức chờ về',
                                  amount:
                                      state.dataCashInfo?.careceiving
                                          ?.toMoney() ??
                                      '0 đ',
                                ),
                                InfoMoneyWidget(
                                  title: 'Tiền phong toả',
                                  amount:
                                      state.dataCashInfo?.emkAmt?.toMoney() ??
                                      '0 đ',
                                ),
                                InfoMoneyWidget(
                                  title: 'Mua chưa thanh toán cổ phiếu',
                                  amount:
                                      state.dataCashInfo?.secureAmt
                                          ?.toMoney() ??
                                      '0 đ',
                                ),
                              ],
                            ),
                          ),
                          kSpacingHeight8,
                          Column(
                            children: [
                              _itemRowTextAction(
                                title: 'Lịch sử chuyển tiền',
                                radius: const BorderRadius.only(
                                  topLeft: Radius.circular(8),
                                  topRight: Radius.circular(8),
                                ),
                                icon:
                                    context.read<ThemeCubit>().currentTheme ==
                                            AppTheme.dark
                                        ? Assets.icons.icHistoryDark.svg()
                                        : Assets.icons.icHistory.svg(),
                                onTap:
                                    () => context.push(
                                      WealthRouter.historyPage,
                                      extra: widget.model,
                                    ),
                              ),
                              _itemRowTextAction(
                                title: 'Sao kê tiền',
                                haveDivider: false,
                                radius: const BorderRadius.only(
                                  bottomLeft: Radius.circular(8),
                                  bottomRight: Radius.circular(8),
                                ),
                                icon:
                                    context.read<ThemeCubit>().currentTheme ==
                                            AppTheme.dark
                                        ? Assets.icons.icStatementDark.svg()
                                        : Assets.icons.icStatement.svg(),
                                onTap:
                                    () => context.push(
                                      WealthRouter.stateOfMoneyPage,
                                      extra: widget.model,
                                    ),
                              ),
                            ],
                          ),
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _itemRowTextAction({
    required VoidCallback onTap,
    required String title,
    bool haveDivider = true,
    radius = const BorderRadius.all(Radius.circular(8)),
    required Widget icon,
  }) {
    return Column(
      children: [
        InkWell(
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 0),
            child: Container(
              padding: const EdgeInsets.all(SizeUtils.kSize16),
              decoration: BoxDecoration(
                borderRadius: radius,
                color: vpColor.backgroundElevation0,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      icon,
                      kSpacingWidth12,
                      Padding(
                        padding: const EdgeInsets.only(bottom: 2.0),
                        child: Text(
                          title,
                          style: vpTextStyle.body14?.copyWith(
                            color: vpColor.textPrimary,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: vpColor.iconPrimary,
                    size: 15,
                  ),

                  // SvgPicture.asset(icon),
                ],
              ),
            ),
          ),
        ),
        if (haveDivider)
          Divider(height: 1, thickness: 1, color: vpColor.strokeNormal),
      ],
    );
  }

  @override
  void initState() {
    super.initState();
    bloc = WealthMoneyCubit()..getCashDetail(widget.model);
    // bloc = WealthMoneyCubit();
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   bloc.getCashDetail(widget.model);
    // });
  }
}
