class DerivativeModuleConfig {
  static final DerivativeModuleConfig _singleton =
      DerivativeModuleConfig._internal();

  factory DerivativeModuleConfig() => _singleton;

  DerivativeModuleConfig._internal();

  String _socketUrl = "";

  String get socketUrl => _socketUrl;
  String _derivativeSocketUrl = "";

  String get derivativeSocketUrl => _derivativeSocketUrl;

  void initConfig(String url, String derivativeSocketUrl) {
    _socketUrl = url;
    _derivativeSocketUrl = derivativeSocketUrl;
  }
}
