import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_common/constants/app_constants.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_input/bloc/money_cash_in_input_bloc.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/select_sub_account_bottom_sheet.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_common/lang/money_key_lang.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_common/lang/money_localized_values.dart';

class SelectSubAccount extends StatelessWidget {
  const SelectSubAccount({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MoneyCashInInputBloc, MoneyCashInInputState>(
      builder: (context, state) {
        return Padding(
          padding: kPadding[3]!,
          child: Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    getMoneyLang(MoneyKeyLang.subAccountReceived),
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  kSpacer,
                  InkWell(
                    onTap:
                        () => showSelectSubAccountBottomSheet(
                          context,
                          selectAll: false,
                          isAll: true,
                          isDismissible: false,
                        ).then((value) {
                          if (value != null) {
                            context.read<MoneyCashInInputBloc>().add(
                              MoneyCashInInputSelectedSubAccount(value),
                            );
                          }
                        }),
                    child: Row(
                      children: [
                        if (state.selectSubAccount != null)
                          Text(
                            'Tất cả tiểu khoản ${'vi' == AppConstants.vi ? state.selectSubAccount!.afAcctNoExt!.toLowerCase() : state.selectSubAccount!.enafAcctNoext!.toLowerCase()}',
                            style: Theme.of(context).textTheme.bodyLarge,
                          )
                        else
                          Text(
                            getMoneyLang(MoneyKeyLang.selectSubAccount),
                            style: Theme.of(context).textTheme.bodyLarge!
                                .copyWith(color: themeData.gray700),
                          ),
                        kSpacingWidth16,
                        const Icon(Icons.arrow_forward_ios, size: 15),
                      ],
                    ),
                  ),
                ],
              ),
              BlocBuilder<MoneyCashInInputBloc, MoneyCashInInputState>(
                builder: (context, state) {
                  return state.subAccountStatus ==
                          MoneyCashInSelectSubAccountStatus.invalid
                      ? Align(
                        alignment: Alignment.centerRight,
                        child: Text(
                          getMoneyLang(MoneyKeyLang.pleaseSelectSubAccount),
                          style: vpTextStyle.captionRegular?.copyWith(
                            color: themeData.red,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      )
                      : const SizedBox.shrink();
                },
              ),
            ],
          ),
        );
      },
    );
  }
}
