import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/widget/appbar/appbar_view.dart';
import 'package:vp_design_system/widget/scaffold/scaffold_view.dart';

import '../../../../data/model/assets/item_assets_model.dart';
import 'cubit/detail_assets_cubit.dart';
import 'tabs/money/money_page.dart';

class TotalMoneyScreen extends StatefulWidget {
  final ItemAssetsModel model;

  const TotalMoneyScreen({Key? key, required this.model}) : super(key: key);

  @override
  State<TotalMoneyScreen> createState() => _TotalMoneyScreenState();
}

class _TotalMoneyScreenState extends State<TotalMoneyScreen> {
  final DetailAssetsCubit _cubit = DetailAssetsCubit();

  ItemAssetsModel get _model => widget.model;
  @override
  void initState() {
    super.initState();
    _cubit.onFetchUnreadRight(_model.copierId ?? -1);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<DetailAssetsCubit>(
      create: (context) => _cubit,
      child: VPScaffold(
        appBar: VPAppBar.layer(title: 'Tiền'),
        body: Container(
          color: vpColor.backgroundElevationMinus1,
          child: WealthMoneyPage(model: _model),
        ),
      ),
    );
  }
}
