import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/presentation/tutorial/presentation/cubit/tutorial_cubit.dart';
import 'package:vp_wealth/presentation/tutorial/presentation/tutorial.dart';
import 'utils.dart';
import 'package:vp_common/vp_common.dart' as commont;

import 'tutorial_dot_view.dart';

class TutorialPage extends StatelessWidget {
  const TutorialPage({Key? key, required this.type}) : super(key: key);

  final TutorialType type;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => TutorialCubit(type),
      child: const _TutoriaPageBody(),
    );
  }
}

class _TutoriaPageBody extends StatelessWidget {
  const _TutoriaPageBody({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final paddingBottom = MediaQuery.of(context).padding.bottom;
    return Container(
      decoration: BoxDecoration(
        color: themeData.bgPopupTutorial,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(SizeUtils.kSize24),
          topRight: Radius.circular(SizeUtils.kSize24),
        ),
      ),
      child: IntrinsicHeight(
        child: Column(
          children: [
            const _TutorialTopView(),
            _TutorialBottomView(paddingBottom: paddingBottom),
          ],
        ),
      ),
    );
  }
}

class _TutorialTopView extends StatelessWidget {
  const _TutorialTopView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<TutorialCubit>();
    return SizedBox(
      height: MediaQuery.of(context).size.width * (43 / 65),
      width: MediaQuery.of(context).size.width,
      child: Column(
        children: [
          kSpacingHeight4,
          Row(
            children: [
              const Expanded(child: SizedBox.shrink()),
              IconButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                icon: Assets.icons.icClose.svg(package: Assets.package),
              ),
              kSpacingWidth4,
            ],
          ),
          Expanded(
            child: PageView(
              controller: bloc.controller.pageController,
              children: Utils.getListWidget(context),
              onPageChanged: (value) => bloc.controller.setCurrentIndex(value),
            ),
          ),
        ],
      ),
    );
  }
}

class _TutorialBottomView extends StatelessWidget {
  const _TutorialBottomView({Key? key, required this.paddingBottom})
    : super(key: key);

  final double paddingBottom;

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<TutorialCubit>();
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: SizeUtils.kSize16),
      color: themeData.bgMain,
      height: 210,
      child: Column(
        children: [
          Expanded(
            child: Center(
              child: BlocBuilder<TutorialCubit, TutorialState>(
                buildWhen:
                    (previous, current) => previous.title != current.title,
                builder: (context, state) {
                  return AutoSizeText(
                    state.title,
                    style: vpTextStyle.body16?.copyWith(color: themeData.text),
                    minFontSize: 12,
                    textAlign: TextAlign.center,
                  );
                },
              ),
            ),
          ),
          Row(
            children: [
              IconButton(
                onPressed: () {
                  bloc.controller.previousPage();
                },
                icon: BlocBuilder<TutorialCubit, TutorialState>(
                  builder:
                      (context, state) => AppIconBg(
                        icon: commont.Assets.icons.icArrowBack.svg(
                          colorFilter: ColorFilter.mode(
                            state.currentIndex == 0
                                ? themeData.gray500
                                : themeData.primary,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                ),
              ),
              Expanded(
                child: BlocBuilder<TutorialCubit, TutorialState>(
                  builder: (context, state) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(
                        bloc.controller.listTutorial.length,
                        (index) => TutorialDotView(
                          isCurrentSelect: index == state.currentIndex,
                        ),
                      ),
                    );
                  },
                ),
              ),
              BlocBuilder<TutorialCubit, TutorialState>(
                builder: (context, state) {
                  if (bloc.isCurrentIndex(state.currentIndex)) {
                    return VpsButton.primarySmall(
                      title: 'Hoàn tất',
                      onPressed: () {
                        Navigator.pop(context);
                      },
                    );
                  }
                  return IconButton(
                    onPressed: () {
                      bloc.controller.moveToNext();
                    },
                    icon: AppIconBg(
                      icon: commont.Assets.icons.icArrowNext.svg(
                        color: themeData.primary,
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
          SizedBox(height: paddingBottom),
        ],
      ),
    );
  }
}
