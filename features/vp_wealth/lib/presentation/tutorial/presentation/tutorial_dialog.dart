import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/utils/constains.dart';

import 'cubit/tutorial_cubit.dart';
import 'tutorial_dot_view.dart';
import 'utils.dart';

class TutorialDialog extends StatelessWidget {
  final ValueGetter<Widget> content;
  final ValueGetter<Widget> action;

  const TutorialDialog(
      {super.key, required this.content, required this.action});

  @override
  Widget build(BuildContext context) {
    late final bloc = context.read<TutorialCubit>();
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: SizeUtils.kSize8),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 334,
            child: PageView(
              controller: bloc.controller.pageController,
              children: Utils.getListWidget(context),
              onPageChanged: (value) => bloc.controller.setCurrentIndex(value),
            ),
          ),
          kSpacingHeight16,
          BlocBuilder<TutorialCubit, TutorialState>(
            builder: (context, state) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                    bloc.controller.listTutorial.length,
                    (index) => TutorialDotView(
                        isCurrentSelect: index == state.currentIndex)),
              );
            },
          ),
          kSpacingHeight16,
          content(),
          kSpacingHeight16,
          action(),
        ],
      ),
    );
  }
}
