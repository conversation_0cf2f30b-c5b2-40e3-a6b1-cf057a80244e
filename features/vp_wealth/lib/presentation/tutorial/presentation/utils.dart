import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:vp_common/utils/navigation_service.dart';
import 'package:vp_core/vp_core.dart';
import 'cubit/tutorial_cubit.dart';

class Utils {
  static bool get isVi =>
      GetIt.instance<NavigationService>()
          .navigatorKey
          .currentContext
          ?.locale
          .languageCode ==
      'vi';

  static List<Widget> getListWidget(BuildContext context) {
    List<Widget> listWidget = [];
    final bloc = context.read<TutorialCubit>();
    for (var item in bloc.controller.listTutorial) {
      listWidget.add(
        SizedBox(
          child: Align(
            alignment: Alignment.bottomCenter,
            child: BlocBuilder<ThemeCubit, ThemeState>(
              builder:
                  (context, state) => Image.asset(
                    isDark ? item.pathDarkImage : item.pathLightImage,
                    width: MediaQuery.of(context).size.width * 0.92,
                  ),
            ),
          ),
        ),
      );
    }
    return listWidget;
  }
}
