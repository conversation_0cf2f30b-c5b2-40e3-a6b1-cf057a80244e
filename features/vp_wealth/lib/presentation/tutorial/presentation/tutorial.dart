import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_common/shared_prefs/shared_prefs.dart';
import 'package:vp_common/utils/navigation_service.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/app_dialog/app_dialog.dart';
import 'cubit/tutorial_cubit.dart';
import 'tutorial_dialog.dart';
import 'tutorial_page.dart';

class Tutorial {
  static final Tutorial _singleton = Tutorial._internal();

  factory Tutorial() => _singleton;

  Tutorial._internal();

  Future show(TutorialType type,
      {bool reShow = false, bool forInitState = true}) async {
    if (forInitState) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        _actionShow(type: type, reShow: reShow, showBuilder: _showBottomSheet);
      });
    } else {
      _actionShow(type: type, reShow: reShow, showBuilder: _showBottomSheet);
    }
  }

  Future showDialog(
    TutorialType type, {
    bool reShow = false,
    bool forInitState = true,
    required ValueGetter<Widget> content,
    required ValueGetter<Widget> action,
  }) async {
    if (forInitState) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        _actionShow(
            type: type,
            reShow: reShow,
            showBuilder: (type) => _showDialog(type, content, action));
      });
    } else {
      _actionShow(
          type: type,
          reShow: reShow,
          showBuilder: (type) => _showDialog(type, content, action));
    }
  }

  Future _showBottomSheet(TutorialType type) async {
    final context = GetIt.instance<NavigationService>().navigatorKey.currentContext;
    if (context == null) {
      return;
    }
    return showModalBottomSheet(
      barrierColor: themeData.disabledColor,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) => TutorialPage(
        type: type,
      ),
    );
  }

  Future _showDialog(TutorialType type, ValueGetter<Widget> content,
      ValueGetter<Widget> action) async {
    final context = GetIt.instance<NavigationService>().navigatorKey.currentContext;
    if (context == null) {
      return;
    }
    return appShowDialog(
      contentBuilder: () => BlocProvider(
        create: (context) => TutorialCubit(type),
        child: TutorialDialog(
          content: content,
          action: action,
        ),
      ),
    );
  }

  Future _actionShow({
    required TutorialType type,
    required bool reShow,
    required Future Function(TutorialType) showBuilder,
  }) async {
    final show = SharedPref.getBool(type.name);
    if (!show || reShow) {
      await showBuilder(type);
      SharedPref.setBool(type.name, true);
    }
  }
}

enum TutorialType { home, stock, bond, assets, order, newOrderIntro }
