import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:vp_wealth/presentation/tutorial/presentation/cubit/tutorial_controller.dart';
import 'package:vp_wealth/presentation/tutorial/presentation/tutorial.dart';
part 'tutorial_state.dart';

class TutorialCubit extends Cubit<TutorialState> {
  TutorialController controller = TutorialController();
  final TutorialType type;
  TutorialCubit(this.type) : super(const TutorialState()) {
    controller.initData(type);
    if (controller.listTutorial.isNotEmpty) {
      emit(state.copyWith(title: controller.listTutorial[0].title));
      controller.listenerChangePage((index) {
        emit(state.copyWith(
            currentIndex: index, title: controller.listTutorial[index].title));
      });
    }
  }

  bool isCurrentIndex(int index) {
    return controller.listTutorial.length - 1 == index;
  }
}
