import 'package:flutter/material.dart';
import 'package:vp_wealth/presentation/tutorial/data/tutorial_data.dart';
import 'package:vp_wealth/presentation/tutorial/data/tutorial_obj.dart';
import 'package:vp_wealth/presentation/tutorial/presentation/tutorial.dart';

class TutorialController {
  List<TutorialObj> listTutorial = [];
  TutorialData data = TutorialData();

  TutorialController() {
    pageController = PageController();
  }

  void initData(TutorialType type) {
    final map = {
      TutorialType.home.name: data.listHome,
      TutorialType.stock.name: data.listStock,
      TutorialType.assets.name: data.listAssets,
      TutorialType.bond.name: data.listBond,
      TutorialType.order.name: data.listOrder,
      TutorialType.newOrderIntro.name: data.listIntroOrder
    };
    listTutorial = map[type.name] ?? [];
  }

  int _currentIndex = 0;

  int get currentIndex => _currentIndex;

  void setCurrentIndex(int value) {
    _currentIndex = value;
    _changePage();
  }

  late PageController pageController;

  void moveToNext() {
    if (_currentIndex == isLastIndex) {
      return;
    }
    _currentIndex++;
    _changePage();
  }

  void previousPage() {
    if (_currentIndex == 0) {
      return;
    }
    _currentIndex--;
    _changePage();
  }

  void _changePage() {
    onCallBackIndex(_currentIndex);
    pageController.animateToPage(_currentIndex,
        duration: const Duration(milliseconds: 300), curve: Curves.linear);
  }

  Function(int) onCallBackIndex = (p0) => {};

  void listenerChangePage(Function(int) onCallBack) {
    onCallBackIndex = onCallBack;
  }

  bool get isValidCurrentIndex =>
      (_currentIndex >= 0 && _currentIndex <= listTutorial.length - 1);

  int get isLastIndex => listTutorial.length - 1;
}
