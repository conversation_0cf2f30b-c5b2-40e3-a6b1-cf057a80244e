part of 'tutorial_cubit.dart';

class TutorialState extends Equatable {
  final String title;
  final int currentIndex;

  const TutorialState({this.title = '', this.currentIndex = 0});
  @override
  List<Object?> get props => [title, currentIndex];

  TutorialState copyWith({String? title, int? currentIndex}) {
    return TutorialState(
        title: title ?? this.title,
        currentIndex: currentIndex ?? this.currentIndex);
  }
}
