import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';

class TutorialDotView extends StatelessWidget {
  final bool isCurrentSelect;

  const TutorialDotView({Key? key, required this.isCurrentSelect})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: const EdgeInsets.only(right: SizeUtils.kSize8),
      height: 8,
      width: isCurrentSelect ? SizeUtils.kSize24 : SizeUtils.kSize8,
      decoration: BoxDecoration(
        color: isCurrentSelect ? themeData.primary : themeData.gray300,
        borderRadius: BorderRadius.circular(SizeUtils.kRadius4),
      ),
    );
  }
}
