import 'package:vp_common/utils/navigation_service.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/presentation/tutorial/common/lang/tutorial_lang_key.dart';

Map<String, Map<String, String>> _tutorialLocalizedValues({dynamic value}) => {
      'en': {
        /* HOME */
        TutorialLangKey.home1: 'View Account Info, Setup Settings and Help',
        TutorialLangKey.home2:
            'Place Stock Orders, Hold Portfolio, Order Book, Market Info, Watchlist and other stock widgets',
        TutorialLangKey.home3: 'Place derivative orders',
        TutorialLangKey.home4: 'Using Bonds, eInvest',
        TutorialLangKey.home5:
            'View assets you are holding, Statistics of Money and operations Deposit, Transfer money',

        /* STOCK */
        TutorialLangKey.stock1:
            'Select your watch category, edit the category name, order the codes and remove the codes from the category',
        TutorialLangKey.stock2:
            'Add stock ticker to your self-created portfolio',
        TutorialLangKey.stock3:
            'Stock filter, Market information, Stock order confirmation, Profit and loss log, Cash advance and other utilities',

        /* ASSETS */
        TutorialLangKey.assets1: 'View details of Money items in the account',
        TutorialLangKey.assets2:
            'See detailed value of the product portfolio you are holding',
        TutorialLangKey.assets3: 'View details of advances, deposits and fees',

        /* BOND */
        TutorialLangKey.bond1:
            'Information about the Bond ticker, Place a Buy Order',
        TutorialLangKey.bond2: 'View referral orders to buy Bonds from Carers',

        /* ORDER */
        TutorialLangKey.order1:
            'To place a stock order, please enter order information and stock code',
        TutorialLangKey.order2: 'Market information of the selected token',
        TutorialLangKey.order3:
            'Enter the price and volume of the selected ticker to complete the stock order'
      },
      'vi': {
        /* HOME */
        TutorialLangKey.home1:
            'Xem thông tin tài khoản, Thiết lập cài đặt và Trợ giúp',
        TutorialLangKey.home2:
            'Đặt lệnh cổ phiếu, Danh mục nắm giữ, Sổ lệnh, Thông tin thị trường, Danh sách theo dõi và các tiện ích cổ phiếu khác',
        TutorialLangKey.home3: 'Đặt lệnh phái sinh',
        TutorialLangKey.home4: 'Sử dụng các sản phẩm Trái phiếu, eInvest',
        TutorialLangKey.home5:
            'Xem tài sản bạn đang nắm giữ, Thống kê Tiền và thao tác Nạp tiền, Chuyển tiền',

        /* STOCK */
        TutorialLangKey.stock1:
            'Chọn danh mục theo dõi của bạn, chỉnh sửa tên danh mục, thứ tự các mã và xóa mã khỏi danh mục',
        TutorialLangKey.stock2:
            'Thêm mã chứng khoán vào danh mục tự tạo của bạn',
        TutorialLangKey.stock3:
            'Bộ lọc cổ phiếu, Thông tin thị trường, Xác nhận lệnh cổ phiếu, Nhật ký lãi lỗ, Ứng trước tiền bán và các tiện ích khác',

        /* ASSETS */
        TutorialLangKey.assets1:
            'Xem chi tiết các khoản mục Tiền trong tài khoản',
        TutorialLangKey.assets2:
            'Xem chi tiết giá trị danh mục sản phẩm bạn đang nắm giữ',
        TutorialLangKey.assets3: 'Xem chi tiết nợ ứng trước, ký quỹ và nợ phí',

        /* BOND */
        TutorialLangKey.bond1: 'Thông tin về mã Trái phiếu, Đặt lệnh mua',
        TutorialLangKey.bond2:
            'Xem các lệnh giới thiệu mua Trái phiếu từ Nhân viên chăm sóc',

        /* ORDER */
        TutorialLangKey.order1:
            'Để đặt lệnh cổ phiếu, bạn hãy nhập thông tin lệnh và mã chứng khoán',
        TutorialLangKey.order2: 'Thông tin thị trường của mã đang chọn',
        TutorialLangKey.order3:
            'Nhập giá và khối lượng của mã đang chọn để hoàn tất đặt lệnh cổ phiếu'
      }
    };

String getTutorialLang(String key, {dynamic value}) {
  final context = GetIt.instance<NavigationService>().navigatorKey.currentContext;
  if (context == null) {
    return '';
  }
  var result = _tutorialLocalizedValues(value: value)['vi'] ?? {'': ''};
  return result[key] ?? '';
}
