
import 'package:vp_wealth/presentation/place_order/gen/asset_util.dart';
import 'package:vp_wealth/presentation/tutorial/common/lang/tutorial_lang.dart';
import 'package:vp_wealth/presentation/tutorial/common/lang/tutorial_lang_key.dart';
import 'package:vp_wealth/presentation/tutorial/data/tutorial_obj.dart';

class TutorialData {
  List<TutorialObj> listHome = [];
  List<TutorialObj> listStock = [];
  List<TutorialObj> listAssets = [];
  List<TutorialObj> listBond = [];
  List<TutorialObj> listOrder = [];
  List<TutorialObj> listIntroOrder = [];

  TutorialData() {
    _initListHome();
    _initListStock();
    _initListAssets();
    _initListBond();
    _initListOrder();
    _initListIntroOrder();
  }

  _initListHome() {
    listHome = [
      TutorialObj(
        title: getTutorialLang(TutorialLangKey.home1),
        pathDarkImage: TutorialAssetsUtil.dark.home1.path,
        pathLightImage: TutorialAssetsUtil.light.home1.path,
      ),
      TutorialObj(
        title: getTutorialLang(TutorialLangKey.home2),
        pathDarkImage: TutorialAssetsUtil.dark.home2.path,
        pathLightImage: TutorialAssetsUtil.light.home2.path,
      ),
      TutorialObj(
        title: getTutorialLang(TutorialLangKey.home3),
        pathDarkImage: TutorialAssetsUtil.dark.home3.path,
        pathLightImage: TutorialAssetsUtil.light.home3.path,
      ),
      TutorialObj(
        title: getTutorialLang(TutorialLangKey.home4),
        pathDarkImage: TutorialAssetsUtil.dark.home4.path,
        pathLightImage: TutorialAssetsUtil.light.home4.path,
      ),
      TutorialObj(
        title: getTutorialLang(TutorialLangKey.home5),
        pathDarkImage: TutorialAssetsUtil.dark.home5.path,
        pathLightImage: TutorialAssetsUtil.light.home5.path,
      )
    ];
  }

  _initListStock() {
    listStock = [
      TutorialObj(
        title: getTutorialLang(TutorialLangKey.stock1),
        pathDarkImage: TutorialAssetsUtil.dark.stock1.path,
        pathLightImage: TutorialAssetsUtil.light.stock1.path,
      ),
      TutorialObj(
        title: getTutorialLang(TutorialLangKey.stock2),
        pathDarkImage: TutorialAssetsUtil.dark.stock2.path,
        pathLightImage: TutorialAssetsUtil.light.stock2.path,
      ),
      TutorialObj(
        title: getTutorialLang(TutorialLangKey.stock3),
        pathDarkImage: TutorialAssetsUtil.dark.stock3.path,
        pathLightImage: TutorialAssetsUtil.light.stock3.path,
      ),
    ];
  }

  _initListAssets() {
    listAssets = [
      TutorialObj(
        title: getTutorialLang(TutorialLangKey.assets1),
        pathDarkImage: TutorialAssetsUtil.dark.assets1.path,
        pathLightImage: TutorialAssetsUtil.light.assets1.path,
      ),
      TutorialObj(
        title: getTutorialLang(TutorialLangKey.assets2),
        pathDarkImage: TutorialAssetsUtil.dark.assets2.path,
        pathLightImage: TutorialAssetsUtil.light.assets2.path,
      ),
      TutorialObj(
        title: getTutorialLang(TutorialLangKey.assets3),
        pathDarkImage: TutorialAssetsUtil.dark.assets3.path,
        pathLightImage: TutorialAssetsUtil.light.assets3.path,
      ),
    ];
  }

  _initListBond() {
    listBond = [
      TutorialObj(
        title: getTutorialLang(TutorialLangKey.bond1),
        pathDarkImage: TutorialAssetsUtil.dark.bond1.path,
        pathLightImage: TutorialAssetsUtil.light.bond1.path,
      ),
      TutorialObj(
        title: getTutorialLang(TutorialLangKey.bond2),
        pathDarkImage: TutorialAssetsUtil.dark.bond2.path,
        pathLightImage: TutorialAssetsUtil.light.bond2.path,
      ),
    ];
  }

  _initListOrder() {
    listOrder = [
      TutorialObj(
        title: getTutorialLang(TutorialLangKey.order1),
        pathDarkImage: TutorialAssetsUtil.dark.order1.path,
        pathLightImage: TutorialAssetsUtil.light.order1.path,
      ),
      TutorialObj(
        title: getTutorialLang(TutorialLangKey.order2),
        pathDarkImage: TutorialAssetsUtil.dark.order2.path,
        pathLightImage: TutorialAssetsUtil.light.order2.path,
      ),
      TutorialObj(
        title: getTutorialLang(TutorialLangKey.order3),
        pathDarkImage: TutorialAssetsUtil.dark.order3.path,
        pathLightImage: TutorialAssetsUtil.light.order3.path,
      ),
    ];
  }

  _initListIntroOrder() {
    listIntroOrder = [
      TutorialObj(
        pathDarkImage: TutorialAssetsUtil.dark.orderNewIntro1.path,
        pathLightImage: TutorialAssetsUtil.light.orderNewIntro1.path,
      ),
      TutorialObj(
        pathDarkImage: TutorialAssetsUtil.dark.orderNewIntro2.path,
        pathLightImage: TutorialAssetsUtil.light.orderNewIntro2.path,
      ),
    ];
  }
}
