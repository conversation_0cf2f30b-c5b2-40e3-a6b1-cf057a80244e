import 'dart:ui';

import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_wealth/router/wealth_router.dart';


enum InvestmentStatusEnum {
  all,
  signed,
  draft,
  exit,
  complete,
}

InvestmentStatusEnum valueToInvestmentStatusEnum(int value) {
  switch (value) {
    case 0:
      return InvestmentStatusEnum.draft;
    case 1:
      return InvestmentStatusEnum.signed;
    case 2:
      return InvestmentStatusEnum.exit;
    case 3:
      return InvestmentStatusEnum.complete;
    // case 4:
    //   return InvestmentStatusEnum.deleted;
    default:
      return InvestmentStatusEnum.all;
  }
}

extension InvestmentStatusEnumExtention on InvestmentStatusEnum {
  String get title {
    switch (this) {
      case InvestmentStatusEnum.exit:
        return 'Dừng hoạt động';
      case InvestmentStatusEnum.signed:
        return 'Hoạt động';
      case InvestmentStatusEnum.draft:
        return 'Chưa hoàn thiện';
      case InvestmentStatusEnum.complete:
        return 'Hoàn thành';
      // case InvestmentStatusEnum.close:
      //   return '<PERSON><PERSON>g kế hoạch';
      // case InvestmentStatusEnum.deleted:
      //   return 'Kế hoạch bị xóa';
      case InvestmentStatusEnum.all:
        return 'Tất cả';
    }
  }

  int? get dataServer {
    switch (this) {
      case InvestmentStatusEnum.draft:
        return 0;
      case InvestmentStatusEnum.signed:
        return 1;
      case InvestmentStatusEnum.exit:
        return 2;
      case InvestmentStatusEnum.complete:
        return 3;
      // case InvestmentStatusEnum.deleted:
      //   return 4;
      case InvestmentStatusEnum.all:
        return null;
    }
  }

  Color? get color {
    switch (this) {
      case InvestmentStatusEnum.draft:
        return vpColor.chartBlue.withOpacity(0.16);
      case InvestmentStatusEnum.signed:
      case InvestmentStatusEnum.complete:
        return vpColor.chartGreen.withOpacity(0.16);
      case InvestmentStatusEnum.exit:
        return vpColor.chartRed.withOpacity(0.16);
      // case InvestmentStatusEnum.close:
      // case InvestmentStatusEnum.deleted:
      case InvestmentStatusEnum.all:
        return null;
    }
  }

  String? get getRouterStatus {
    switch (this) {
      case InvestmentStatusEnum.draft:
        return WealthRouter.surveyResult;
      case InvestmentStatusEnum.signed:
      case InvestmentStatusEnum.exit:
      case InvestmentStatusEnum.complete:
        return WealthRouter.detailWealthPlanPage;
      case InvestmentStatusEnum.all:
        return null;
    }
  }

  bool get dataStatusStepIs123{
    switch (this) {
      case InvestmentStatusEnum.draft:
        return false;
      case InvestmentStatusEnum.signed:
      case InvestmentStatusEnum.exit:
      case InvestmentStatusEnum.complete:
        return true;
      default: return false;
    }
  }
}
