// Create a new file: dio_extensions.dart
import 'dart:async';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:vp_common/widget/log_api/models/app_base_response.dart';
import 'package:vp_common/error/transform_error.dart';

extension DioExtensions on Dio {
  Future<T> transformGet<T>(
    String path,
    T Function(dynamic) transform, {
    Map<String, dynamic>? queryParameters,
  }) async {
    final Response<dynamic> response = await get(
      path,
      queryParameters: queryParameters,
    );

    // final result = AppBaseResponse.fromJson(response.data);

    if (response.statusCode == 200 && response.data != null) {
      return compute(transform, response.data);
    } else {
      throw ResponseError.fromDioResponse(response);
    }
  }
}

// dio_helper.dart
class DioHelper {
  static Future<T> transformGet<T>(
    Dio dio,
    String path,
    T Function(dynamic) transform, {
    Map<String, dynamic>? queryParameters,
  }) async {
    final Response<dynamic> response = await dio.get(
      path,
      queryParameters: queryParameters,
    );

    final result = AppBaseResponse.fromJson(response.data);

    if (result.isSuccess() && result.data != null) {
      return compute(transform, result.data);
    } else {
      throw ResponseError.fromDioResponse(response);
    }
  }
}

// // Then use it like:
// var response = await DioHelper.transformGet(
//   _dio,
//   StockCommonApi.securitiesPortfolio(accountId),
//   transformStockPortfolioEntity,
// );
