import 'dart:ui';

import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/theme/theme_service.dart';
import 'package:vp_wealth/generated/assets.gen.dart';

import '../../../common/utils/color_utils.dart';
import '../../enum/money_tranfer_enum.dart';
import '../chart/chart_data_investment_category.dart';

class ItemAssetsModel {
  int? copierId;
  String? copierName;
  String? accountNo;
  String? startDate;
  String? activeDate;
  List<Portfolio>? portfolio;
  CashInfo? cashInfo;
  MoneyTranferEnum? moneyTranferEnum;

  ItemAssetsModel({
    this.copierId,
    this.copierName,
    this.accountNo,
    this.startDate,
    this.portfolio,
    this.cashInfo,
    this.moneyTranferEnum,
  });

  ItemAssetsModel.fromJson(Map<String, dynamic> json) {
    copierId = json['copierId'];
    copierName = json['copierName'];
    accountNo = json['accountNo'];
    startDate = json['startDate'];
    activeDate = json['activeDate'];
    if (json['portfolio'] != null) {
      portfolio = <Portfolio>[];
      json['portfolio'].forEach((v) {
        portfolio!.add(Portfolio.fromJson(v));
      });
    }
    cashInfo =
        json['cashInfo'] != null ? CashInfo.fromJson(json['cashInfo']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['copierId'] = copierId;
    data['copierName'] = copierName;
    data['accountNo'] = accountNo;
    data['startDate'] = startDate;
    data['activeDate'] = activeDate;
    if (portfolio != null) {
      data['portfolio'] = portfolio!.map((v) => v.toJson()).toList();
    }
    if (cashInfo != null) {
      data['cashInfo'] = cashInfo!.toJson();
    }
    return data;
  }

  List<ChartDataInvestmentCategory> get chartData =>
      (portfolio ?? [])
          .asMap()
          .map(
            (index, item) => MapEntry(
              index,
              ChartDataInvestmentCategory(
                item.symbol ?? '',
                ((item.marketValue(item.closePrice ?? 0) / totalMarketValue) *
                    100),
                Color(colors[index]),
              ),
            ),
          )
          .values
          .toList();

  // Tài sản ròng = Tổng giá trị danh mục + Tổng tiền - Nợ
  // Spec mới v2 tài sản ròng sẽ không trừ đi nợ
  num get netAssets => totalMarketValue + totalMoney /*- debt*/;

  // Tổng tiền
  num get totalMoney =>
      (cashInfo?.ciBalance ?? 0) +
      ((cashInfo?.receivingAmt ?? 0) - (cashInfo?.advancedAmt ?? 0)) +
      (cashInfo?.careceiving ?? 0) +
      (cashInfo?.emkAmt ?? 0) -
      (cashInfo?.secureAmt ?? 0);

  // Nợ
  num get debt => (cashInfo?.ciDepoFeeAcr ?? 0) + (cashInfo?.smsFeeAmt ?? 0);

  // Tổng giá trị thị trường
  num get totalMarketValue =>
      portfolio?.fold<num>(
        0,
        (total, item) => total + item.marketValue(item.closePrice ?? 0),
      ) ??
      0;

  // Tổng giá trị vốn
  num get totalCapitalValue =>
      portfolio?.fold<num>(0, (total, item) => total + item.capitalValue) ?? 0;

  // Tổng giá trị hiện tại
  num get totalCurrentValue =>
      portfolio?.fold<num>(
        0,
        (total, item) => total + item.currentValue(item.closePrice ?? 0),
      ) ??
      0;

  // Tổng phần trăm lãi lỗ
  double get totalPercentProfitLoss {
    if (totalCapitalValue == 0) return 0;
    return (totalMarketValue / totalCapitalValue - 1) * 100;
  }

  // Màu lãi lỗ
  Color get colorProfitLoss {
    if (totalPercentProfitLoss < 0) return vpColor.textPriceRed;
    return vpColor.textPriceGreen;
  }

  // Icon lãi lỗ
  String get iconProfitLoss {
    if (totalPercentProfitLoss < 0) return Assets.icons.icDropdown.path;
    return Assets.icons.icArrowUp.path;
  }
}

class Portfolio {
  String? cusToDyCD;
  String? accountID;
  String? symbol;
  String? secType;
  String? tradePlace;
  num? total;
  num? trade;
  int? blocked;
  int? vsdMortGage;
  int? mortGage;
  int? restrict;
  int? receivingRight;
  int? receivingT0;
  int? receivingT1;
  int? receivingT2;
  int? costPrice;
  int? costPriceAmt;
  int? basicPrice;
  int? basicPriceAmt;
  String? marginAmt;
  int? pnLamt;
  String? pnlRate;
  String? isSell;
  num? closePrice;
  int? withDraw;
  int? matchIngAmt;
  int? totalPnl;
  String? productTypeName;
  String? fullName;
  int? r;
  int? lCount;
  int? totalWFT;
  int? costPriceWFT;
  int? costPriceAmtWFT;

  Portfolio({
    this.cusToDyCD,
    this.accountID,
    this.symbol,
    this.secType,
    this.tradePlace,
    this.total,
    this.trade,
    this.blocked,
    this.vsdMortGage,
    this.mortGage,
    this.restrict,
    this.receivingRight,
    this.receivingT0,
    this.receivingT1,
    this.receivingT2,
    this.costPrice,
    this.costPriceAmt,
    this.basicPrice,
    this.basicPriceAmt,
    this.marginAmt,
    this.pnLamt,
    this.pnlRate,
    this.isSell,
    this.closePrice,
    this.withDraw,
    this.matchIngAmt,
    this.totalPnl,
    this.productTypeName,
    this.fullName,
    this.r,
    this.lCount,
    this.totalWFT,
    this.costPriceWFT,
    this.costPriceAmtWFT,
  });

  Portfolio.fromJson(Map<String, dynamic> json) {
    cusToDyCD = json['cusToDyCD'];
    accountID = json['accountID'];
    symbol = json['symbol'];
    secType = json['secType'];
    tradePlace = json['tradePlace'];
    total = json['total'];
    trade = json['trade'];
    blocked = json['blocked'];
    vsdMortGage = json['vsdMortGage'];
    mortGage = json['mortGage'];
    restrict = json['restrict'];
    receivingRight = json['receivingRight'];
    receivingT0 = json['receivingT0'];
    receivingT1 = json['receivingT1'];
    receivingT2 = json['receivingT2'];
    costPrice = json['costPrice'];
    costPriceAmt = json['costPriceAmt'];
    basicPrice = json['basicPrice'];
    basicPriceAmt = json['basicPriceAmt'];
    marginAmt = json['marginAmt'];
    pnLamt = json['pnLamt'];
    pnlRate = json['pnlRate'];
    isSell = json['isSell'];
    closePrice = json['closePrice'];
    withDraw = json['withDraw'];
    matchIngAmt = json['matchIngAmt'];
    totalPnl = json['totalPnl'];
    productTypeName = json['productTypeName'];
    fullName = json['fullName'];
    r = json['r'];
    lCount = json['lCount'];
    totalWFT = json['total_WFT'];
    costPriceWFT = json['costPrice_WFT'];
    costPriceAmtWFT = json['costPriceAmt_WFT'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['cusToDyCD'] = cusToDyCD;
    data['accountID'] = accountID;
    data['symbol'] = symbol;
    data['secType'] = secType;
    data['tradePlace'] = tradePlace;
    data['total'] = total;
    data['trade'] = trade;
    data['blocked'] = blocked;
    data['vsdMortGage'] = vsdMortGage;
    data['mortGage'] = mortGage;
    data['restrict'] = restrict;
    data['receivingRight'] = receivingRight;
    data['receivingT0'] = receivingT0;
    data['receivingT1'] = receivingT1;
    data['receivingT2'] = receivingT2;
    data['costPrice'] = costPrice;
    data['costPriceAmt'] = costPriceAmt;
    data['basicPrice'] = basicPrice;
    data['basicPriceAmt'] = basicPriceAmt;
    data['marginAmt'] = marginAmt;
    data['pnLamt'] = pnLamt;
    data['pnlRate'] = pnlRate;
    data['isSell'] = isSell;
    data['closePrice'] = closePrice;
    data['withDraw'] = withDraw;
    data['matchIngAmt'] = matchIngAmt;
    data['totalPnl'] = totalPnl;
    data['productTypeName'] = productTypeName;
    data['fullName'] = fullName;
    data['r'] = r;
    data['lCount'] = lCount;
    data['total_WFT'] = totalWFT;
    data['costPrice_WFT'] = costPriceWFT;
    data['costPriceAmt_WFT'] = costPriceAmtWFT;
    return data;
  }

  // Chứng khoán bị hạn chế giao dịch
  num get stockRestricted =>
      (blocked ?? 0) +
      (mortGage ?? 0) +
      (vsdMortGage ?? 0) +
      (restrict ?? 0) +
      (withDraw ?? 0);

  // Giá vốn
  num get costPriceCalculate {
    return capitalValue / totalCalculate;
  }

  // Khối lượng
  num get totalCalculate {
    return ((total ?? 0) + (totalWFT ?? 0));
  }

  // Giá trị thị trường
  num marketValue(num closePrice) {
    return totalCalculate * closePrice;
  }

  // Giá trị vốn
  num get capitalValue {
    return ((costPriceAmt ?? 0) + (costPriceAmtWFT ?? 0));
  }

  // Giá trị hiện tại
  num currentValue(num closePrice) {
    return marketValue(closePrice) - capitalValue;
  }

  // Phần trăm lãi lỗ
  double percentProfitLoss(num closePrice) {
    return capitalValue == 0
        ? 0
        : (marketValue(closePrice) / capitalValue - 1) * 100;
  }

  // Màu lãi lỗ
  Color colorProfitLoss(num closePrice, {bool priceChanged = false}) {
    if (priceChanged) return ColorDefine.white;
    if (percentProfitLoss(closePrice) < 0) return vpColor.textPriceRed;
    return vpColor.textPriceGreen;
  }

  String iconProfitLoss(num closePrice) {
    if (percentProfitLoss(closePrice) < 0) return Assets.icons.icDropdown.path;
    return Assets.icons.icArrowUp.path;
  }
}

class CashInfo {
  int? balance;
  int? ciBalance;
  int? tdBalance;
  int? intBalance;
  int? careceiving;
  int? receivingT1;
  int? receivingT2;
  int? receivingT3;
  int? securitiesAmt;
  int? totalDebtAmt;
  int? secureAmt;
  int? trfBuyAmt;
  int? t0debtAmt;
  int? advancedAmt;
  int? dfDebtAmt;
  int? tdDebtAmt;
  int? ciDepoFeeAcr;
  int? depositFeeAmtCt;
  int? netAssetValue;
  int? mrCrLimit;
  int? debtAmt;
  int? advanceMaxAmtFee;
  int? receivingAmt;
  int? marginRate;
  num? smsFeeAmt;
  int? holdBalance;
  int? mrIrate;
  int? mrmRate;
  int? ciDePoFee;
  int? tdIntAmt;
  int? addVnd;
  int? addVnd1;
  String? coreBank;
  dynamic bankAcctNo;
  String? bankName;
  int? emkAmt;
  String? baldefovd;
  int? marginAmt;
  String? accountNumber;
  int? einvestAmt;
  int? einvestFix;
  int? ibrokerFeeAmt;

  CashInfo({
    this.balance,
    this.ciBalance,
    this.tdBalance,
    this.intBalance,
    this.careceiving,
    this.receivingT1,
    this.receivingT2,
    this.receivingT3,
    this.securitiesAmt,
    this.totalDebtAmt,
    this.secureAmt,
    this.trfBuyAmt,
    this.t0debtAmt,
    this.advancedAmt,
    this.dfDebtAmt,
    this.tdDebtAmt,
    this.ciDepoFeeAcr,
    this.depositFeeAmtCt,
    this.netAssetValue,
    this.mrCrLimit,
    this.debtAmt,
    this.advanceMaxAmtFee,
    this.receivingAmt,
    this.marginRate,
    this.smsFeeAmt,
    this.holdBalance,
    this.mrIrate,
    this.mrmRate,
    this.ciDePoFee,
    this.tdIntAmt,
    this.addVnd,
    this.addVnd1,
    this.coreBank,
    this.bankAcctNo,
    this.bankName,
    this.emkAmt,
    this.baldefovd,
    this.marginAmt,
    this.accountNumber,
    this.einvestAmt,
    this.einvestFix,
    this.ibrokerFeeAmt,
  });

  CashInfo.fromJson(Map<String, dynamic> json) {
    balance = json['balance'];
    ciBalance = json['ciBalance'];
    tdBalance = json['tdBalance'];
    intBalance = json['intBalance'];
    careceiving = json['careceiving'];
    receivingT1 = json['receivingT1'];
    receivingT2 = json['receivingT2'];
    receivingT3 = json['receivingT3'];
    securitiesAmt = json['securitiesAmt'];
    totalDebtAmt = json['totalDebtAmt'];
    secureAmt = json['secureAmt'];
    trfBuyAmt = json['trfBuyAmt'];
    t0debtAmt = json['t0debtAmt'];
    advancedAmt = json['advancedAmt'];
    dfDebtAmt = json['dfDebtAmt'];
    tdDebtAmt = json['tdDebtAmt'];
    ciDepoFeeAcr = json['ciDepoFeeAcr'];
    depositFeeAmtCt = json['depositFeeAmtCt'];
    netAssetValue = json['netAssetValue'];
    mrCrLimit = json['mrCrLimit'];
    debtAmt = json['debtAmt'];
    advanceMaxAmtFee = json['advanceMaxAmtFee'];
    receivingAmt = json['receivingAmt'];
    marginRate = json['marginRate'];
    smsFeeAmt = json['smsFeeAmt'];
    holdBalance = json['holdBalance'];
    mrIrate = json['mrIrate'];
    mrmRate = json['mrmRate'];
    ciDePoFee = json['ciDePoFee'];
    tdIntAmt = json['tdIntAmt'];
    addVnd = json['addVnd'];
    addVnd1 = json['addVnd1'];
    coreBank = json['coreBank'];
    bankAcctNo = json['bankAcctNo'];
    bankName = json['bankName'];
    emkAmt = json['emkAmt'];
    baldefovd = json['baldefovd'];
    marginAmt = json['marginAmt'];
    accountNumber = json['accountNumber'];
    einvestAmt = json['einvestAmt'];
    einvestFix = json['einvestFix'];
    ibrokerFeeAmt = json['ibrokerFeeAmt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['balance'] = balance;
    data['ciBalance'] = ciBalance;
    data['tdBalance'] = tdBalance;
    data['intBalance'] = intBalance;
    data['careceiving'] = careceiving;
    data['receivingT1'] = receivingT1;
    data['receivingT2'] = receivingT2;
    data['receivingT3'] = receivingT3;
    data['securitiesAmt'] = securitiesAmt;
    data['totalDebtAmt'] = totalDebtAmt;
    data['secureAmt'] = secureAmt;
    data['trfBuyAmt'] = trfBuyAmt;
    data['t0debtAmt'] = t0debtAmt;
    data['advancedAmt'] = advancedAmt;
    data['dfDebtAmt'] = dfDebtAmt;
    data['tdDebtAmt'] = tdDebtAmt;
    data['ciDepoFeeAcr'] = ciDepoFeeAcr;
    data['depositFeeAmtCt'] = depositFeeAmtCt;
    data['netAssetValue'] = netAssetValue;
    data['mrCrLimit'] = mrCrLimit;
    data['debtAmt'] = debtAmt;
    data['advanceMaxAmtFee'] = advanceMaxAmtFee;
    data['receivingAmt'] = receivingAmt;
    data['marginRate'] = marginRate;
    data['smsFeeAmt'] = smsFeeAmt;
    data['holdBalance'] = holdBalance;
    data['mrIrate'] = mrIrate;
    data['mrmRate'] = mrmRate;
    data['ciDePoFee'] = ciDePoFee;
    data['tdIntAmt'] = tdIntAmt;
    data['addVnd'] = addVnd;
    data['addVnd1'] = addVnd1;
    data['coreBank'] = coreBank;
    data['bankAcctNo'] = bankAcctNo;
    data['bankName'] = bankName;
    data['emkAmt'] = emkAmt;
    data['baldefovd'] = baldefovd;
    data['marginAmt'] = marginAmt;
    data['accountNumber'] = accountNumber;
    data['einvestAmt'] = einvestAmt;
    data['einvestFix'] = einvestFix;
    data['ibrokerFeeAmt'] = ibrokerFeeAmt;
    return data;
  }

  //Tổng tiền = [Cash_detail.cibalance + (Cash_detail.receivingamt - Cash_detail.advancedamt)
  // + Cash_detail.careceiving + Cash_detail.emkamt - Cash_detail.secureamt]
  //TS-192
  num netAsset() {
    return (ciBalance ?? 0) +
        ((receivingAmt ?? 0) - (advancedAmt ?? 0)) +
        (careceiving ?? 0) +
        (emkAmt ?? 0) -
        (secureAmt ?? 0);
  }
}
