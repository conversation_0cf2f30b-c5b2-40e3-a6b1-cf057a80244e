import 'model/asset/history_request_model.dart';

class WealthPathApi {
  static const getSurveyStatus = '/wealth-master/survey/api/v1/surveyStatus';
  static const surveyQuestions = '/wealth-master/survey/api/v1/question';
  static const answerSurvey = '/wealth-master/survey/api/v1/answer';
  static const sampleCategory = '/wealth-master/survey/api/v1/suggestionMP';
  static const listStockOfWealth = '/wealth-master/stock';
  static const getFileContract = '/file/external/files';
  static const listCategoryByType =
      '/wealth-master/survey/api/v1/suggestionByType';
  static const createPlan = '/wealth-copier/external/copier';
  static const commandHistory = '/wealth-order/external/wealth-orders/history';
  static const investmentCommand = '/wealth-copier/external/investmentNotice';
  static const valueNAV = '/wealth-copier/external/wealths/nav';
  static const assetsHeld = '/wealth-copier/external/wealths/assets';
  static const stockRights = '/wealth-copier/external/wealths/rights/info';
  static const expectedPerformance = '/wealth-copier/external/asset/expected';

  static String updateBrokerNo({required int copierId}) {
    return '/wealth-copier/external/copier/$copierId/broker';
  }

  static String activePlan({required int copierId}) {
    return '/wealth-copier/external/copier/$copierId/active';
  }

  static String previewContract({required int copierId}) {
    return '/wealth-copier/external/copier/$copierId/contract/preview';
  }

  static String previewRegistrationContract({required int copierId}) {
    return '/wealth-copier/external/copier/$copierId/product-registration-contract/preview';
  }

  static String previewModifiedContract({required int copierId}) {
    return '/wealth-copier/external/copier/$copierId/modified-contract/preview';
  }

  static String deletePlan({required int copierId}) {
    return '/wealth-copier/external/copier/$copierId/delete';
  }

  static String cancelOrder({required int copierId}) {
    return '/wealth-copier/external/copier/$copierId/free-invest/cancel';
  }

  static String acceptCommand({required int id}) {
    return '/wealth-copier/external/investmentNotice/$id/accept';
  }

  static String detailAcceptCommand({required int id}) {
    return '/wealth-copier/external/investmentNotice/$id';
  }

  static String checkChangePlan({required int copierId}) {
    return '/wealth-copier/external/copier/$copierId/comparison';
  }

  static String updateChangePlan({required int copierId}) {
    return '/wealth-copier/external/copier/$copierId';
  }

  static String updateDraftPlan({required int copierId}) {
    return '/wealth-copier/external/copier/$copierId/draft';
  }

  static String stopInvestment({required int copierId}) {
    return '/wealth-copier/external/copier/$copierId/stop-invest';
  }

  static String activeInvestment({required int copierId}) {
    return '/wealth-copier/external/copier/$copierId/active-invest';
  }

  static String cashDetail({required String copierId}) {
    return '/wealth-copier/external/copier/$copierId/cash-detail';
  }

  static String cashTranferHistory(
      {required HistoriesRequestModel historiesRequestModel}) {
    return '/wealth-copier/external/copier/${historiesRequestModel.copierId}/cashTransfer/history';
  }

  static String depositMoney({required int copierId}) {
    return '/wealth-copier/external/copier/$copierId/deposit';
  }

  static String withdrawMoney({required int copierId}) {
    return '/wealth-copier/external/copier/$copierId/withdraw';
  }

  static String getInfoAccount() {
    return '/wealth-account/accountManagement/copierInfos';
  }

  // Lay danh sach hien thi chart
  static String getCashStatementHist({
    required String idSubAccount,
    required String accountId,
    required String fromDate,
    required String toDate,
  }) {
    return '/flex/report/accounts/$idSubAccount/cashStatementHist?accountID=$accountId&fromDate=$fromDate&toDate=$toDate';
  }

  static String proactiveBuyStock({required int copierId}) {
    return '/wealth-copier/external/copier/$copierId/free-invest/buy';
  }

  static String proactiveSellStock({required int copierId}) {
    return '/wealth-copier/external/copier/$copierId/free-invest/sell';
  }

  static String stockBySymbols(String symbols) =>
      "/invest/api/stockInfoByList?symbols=$symbols";

  static String orderSummary({required int copierId}) {
    return '/wealth-copier/external/copier/$copierId/invest';
  }

  static String previewContractContinueInvestment({required int copierId}) {
    return '/wealth-copier/external/copier/$copierId/active-invest-contract/preview';
  }

  static String previewContractStopInvestment({required int copierId}) {
    return '/wealth-copier/external/copier/$copierId/stop-invest-contract/preview';
  }

  // Wea-047 : Api lấy danh sách sổ lệnh
  static String getInvestmentOrders(
      int page,
      int pageSize,
      String? status,
      String? investType,
      String? transactionKind,
      String? fromDate,
      String? toDate) {
    return '/wealth-copier/external/invest?page=$page&size=$pageSize&status=$status&investType=$investType&transactionKind=$transactionKind&fromDate=$fromDate&toDate=$toDate';
  }

  // Wea_048 : chi tiết sổ lệnh đã xác nhận
  static String getDetailAcceptCommand(
      {required String type, required int copierId, required String status}) {
    return '/wealth-copier/external/invest/$type/$copierId?status=$status';
  }
}
