import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:vp_common/error/handle_error.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/widget/log_api/models/app_base_response.dart';
import 'package:vp_core/utils/app_device_id.dart';
import 'package:vp_wealth/common/utils/stock_utils.dart';
import 'package:vp_wealth/data/model/asset/history_request_model.dart';
import 'package:vp_wealth/data/model/assets/item_assets_model.dart';
import 'package:vp_wealth/data/model/assets/nav_model.dart';
import 'package:vp_wealth/data/model/assets/right_model.dart';
import 'package:vp_wealth/data/model/category/category_model.dart';
import 'package:vp_wealth/data/model/category/wealth_category_detail_model.dart';
import 'package:vp_wealth/data/model/chart_price_entity.dart';
import 'package:vp_wealth/data/model/chart_price_model.dart';
import 'package:vp_wealth/data/model/contract/wealth_contract_model.dart';
import 'package:vp_wealth/data/model/money_statement_model.dart';
import 'package:vp_wealth/data/model/order/accept_command_data_model.dart';
import 'package:vp_wealth/data/model/order/command_history_model.dart';
import 'package:vp_wealth/data/model/order/investment_command_model.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/data/model/stock_detail_entity.dart';
import 'package:vp_wealth/domain/entity/accept_command_data_entity.dart';
import 'package:vp_wealth/domain/entity/command_history_entity.dart';
import 'package:vp_wealth/domain/entity/investment_command_entity.dart';
import 'package:vp_wealth/domain/entity/item_expected_performance.dart';
import 'package:vp_wealth/domain/entity/plan_entity.dart';
import 'package:vp_wealth/domain/request/fetch_list_command_history_request.dart';
import 'package:vp_wealth/domain/request/fetch_list_investment_command_request.dart';
import 'package:vp_wealth/domain/request/fetch_list_plan_request.dart';
import 'package:vp_wealth/domain/request/order_request.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/presentation/model/broker_model.dart';
import 'package:vp_wealth/presentation/place_order/data/model/response/stock_detail_model.dart';
import 'package:vp_wealth/presentation/place_order/model/securities_portfolio_responses_model.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/stock_order_entity.dart';
import 'package:vp_wealth/presentation/wealths/order/stock_order_model.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/data/models/stock_price_chart_model.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/data/repo/main_stock_path_api.dart';

import '../domain/entity/investment_order_entity.dart';
import '../presentation/wealths/stock/details_stock/data/repo/stock_common_api_path.dart';
import 'model/asset/copier_info.dart';
import 'model/asset/transaction_model.dart';
import 'model/order/investment_order_model.dart';
import 'model/survey/survey_question_model.dart';
import 'wealth_path_api.dart';

class WealthRepositoryImpl extends WealthRepository {
  final Dio _restDio;
  final Dio _investDio;
  final Dio _flexDio;

  WealthRepositoryImpl({
    required Dio restDio,
    required Dio investDio,
    required Dio flexDio,
  }) : _restDio = restDio,
       _investDio = investDio,
       _flexDio = flexDio;

  @override
  Future<int> getSurveyStatus() async {
    try {
      final response = await _restDio.get(WealthPathApi.getSurveyStatus);
      final result = BEBaseResponse.fromJson(response.data);

      if (result.isSuccess() && result.data != null) {
        return result.data['checkSurvey'] as int;
      }

      throw ResponseError.fromDioResponse(response);
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<List<SurveyQuestionModel>> getSurveyQuestions() async {
    try {
      final response = await _restDio.get(WealthPathApi.surveyQuestions);
      final BEBaseResponse result = BEBaseResponse.fromJson(response.data);
      List<SurveyQuestionModel> listMpItem = [];
      SurveyQuestionReponse surveyQuestionReponse;

      if (result.data != null) {
        surveyQuestionReponse = SurveyQuestionReponse.fromJson(result.data);
        listMpItem = surveyQuestionReponse.questions;
        return listMpItem;
      } else {
        return [];
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<int> updateSurveyAnswer(Map<String, dynamic> data) async {
    try {
      final response = await _restDio.post(
        WealthPathApi.answerSurvey,
        data: data,
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result.data['point'] ?? 0;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<CategoryModel> getSampleCategory() async {
    try {
      final response = await _restDio.get(WealthPathApi.sampleCategory);
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return CategoryModel.fromJson(result.data);
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse; 
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<List<CategoryModel>> getListCategoryByType() async {
    try {
      final response = await _restDio.get(WealthPathApi.listCategoryByType);
      final result = BEBaseResponse.fromJson(response.data);
      List<CategoryModel> listCategory = [];

      if (result.isSuccess()) {
        if (result.data != null) {
          result.data.forEach((e) {
            listCategory.add(CategoryModel.fromJson(e));
          });
          return listCategory;
        } else {
          return [];
        }
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<dynamic> createDraftPLan(PlanModel data) async {
    try {
      final response = await _restDio.post(
        WealthPathApi.createPlan,
        data: data.toJson(),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return response.data['id'];
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<dynamic> updateDraftPLan(PlanModel data) async {
    try {
      final response = await _restDio.put(
        WealthPathApi.updateDraftPlan(copierId: data.id!),
        data: data.toJson(),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return response.data['id'];
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<dynamic> updateBrokerNo({
    required int copierId,
    required String brokerNo,
  }) async {
    try {
      final response = await _restDio.put(
        WealthPathApi.updateBrokerNo(copierId: copierId),
        data: {'brokerNo': brokerNo},
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<BEBaseResponse> activePlan({required int copierId}) async {
    try {
      final response = await _restDio.put(
        WealthPathApi.activePlan(copierId: copierId),
        options: Options(headers: StockUtils.orderHeaders),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<BEBaseResponse> deletePlan({required int copierId}) async {
    try {
      final response = await _restDio.put(
        WealthPathApi.deletePlan(copierId: copierId),
        options: Options(headers: StockUtils.orderHeaders),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<BEBaseResponse> stopInvestment({required int copierId}) async {
    try {
      final response = await _restDio.put(
        WealthPathApi.stopInvestment(copierId: copierId),
        options: Options(headers: StockUtils.orderHeaders),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<BEBaseResponse> activeInvestment({required int copierId}) async {
    try {
      final response = await _restDio.put(
        WealthPathApi.activeInvestment(copierId: copierId),
        options: Options(headers: StockUtils.orderHeaders),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<List<ItemData>> getListStockOfWealth() async {
    try {
      final response = await _restDio.get(WealthPathApi.listStockOfWealth);
      final result = BEBaseResponse.fromJson(response.data);
      List<ItemData> listStock = [];

      if (result.isSuccess()) {
        if (result.data != null) {
          result.data.forEach((e) {
            listStock.add(MpItemData.fromJson(e).mapResponseToItemData());
          });
          return listStock;
        } else {
          return [];
        }
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<String> previewContract(PlanModel data) async {
    try {
      final response = await _restDio.post(
        WealthPathApi.previewContract(copierId: data.id ?? -1),
        data: data.toJson(),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result.data['request-id'];
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<String> previewRegistrationContract(PlanModel data) async {
    try {
      final response = await _restDio.post(
        WealthPathApi.previewRegistrationContract(copierId: data.id ?? -1),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result.data['request-id'] ?? '';
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<String> previewModifiedContract(PlanModel data) async {
    try {
      final response = await _restDio.post(
        WealthPathApi.previewModifiedContract(copierId: data.id ?? -1),
        data: data.previewModifiedContract(),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result.data['request-id'];
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<String> previewContractStopInvestment(int copierId) async {
    try {
      final response = await _restDio.post(
        WealthPathApi.previewContractStopInvestment(copierId: copierId),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result.data['request-id'];
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<String> previewContractContinueInvestment(int copierId) async {
    try {
      final response = await _restDio.post(
        WealthPathApi.previewContractContinueInvestment(copierId: copierId),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result.data['request-id'];
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<WealthContractModel> getContractByRequestId(String requestId) async {
    try {
      final response = await _restDio.get(
        WealthPathApi.getFileContract,
        queryParameters: {'requestId': requestId},
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return WealthContractModel.fromJson(result.data);
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<DataListResponse<List<PlanModel>>> getListPlan(
    FetchListPlanRequest request,
  ) async {
    try {
      final response = await _restDio.get(
        WealthPathApi.createPlan,
        queryParameters: request.toJson(),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        var dataResponse = DataListResponse<List<PlanModel>>.fromJson(
          response.data,
          (json) => List<PlanModel>.from(
            (json as List)
                .map((e) => PlanEntity.fromJson(e).mapResponseToPlanModel())
                .toList(),
          ),
        );
        return dataResponse;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<DataListResponse<List<CommandHistoryModel>>> getListCommandHistory(
    FetchListCommandHistoryRequest request,
  ) async {
    try {
      final response = await _restDio.get(
        WealthPathApi.commandHistory,
        queryParameters: request.toJson(),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        var dataResponse = DataListResponse<List<CommandHistoryModel>>.fromJson(
          response.data,
          (json) => List<CommandHistoryModel>.from(
            (json as List)
                .map(
                  (e) =>
                      CommandHistoryEntity.fromJson(
                        e,
                      ).mapResponseToCommandHistoryModel(),
                )
                .toList(),
          ),
        );
        return dataResponse;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<BEBaseResponse> cancelOrder({
    required int copierId,
    required String transactionId,
  }) async {
    try {
      final response = await _restDio.post(
        WealthPathApi.cancelOrder(copierId: copierId),
        data: {"transactionId": transactionId},
        options: Options(headers: StockUtils.orderHeaders),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<DataListResponse<List<InvestmentCommandModel>>>
  getListInvestmentCommand(FetchListInvestmentCommandRequest request) async {
    try {
      final response = await _restDio.get(
        WealthPathApi.investmentCommand,
        queryParameters: request.toJson(),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        var dataResponse =
            DataListResponse<List<InvestmentCommandModel>>.fromJson(
              response.data,
              (json) => List<InvestmentCommandModel>.from(
                (json as List)
                    .map(
                      (e) =>
                          InvestmentCommandEntity.fromJson(
                            e,
                          ).mapResponseToInvestmentCommandModel(),
                    )
                    .toList(),
              ),
            );
        return dataResponse;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<BEBaseResponse> acceptCommand({required int id}) async {
    try {
      final deviceName = await AppUtils.getDeviceName();
      final ip = await AppUtils.getIp();
      final deviceID = await AppUtils.getDeviceId();

      final response = await _restDio.put(
        WealthPathApi.acceptCommand(id: id),
        data: {
          'device_name': deviceName,
          'device_id': deviceID,
          'device_type': Platform.isIOS ? 'IOS' : 'Android',
          'ip': ip,
        },
        options: Options(headers: StockUtils.orderHeaders),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<BEBaseResponse> checkChangePlan(PlanModel data) async {
    try {
      final response = await _restDio.put(
        WealthPathApi.checkChangePlan(copierId: data.id!),
        data: data.toJsonCheckChangePlan(),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<BEBaseResponse> updateChangePlan(PlanModel data) async {
    try {
      final response = await _restDio.put(
        WealthPathApi.updateChangePlan(copierId: data.id!),
        data: data.toJsonUpdatePlan(),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<PlanModel> getDetailPlan(int copierId) async {
    try {
      final response = await _restDio.get(
        WealthPathApi.updateChangePlan(copierId: copierId),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return PlanEntity.fromJson(result.data).mapResponseToPlanModel();
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<NavModel?> getValueNAV() async {
    try {
      final response = await _restDio.get(WealthPathApi.valueNAV);
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return NavModel.fromJson(result.data);
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<List<ItemAssetsModel>> getListAssetsHeld() async {
    try {
      final response = await _restDio.get(WealthPathApi.assetsHeld);
      final result = BEBaseResponse.fromJson(response.data);
      List<ItemAssetsModel> listAssetsHeld = [];

      if (result.isSuccess()) {
        if (result.data != null) {
          result.data.forEach((e) {
            listAssetsHeld.add(ItemAssetsModel.fromJson(e));
          });
          return listAssetsHeld;
        } else {
          return [];
        }
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<ItemAssetsModel?> detailAssetsHeld(int copierId) async {
    try {
      final response = await _restDio.get(
        WealthPathApi.assetsHeld,
        queryParameters: {'copierId': copierId},
      );
      final result = BEBaseResponse.fromJson(response.data);
      List<ItemAssetsModel> listAssetsHeld = [];

      if (result.isSuccess()) {
        if (result.data != null) {
          result.data.forEach((e) {
            listAssetsHeld.add(ItemAssetsModel.fromJson(e));
          });
          return listAssetsHeld.first;
        } else {
          return null;
        }
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<List<StockDetailEntity>> getStockBySymbols(
    List<String> symbols,
  ) async {
    List<StockDetailEntity> list = [];
    if (symbols.isEmpty) return list;

    try {
      Response response = await _investDio.get(
        WealthPathApi.stockBySymbols(symbols.join(',')),
      );
      final value = BEBaseResponse.fromJson(response.data);
      if (value.isSuccess() && value.data != null) {
        list = transformListStockEntities(value.data["symbols"]);
      } else if (value.code == 'IVBERR02') {
        //không có dữ liệu
        list = [];
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (e) {
      throw HandleError.from(e);
    }
    return list;
  }

  @override
  Future<List<AccountModel>> getInfoAccount() async {
    try {
      final response = await _restDio.get(WealthPathApi.getInfoAccount());
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        List<dynamic> dataList = result.data as List<dynamic>;
        List<AccountModel> lstAccountModel =
            dataList.map((data) => AccountModel.fromJson(data)).toList();
        return lstAccountModel;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<CashInfo> cashDetail({required String copierId}) async {
    try {
      final response = await _restDio.get(
        WealthPathApi.cashDetail(copierId: copierId),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return CashInfo.fromJson(result.data['cashInfo']);
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<DataListResponse<List<Transaction>>> cashTransferHistory({
    required HistoriesRequestModel historiesRequestModel,
  }) async {
    try {
      final response = await _restDio.get(
        WealthPathApi.cashTranferHistory(
          historiesRequestModel: historiesRequestModel,
        ),
        queryParameters: historiesRequestModel.toJson(),
      );
      final result = BEBaseResponse.fromJson(response.data);

      if (result.isSuccess()) {
        var dataResponse = DataListResponse<List<Transaction>>.fromJson(
          response.data,
          (json) => List<Transaction>.from(
            (json as List).map((e) => Transaction.fromJson(e)).toList(),
          ),
        );
        return dataResponse;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<List<MoneyStatementModel>> getCashStatementHist({
    required String subAccountId,
    required String accountId,
    required String fromDate,
    required String toDate,
  }) async {
    try {
      final response = await _restDio.get(
        WealthPathApi.getCashStatementHist(
          idSubAccount: subAccountId,
          accountId: accountId,
          fromDate: fromDate,
          toDate: toDate,
        ),
      );

      final result = AppBaseResponse.fromJson(response.data);

      if (result.isSuccess() && result.data is List) {
        return (result.data as List)
            .map((e) => MoneyStatementModel.fromJson(e))
            .toList();
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BEBaseResponse> depositMoney({
    required int copierId,
    required num amount,
    required String normalAccount,
  }) async {
    try {
      final deviceName = await AppUtils.getDeviceName();
      final ip = await AppUtils.getIp();
      final deviceID = await AppUtils.getDeviceId();

      final response = await _restDio.post(
        WealthPathApi.depositMoney(copierId: copierId),
        data: {
          'amount': amount,
          'normalAccount': normalAccount,
          "device_name": deviceName,
          "device_type": Platform.isIOS ? 'IOS' : 'Android',
          "device_id": deviceID,
          "ip": ip,
        },
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<BEBaseResponse> withdrawMoney({
    required int copierId,
    required num amount,
    required String normalAccount,
  }) async {
    try {
      final deviceName = await AppUtils.getDeviceName();
      final ip = await AppUtils.getIp();
      final deviceID = await AppUtils.getDeviceId();

      final response = await _restDio.post(
        WealthPathApi.withdrawMoney(copierId: copierId),
        data: {
          'amount': amount,
          'normalAccount': normalAccount,
          "device_name": deviceName,
          "device_type": Platform.isIOS ? 'IOS' : 'Android',
          "device_id": deviceID,
          "ip": ip,
        },
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<BEBaseResponse> proactiveBuyStock(
    OrderForWealthRequest request,
  ) async {
    try {
      final deviceName = await AppUtils.getDeviceName();
      final ip = await AppUtils.getIp();
      final deviceID = await AppUtils.getDeviceId();

      final response = await _restDio.post(
        WealthPathApi.proactiveBuyStock(copierId: request.copierId),
        data: request.toJson(
          deviceName: deviceName,
          deviceID: deviceID,
          ip: ip,
        ),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<BEBaseResponse> proactiveSellStock(
    OrderForWealthRequest request,
  ) async {
    try {
      final deviceName = await AppUtils.getDeviceName();
      final ip = await AppUtils.getIp();
      final deviceID = await AppUtils.getDeviceId();

      final response = await _restDio.post(
        WealthPathApi.proactiveSellStock(copierId: request.copierId),
        data: request.toJson(
          deviceName: deviceName,
          deviceID: deviceID,
          ip: ip,
        ),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<InvestmentCommandModel> detailAcceptCommand(int id) async {
    try {
      final response = await _restDio.get(
        WealthPathApi.detailAcceptCommand(id: id),
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return InvestmentCommandEntity.fromJson(
          result.data,
        ).mapResponseToInvestmentCommandModel();
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<int> orderSummary({
    required int copierId,
    required num totalInvest,
  }) async {
    try {
      final response = await _restDio.post(
        WealthPathApi.orderSummary(copierId: copierId),
        data: {'totalInvest': totalInvest},
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return result.data['id'];
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<RightModel> getRightOffList({required int copierId}) async {
    try {
      final response = await _restDio.get(
        WealthPathApi.stockRights,
        queryParameters: {'copierId': copierId},
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        return RightModel.fromJson(result.data);
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<ExpectedPerformanceModel?> calculateExpectedPerformance(
    PlanModel data,
  ) async {
    try {
      final response = await _restDio.post(
        WealthPathApi.expectedPerformance,
        data: data.toJsonCalculateExpectedPerformance(),
      );
      final result = BEBaseResponse.fromJson(response.data);
      ExpectedPerformanceModel expectedPerformanceModel;

      if (result.isSuccess()) {
        if (result.data != null) {
          expectedPerformanceModel = ExpectedPerformanceModel.fromJson(
            result.data,
          );
          return expectedPerformanceModel;
        } else {
          return null;
        }
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<DataListResponse<List<InvestmentOrderModel>>> getListInvestmentOrder(
    int page,
    int pageSize,
    String? status,
    String? investType,
    String? transactionKind,
    String? fromDate,
    String? toDate,
  ) async {
    try {
      final response = await _restDio.get(
        WealthPathApi.getInvestmentOrders(
          page,
          pageSize,
          status,
          investType,
          transactionKind,
          fromDate,
          toDate,
        ),
      );

      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        var dataResponse =
            DataListResponse<List<InvestmentOrderModel>>.fromJson(
              response.data,
              (json) => List<InvestmentOrderModel>.from(
                (json as List)
                    .map(
                      (e) =>
                          InvestmentOrderEntity.fromJson(
                            e,
                          ).mapResponseToInvestmentOrderModel(),
                    )
                    .toList(),
              ),
            );
        return dataResponse;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<DataListResponse<List<AcceptCommandDataModel>>> getDetailAcceptCommand(
    String type,
    int id,
    String status,
  ) async {
    try {
      final response = await _restDio.get(
        WealthPathApi.getDetailAcceptCommand(
          type: type,
          copierId: id,
          status: status,
        ),
      );

      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        var dataResponse =
            DataListResponse<List<AcceptCommandDataModel>>.fromJson(
              response.data,
              (json) => List<AcceptCommandDataModel>.from(
                (json as List)
                    .map(
                      (e) =>
                          AcceptCommandDataEntity.fromJson(
                            e,
                          ).mapResponseToAcceptCommandDataModel(),
                    )
                    .toList(),
              ),
            );
        return dataResponse;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<Map<String, List<ChartPriceEntity>>> getPriceCharts(
    String symbols, {
    int frequent = 5,
  }) async {
    try {
      final Response<dynamic> response = await _investDio.get(
        '${StockCommonApi.priceChartsFullTime}/${frequent}min',
        queryParameters: {'symbolList': symbols},
      );
      final data = await compute(parseData, {symbols: response.data});
      return data;
    } catch (e, stackTrace) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<List<ChartPriceEntity>> getChartDetail({
    required String symbol,
    required String chartType,
  }) async {
    try {
      final response = await _investDio.get(
        StockCommonApi.chartDetail,
        queryParameters: {'symbol': symbol, 'chartType': chartType},
      );

      final data = StockPriceChartModel.fromJson(response.data);
      return data.priceHistory!.map((e) => e.toChartPriceEntity()).toList();
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<List<SecuritiesPortfolioResponsesModel>> getSecuritiesPortfolio(
    String accountId,
  ) async {
    List<SecuritiesPortfolioResponsesModel> list = [];
    try {
      Response response = await _flexDio.get(
        MainStockPathApi.securitiesPortfolio(accountId),
      );
      final result = AppBaseResponse.fromJson(response.data);
      if (result.isSuccess() && result.data is List) {
        for (var jsonObj in result.data) {
          list.add(SecuritiesPortfolioResponsesModel.fromJson(jsonObj));
        }
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
    return list;
  }

  @override
  Future<List<StockDetailEntity>> getStocks(String symbols) async {
    try {
      final response = await _investDio.get(
        MainStockPathApi.instruments,
        queryParameters: {'symbols': symbols},
      );

      final beResponse = BEBaseResponse.fromJson(response.data);

      if (beResponse.isSuccess() && beResponse.data != null) {
        return transformListStockEntities((beResponse.data as Map)['symbols']);
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<List<StockOrderEntity>> getAllStockAtSplash() async {
    final newDio = Dio(_investDio.options);

    return _getAllStock(newDio);
  }

  Future<List<StockOrderEntity>> _getAllStock(Dio dio) async {
    List<StockOrderEntity> listStock = [];

    try {
      final response = await dio.get(
        StockCommonApi.stockByIndex,
        queryParameters: {'indexCode': 'ALLSTOCK,CW,ETF,BOND,FUVN30,FUGB'},
      );

      final result = BEBaseResponse.fromJson(response.data);

      if (result.isSuccess()) {
        listStock = await compute(transformListStockOrderEntities, result.data);
        listStock.sort((a, b) => (a.symbol).compareTo(b.symbol));
      } else {
        throw ResponseError.fromDioResponse(response);
      }
      return listStock;
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  // Helper function for parseData compute
  static Future<Map<String, List<ChartPriceEntity>>> parseData(
    Map<String, dynamic> map,
  ) async {
    final symbols = map.keys.first;
    final data = map.values.first;
    Map<String, List<ChartPriceEntity>> mapData = {};
    List<String> symbolList = symbols.split(',');

    for (var symbol in symbolList) {
      if (data[symbol] != null) {
        mapData[symbol] = transformChartPriceEntity(data[symbol]);
      }
    }
    return mapData;
  }

  @override
  Future<BrokerModel> getBrokerInfo(String accountNo) async {
    BrokerModel model = BrokerModel();
    try {
      Map<String, dynamic> param = {'accountNo': accountNo};
      Response response = await _restDio.get(
        '/iam/external/users/brokers',
        queryParameters: param,
      );
      final result = BEBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        if (result.data is List && result.data.isNotEmpty) {
          model = BrokerModel.fromJson(result.data[0]);
        }
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
    return model;
  }
}
