import 'dart:io';

import 'package:collection/collection.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_log_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/stock_order_entity.dart';
import 'package:vp_wealth/presentation/wealths/socket/investment_tool_socket_connect.dart';
import 'package:vp_wealth/presentation/wealths/socket/socket_account_connect.dart';

class WealthData {
  static final WealthData _singleton = WealthData._internal();

  factory WealthData() => _singleton;

  WealthData._internal();

  // List<MappingModel> _mapping = [];

  // List<MappingModel> get mapping => _mapping;

  List<StockOrderEntity> _allStocks = [];

  // List<StockExRightEntity> _stocksExRight = [];

  List<StockOrderEntity> get allStocks => _allStocks;

  List<ItemData> _allStocksWealth = [];

  // List<StockOrderEntity> get onlyStocks =>
  //     _allStocks.where((e) => e.searchType.isStock).toList();

  // List<StockOrderEntity> get onlyDerivative =>
  //     _allStocks.where((e) => e.searchType.isDerivative).toList();

  // List<BondModel> _allBonds = [];

  // List<BondModel> get allBonds => _allBonds;

  List<ItemData> get allStocksWealth => _allStocksWealth;

  // List<CategoryModel> _category = [];

  // List<CategoryModel> get category => _category;

  String deviceName = 'Iphone 12';

  String _agencyBondCode = '';

  String get agencyBondCode => _agencyBondCode;

  void setAgencyBondCode(String newAgencyBondCode) {
    _agencyBondCode = newAgencyBondCode;
  }

  // void setMapping(List<MappingModel> data) {
  //   _mapping = data;
  // }

  // void setAllStocks(List<StockOrderEntity> data) {
  //   _allStocks = data;
  // }

  // void setAllStocksWealth(List<ItemData> data) {
  //   _allStocksWealth = data;
  // }

  // void setStocksExRight(List<StockExRightEntity> data) {
  //   _stocksExRight = data;
  // }

  // void setAllBondService(List<BondModel> data) {
  //   _allBonds = data;
  // }

  String? findCompanyNameFromSymbol(String? symbol) {
    return allStocks
            .firstWhereOrNull((stock) => stock.symbol == symbol)
            ?.fullName ??
        '';
  }

  // Exchange? getExchangeNameBySymbol(String? symbol) {
  //   //   return allStocks
  //   //       .firstWhereOrNull((stock) => stock.symbol == symbol)
  //   //       ?.exchange;
  //   // }

  //   // void setCategory(List<CategoryModel> data) {
  //   //   _category = data;
  //   // }

  //   Future getAllStock({bool throwE = false}) async {
  //     if (allStocks.isNotEmpty) return;

  //     try {
  //       _allStocks =
  //           await GetIt.instance.get<WealthRepository>().getAllStockSymbol();
  //     } catch (e, stackTrace) {
  //       debugPrintStack(stackTrace: stackTrace);
  //       if (throwE) rethrow;
  //     }
  //   }

  Future getAllStockWealth({bool throwE = false}) async {
    if (allStocksWealth.isNotEmpty) return;

    try {
      _allStocksWealth =
          await GetIt.instance.get<WealthRepository>().getListStockOfWealth();
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      if (throwE) rethrow;
    }
  }

  Future<void> setDeviceName() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        deviceName = androidInfo.model ?? deviceName;
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        deviceName = iosInfo.utsname.machine ?? deviceName;
      }
    } catch (e) {
      dlog(e);
    }
  }

  /*---- Xu ly clear du lieu, token, socket khi logout  ----*/
  Future clear() async {
    ISocketConnect().destroy();
    SocketAccountConnect().destroy();
    // await apiLogout();
    // Session().resetSession();
    // AppCache().clear();
    // DerivativeSession().resetSession();
    // WatchListManager.instance.clear();
  }

  /// Call API logout
  // Future apiLogout() {
  //   return sl
  //       .get<NotificationRepository>()
  //       .cancellationFCM()
  //       .catchError((e, stackTrace) => debugPrintStack(stackTrace: stackTrace));
  // }

  // bool checkIsExRightStock(String symbol) =>
  //     _stocksExRight.firstWhereOrNull((element) => element.symbol == symbol) !=
  //     null;
}
