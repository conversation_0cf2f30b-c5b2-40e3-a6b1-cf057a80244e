import 'package:flutter/material.dart';
import 'package:vp_centralize/screen/smart_otp/settings_smart_otp/smart_otp_register_helper/smart_otp_helper.dart';
import 'package:vp_core/vp_core.dart';

class GetOffsetSyncTimeScreen extends StatefulWidget {
  const GetOffsetSyncTimeScreen({super.key});

  @override
  State<GetOffsetSyncTimeScreen> createState() =>
      _GetOffsetSyncTimeScreenState();
}

class _GetOffsetSyncTimeScreenState extends State<GetOffsetSyncTimeScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      context.pop(SmartOTPHelper().offsetSyncTime);
    });
  }

  @override
  Widget build(BuildContext context) {
    return const SizedBox.shrink();
  }
}
