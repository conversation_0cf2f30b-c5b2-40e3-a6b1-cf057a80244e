import 'package:flutter/material.dart';
import 'package:tuple/tuple.dart';
import 'package:vp_centralize/core/repository/centralize_repository.dart';
import 'package:vp_centralize/core/repository/centralize_repository_impl.dart';
import 'package:vp_centralize/core/repository/smart_otp_repository.dart';
import 'package:vp_centralize/core/service/centralize_service.dart';
import 'package:vp_centralize/core/service/smart_otp_service.dart';
import 'package:vp_centralize/interceptor/fss_centralize_interceptor.dart';
import 'package:vp_centralize/interceptor/iam_centralize_interceptor.dart';
import 'package:vp_centralize/model/params/get_smart_otp_params.dart';
import 'package:vp_centralize/model/params/get_sms_otp_params.dart';
import 'package:vp_centralize/model/params/pin_params.dart';
import 'package:vp_centralize/router/centralize_router.dart';
import 'package:vp_centralize/screen/get_offset_sync_time/get_offset_sync_time_screen.dart';
import 'package:vp_centralize/screen/info_smart_otp/get_info_smart_otp_screen.dart';
import 'package:vp_centralize/screen/pin/pin_page.dart';
import 'package:vp_centralize/screen/smart_otp/change_pin_smart_otp/change_pin_smart_otp_page.dart';
import 'package:vp_centralize/screen/smart_otp/forgot_pin_smart_otp/forgot_pin_smart_otp_page.dart';
import 'package:vp_centralize/screen/smart_otp/get_smart_otp/get_smart_otp.dart';
import 'package:vp_centralize/screen/smart_otp/main_smart_otp/main_smart_otp.dart';
import 'package:vp_centralize/screen/smart_otp/register_smart_otp/register_smart_otp_input_pin/register_smart_input_pin_page.dart';
import 'package:vp_centralize/screen/smart_otp/register_smart_otp/register_smart_otp_splash/register_smart_otp_splash_view.dart';
import 'package:vp_centralize/screen/smart_otp/register_smart_otp/register_smart_otp_success_page.dart';
import 'package:vp_centralize/screen/sms/sms_page.dart';
import 'package:vp_core/vp_core.dart';

import 'screen/verify_otp_contact_email/verify_otp_contact_email.dart';

class VpCentralizeModule implements Module {
  @override
  void injectServices(GetIt service) {
    service<Dio>().interceptors.add(IAMCentralizeInterceptor());
    service<Dio>().interceptors.add(FSSCentralizeInterceptor());
    service.registerLazySingleton(() => CentralizeService(service()));
    service.registerLazySingleton<CentralizeRepository>(
      () => CentralizeRepositoryImpl(centralizeService: service()),
    );
    service.registerLazySingleton(() => SmartOtpService(service()));
    service.registerLazySingleton<SmartOtpRepository>(
      () => SmartOtpRepositoryImpl(smartOtpService: service()),
    );
  }

  @override
  List<RouteBase> router() {
    return [
      GoRoute(
        path: CentralizeRouter.pin.routeName,
        builder: (BuildContext context, GoRouterState state) {
          return PinPage(params: state.extra as GetPinParams?);
        },
        pageBuilder:
            (context, state) => MaterialPage(
              key: state.pageKey,
              fullscreenDialog: true,
              child: PinPage(params: state.extra as GetPinParams?),
            ),
      ),
      GoRoute(
        path: CentralizeRouter.sms.routeName,
        builder: (BuildContext context, GoRouterState state) {
          return SmsPage(params: state.extra as GetSmsOTPParams?);
        },
        pageBuilder:
            (context, state) => MaterialPage(
              key: state.pageKey,
              fullscreenDialog: true,
              child: SmsPage(params: state.extra as GetSmsOTPParams?),
            ),
      ),
      GoRoute(
        path: CentralizeRouter.smartOTP.routeName,
        builder: (BuildContext context, GoRouterState state) {
          if (state.extra is String) {
            return GetSmartOtpPage(
              params: GetSmartOTPParams(accountNo: state.extra as String),
            );
          }
          return GetSmartOtpPage(params: state.extra as GetSmartOTPParams?);
        },
      ),
      GoRoute(
        path: CentralizeRouter.mainSmartOTP.routeName,
        builder: (BuildContext context, GoRouterState state) {
          return const MainSmartOTP();
        },
      ),
      GoRoute(
        path: CentralizeRouter.changePinSmartOTP.routeName,
        builder: (BuildContext context, GoRouterState state) {
          return const ChangePinSmartOTPPage();
        },
      ),
      GoRoute(
        path: CentralizeRouter.getInfoSmartOTP.routeName,
        pageBuilder:
            (context, state) => NoTransitionPage<void>(
              key: state.pageKey,
              child: GetInfoSmartOtpScreen(accountNo: state.extra as String),
            ),
      ),
      GoRoute(
        path: CentralizeRouter.getOffsetSyncTime.routeName,
        pageBuilder:
            (context, state) => NoTransitionPage<void>(
              key: state.pageKey,
              child: GetOffsetSyncTimeScreen(),
            ),
      ),
      GoRoute(
        path: CentralizeRouter.registerSmartOTPInputPin.routeName,
        builder: (BuildContext context, GoRouterState state) {
          return RegisterSmartInputPinPage(
            tuple2: state.extra as Tuple2<String, String>,
          );
        },
      ),
      GoRoute(
        path: CentralizeRouter.settingRegisterSmartOTPResult.routeName,
        builder: (BuildContext context, GoRouterState state) {
          return const RegisterSmartOTPSuccessPage();
        },
      ),
      GoRoute(
        path: CentralizeRouter.registerSmartOTPSplash.routeName,
        builder: (BuildContext context, GoRouterState state) {
          final tuple2 = state.extra as Tuple2<bool, String?>;
          final isRegistered = tuple2.item1;
          final deviceNameRegistered = tuple2.item2;

          return RegisterSmartOtpSplashPage(
            isRegistered: isRegistered,
            deviceNameRegistered: deviceNameRegistered,
          );
        },
      ),
      GoRoute(
        path: CentralizeRouter.forgotPinSmartOTP.routeName,
        builder: (BuildContext context, GoRouterState state) {
          return ForgotPinSmartOTP(fromRoute: state.extra as String);
        },
        pageBuilder:
            (context, state) => CustomTransitionPage(
              key: state.pageKey,
              opaque: false,
              child: ForgotPinSmartOTP(fromRoute: state.extra as String),
              transitionsBuilder: (
                context,
                animation,
                secondaryAnimation,
                child,
              ) {
                return child;
              },
            ),
      ),
      GoRoute(
        path: CentralizeRouter.verifyOtpContactEmail.routeName,
        builder:
            (BuildContext context, GoRouterState state) =>
                VerifyOtpContactEmail(email: state.extra as String),
      ),
    ];
  }

  @override
  List<LocalizationsDelegate> localizationsDelegates() {
    return [];
  }

  @override
  String modulePath() {
    return "vp_centralize";
  }
}
