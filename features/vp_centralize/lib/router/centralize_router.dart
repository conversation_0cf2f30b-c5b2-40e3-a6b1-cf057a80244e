enum CentralizeRouter {
  pin('/pin'),
  smartOTP('/smartOTP'),
  mainSmartOTP('/mainSmartOTP'),
  changePinSmartOTP('/changePinSmartOTP'),
  forgotPinSmartOTP('/forgotPinSmartOTP'),
  registerSmartOTPInputPin('/registerSmartOTPInputPin'),
  registerSmartOTPSplash('/registerSmartOTPSplash'),
  settingRegisterSmartOTPResult('/settingRegisterSmartOTPResult'),
  sms('/sms'),
  verifyOtpContactEmail('/verifyOtpContactEmail'),
  getInfoSmartOTP('/getInfoSmartOTP'),
  getOffsetSyncTime('/getOffsetSyncTime'),
  verifyEmailSuccessPage('/verifyEmailSuccessPage');

  final String routeName;

  const CentralizeRouter(this.routeName);
}
