import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

void showNoDerivativeAccountDialog({
  required BuildContext context,
  required VoidCallback onRegister,
}) {
  showDialog(
    context: context,
    useRootNavigator: true,
    barrierDismissible: false,
    builder: (_) {
      return NoDerivativeAccountDialog(
        title: 'Thông báo',
        content:
            'Quý khách chưa có tài khoản phái sinh. Mở tài khoản ngay để trải nghiệm giao dịch nhanh chóng cùng VPBankS.',
        asset: Assets.icons.icDerivativeNoAccount.path,
        action: [
          Expanded(
            child: VpsButton.custom(
              onPressed: () => context.pop(),
              buttonType: ButtonType.secondary,
              title: '<PERSON>óng',
            ),
          ),

          SizedBox(width: 8),
          Expanded(
            child: VpsButton.custom(
              onPressed: () {
                context.pop();
                onRegister.call();
              },
              buttonType: ButtonType.primary,
              title: '<PERSON>ăng ký',
            ),
          ),
        ],
      );
    },
  );
}

class NoDerivativeAccountDialog extends StatelessWidget {
  final String title;
  final String content;
  final String asset;
  final List<Widget> action;

  const NoDerivativeAccountDialog({
    super.key,
    required this.asset,
    required this.title,
    required this.content,
    required this.action,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.all(12),
      backgroundColor: themeData.bgPopup,
      elevation: 2,
      alignment: Alignment.center,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 32),
            SvgPicture.asset(asset, package: Assets.package),
            SizedBox(height: 24),
            Text(
              title,
              style: vpTextStyle.headineBold6.copyColor(vpColor.textPrimary),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              content,
              style: vpTextStyle.body16.copyColor(vpColor.textSecondary),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            Row(children: action),
            SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}
