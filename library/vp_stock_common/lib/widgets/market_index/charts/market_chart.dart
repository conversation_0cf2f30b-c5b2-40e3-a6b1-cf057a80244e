import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/widgets/intraday_chart/widgets/intraday_chart_tooltip_view.dart';

import 'market_chart_cubit.dart';

class MarketChart extends StatelessWidget {
  const MarketChart({
    super.key,
    required this.indexCode,
    required this.chartData,
    required this.referencePrice,
    this.xAxisInterval,
    this.xAxisVisible = false,
    this.showVolume = false,
    this.primaryYAxisVisible = false,
    this.primaryXAxisFormatter,
  });

  final List<ChartData> chartData;

  final IndexCode indexCode;

  final double referencePrice;

  final bool showVolume;

  final bool primaryYAxisVisible;

  final bool xAxisVisible;

  final double? xAxisInterval;

  final String Function(num value)? primaryXAxisFormatter;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<MarketChartBloc>(
      create:
          (_) => MarketChartBloc(
            chartData: chartData,
            indexCode: indexCode,
            refPrice: referencePrice,
          )..init(),
      child: BlocBuilder<MarketChartBloc, MarketChartState>(
        builder: (context, state) {
          return IntradayChartView(
            minY: -20,
            maxY: 120,
            xAxisInterval: xAxisInterval,
            xAxisVisible: xAxisVisible,
            primaryXAxisFormatter: primaryXAxisFormatter,
            minutesPerPoint: 1,
            showVolume: showVolume,
            primaryYAxisVisible: primaryYAxisVisible,
            chartData: state.chartData,
            refPrice: referencePrice,
            tooltipBuilder:
                (data) => IntradayChartMarketTooltipView(
                  data: data,
                  referencePrice: referencePrice,
                ),
            primaryYAxisFormatter: (value) => value.getIndexFormatted(),
          );
        },
      ),
    );
  }
}
