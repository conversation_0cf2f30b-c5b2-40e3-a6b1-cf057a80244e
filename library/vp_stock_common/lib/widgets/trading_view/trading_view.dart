import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/model/enum/trading_view_interval.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/vp_stock_common_navigator.dart';
import 'package:vp_stock_common/widgets/trading_view/trading_view_cubit.dart';

class VPTradingView extends StatelessWidget {
  const VPTradingView({
    required this.symbol,
    this.intervals = const [
      TradingViewInterval.oneDay,
      TradingViewInterval.fourHour,
      TradingViewInterval.oneHour,
      TradingViewInterval.thirtyMin,
      TradingViewInterval.fifteenMin,
      TradingViewInterval.fiveMin,
    ],
    this.showAction = true,
    this.actions,
    this.marketCode,
    this.initialInterval = TradingViewInterval.oneDay,
    this.actionPadding = const EdgeInsets.fromLTRB(16, 0, 5, 0),
    this.padding = const EdgeInsets.fromLTRB(16, 8, 5, 0),
    super.key,
  });

  final String symbol;

  final EdgeInsets padding;

  final EdgeInsets actionPadding;

  final List<Widget>? actions;

  final MarketCode? marketCode;

  final TradingViewInterval initialInterval;

  final List<TradingViewInterval> intervals;

  final bool showAction;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<TradingViewCubit>(
      create:
          (context) => TradingViewCubit(
            symbol: symbol,
            marketCode: marketCode,
            userId: context.read<AuthCubit>().userInfo?.userinfo?.custodycd,
          ),
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: vpColor.backgroundElevation0,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Padding(
              padding: padding,
              child: SizedBox(
                height: 230,
                child: BlocBuilder<TradingViewCubit, TradingViewState>(
                  builder: (context, state) {
                    return VPWebView(
                      url: state.url,
                      gestureRecognizers:
                          <Factory<OneSequenceGestureRecognizer>>{
                            Factory<OneSequenceGestureRecognizer>(
                              () => EagerGestureRecognizer(),
                            ),
                          },
                      loadingBuilder: (_) {
                        return CircularProgressIndicator(
                          color: const Color(0xFF0059FF),
                          backgroundColor: context.colors.strokeNormal,
                        );
                      },
                      cookie:
                          state.accessToken != null
                              ? (
                                name: 'neo-access-token',
                                value: state.accessToken!,
                                domain: state.url,
                                path: '/',
                              )
                              : null,
                      backgroundColor: vpColor.backgroundElevation0,
                    );
                  },
                ),
              ),
            ),

            if (showAction)
              Padding(
                padding: actionPadding,
                child: _ChartIntervalView(symbol: symbol, actions: actions),
              ),
          ],
        ),
      ),
    );
  }
}

class _ChartIntervalView extends StatelessWidget {
  const _ChartIntervalView({required this.symbol, this.actions});

  final String symbol;

  final List<Widget>? actions;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: BlocSelector<
              TradingViewCubit,
              TradingViewState,
              TradingViewInterval
            >(
              selector: (state) => state.interval,
              builder: (context, period) {
                return Row(
                  children:
                      TradingViewInterval.values
                          .map(
                            (e) => Padding(
                              padding: const EdgeInsets.only(right: 8),
                              child: VPChipView.dynamic(
                                text: e.label,
                                size: ChipSize.small,
                                style:
                                    period == e
                                        ? ChipStyle.selected
                                        : ChipStyle.chipDefault,
                                onTap:
                                    () => context
                                        .read<TradingViewCubit>()
                                        .onPeriodChange(e),
                              ),
                            ),
                          )
                          .toList(),
                );
              },
            ),
          ),
        ),

        ...?actions,

        Builder(
          builder: (context) {
            return CircleIconView(
              onPressed:
                  () => stockCommonNavigator.openTradingViewFullScreen(
                    context,
                    args: TradingViewArgs(
                      symbol: symbol,
                      initialInterval:
                          context.read<TradingViewCubit>().state.interval,
                    ),
                  ),
              child: DesignAssets.icons.icZoom.svg(width: 16, height: 16),
            );
          },
        ),
      ],
    );
  }
}
