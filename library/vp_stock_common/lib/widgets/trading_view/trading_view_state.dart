part of 'trading_view_cubit.dart';

String _getTradingViewUrl({
  required String symbol,
  bool isFullScreen = false,
  AppTheme theme = AppTheme.light,
  String? interval,
  String? userId,
  MarketCode? marketCode,
}) {
  return 'https://neopro-sit.vpbanks.com.vn/tradingview/?symbol=$symbol&timescaleMarks=1&theme=${theme.name}&language=&interval=$interval&isLoggedIn=${userId != null ? 1 : 0}&autoSave=1${isFullScreen ? '&hideHeaderFullscreen=1' : ''}${marketCode != null ? '&marketCode=${marketCode.value}' : ''}';
}

class TradingViewState {
  TradingViewState({
    required this.symbol,
    required this.accessToken,
    this.interval = TradingViewInterval.oneDay,
    this.userId,
    this.marketCode,
  });

  final String symbol;

  final String? accessToken;

  final String? userId;

  final MarketCode? marketCode;

  final TradingViewInterval interval;

  String get url => _getTradingViewUrl(
    symbol: symbol,
    theme: isDark ? AppTheme.dark : AppTheme.light,
    isFullScreen: true,
    interval: interval.value,
    userId: userId,
    marketCode: marketCode,
  );

  TradingViewState copyWith({
    TradingViewInterval? interval,
    String? accessToken,
    MarketCode? marketCode,
  }) {
    return TradingViewState(
      symbol: symbol,
      accessToken: accessToken ?? this.accessToken,
      interval: interval ?? this.interval,
      marketCode: marketCode ?? this.marketCode,
    );
  }
}
