import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/model/enum/market_code.dart';
import 'package:vp_stock_common/model/enum/trading_view_interval.dart';

part 'trading_view_state.dart';

class TradingViewCubit extends AppCubit<TradingViewState> {
  TradingViewCubit({
    required String symbol,
    required String? userId,
    MarketCode? marketCode,
    TradingViewInterval interval = TradingViewInterval.oneDay,
  }) : super(
         TradingViewState(
           symbol: symbol,
           interval: interval,
           userId: userId,
           marketCode: marketCode,
           accessToken: SharedPref.getString(KeyShared.accessToken),
         ),
       );

  void onPeriodChange(TradingViewInterval interval) {
    emit(state.copyWith(interval: interval));
  }
}
