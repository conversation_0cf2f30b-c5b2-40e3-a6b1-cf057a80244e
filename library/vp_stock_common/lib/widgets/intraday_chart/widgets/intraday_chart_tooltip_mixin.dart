import 'dart:ui';

import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/stock_color_utils.dart';
import 'package:vp_stock_common/widgets/intraday_chart/widgets/intraday_chart_tooltip_view.dart';

mixin IntradayChartTooltipMixin on IntradayChartTooltip {
  String get time => data.time.toLocal().toHHmm();

  String get volumeValue =>
      FormatUtils.formatVol(data.volume, excludeZero: false);

  double get change => data.value - referencePrice;

  String? get changeValue =>
      FormatUtils.formatClosePrice(change, trimZero: false, showSign: false);

  String? get changePercent =>
      referencePrice != 0
          ? FormatUtils.formatPercent(
            change * 100 / referencePrice,
            showSign: false,
          )
          : '0%';

  String? get price =>
      FormatUtils.formatClosePrice(data.value, trimZero: false);

  Color get color => StockColorUtils.colorByPrice(
    referencePrice: referencePrice,
    currentPrice: data.value,
    ceilingPrice: ceilingPrice,
    floorPrice: floorPrice,
  );
}

mixin IntradayChartMarketTooltipMixin on IntradayChartTooltip {
  String get time => data.time.toLocal().toHHmm();

  String get volumeValue =>
      FormatUtils.formatVol(data.volume, excludeZero: false);

  double get change => data.value - referencePrice;

  String? get changeValue => FormatUtils.formatClosePrice(
    change,
    trimZero: false,
    showSign: true,
    showPositiveSign: false,
  );

  String? get changePercent =>
      referencePrice != 0
          ? FormatUtils.formatPercent(
            change * 100 / referencePrice,
            showSign: true,
            showPositiveSign: false,
          )
          : '0%';

  String? get price => data.value.toDouble().getIndexFormatted();

  Color get color => StockColorUtils.colorByPrice(
    referencePrice: referencePrice,
    currentPrice: data.value,
    ceilingPrice: ceilingPrice,
    floorPrice: floorPrice,
  );
}
