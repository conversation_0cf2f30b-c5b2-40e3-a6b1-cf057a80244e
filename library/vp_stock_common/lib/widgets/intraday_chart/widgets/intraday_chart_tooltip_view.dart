import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_shadow.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/widgets/intraday_chart/widgets/intraday_chart_tooltip_mixin.dart';
import 'package:vp_stock_common/widgets/intraday_chart/widgets/intraday_price_icon_view.dart';

abstract class IntradayChartTooltip extends StatelessWidget {
  const IntradayChartTooltip({
    required this.data,
    required this.referencePrice,
    this.ceilingPrice,
    this.floorPrice,
    super.key,
  });

  final ChartData data;

  final double referencePrice;

  final double? ceilingPrice;

  final double? floorPrice;
}

class IntradayChartMarketTooltipView extends IntradayChartTooltip
    with IntradayChartMarketTooltipMixin {
  const IntradayChartMarketTooltipView({
    required super.data,
    required super.referencePrice,
    super.ceilingPrice,
    super.floorPrice,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(12, 6, 12, 6),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: vpColor.backgroundElevation2,
        boxShadow: VPBoxShadow.shadowElevation1,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            time,
            style: vpTextStyle.captionRegular.copyColor(vpColor.textPrimary),
          ),
          Text(
            'Chỉ số: $price',
            style: vpTextStyle.subtitle14.copyColor(color),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IntradayPriceIconView(
                closePrice: data.value,
                referencePrice: referencePrice,
                ceilingPrice: ceilingPrice,
                floorPrice: floorPrice,
                iconSize: 12,
              ),

              const SizedBox(width: 2),

              Text(
                '$changeValue ($changePercent)',
                style: vpTextStyle.captionRegular.copyColor(color),
              ),
            ],
          ),

          const SizedBox(height: 4),

          Text(
            'Khối lượng: $volumeValue',
            style: vpTextStyle.captionRegular.copyColor(vpColor.textPrimary),
          ),
        ],
      ),
    );
  }
}

class IntradayChartTooltipView extends IntradayChartTooltip
    with IntradayChartTooltipMixin {
  const IntradayChartTooltipView({
    required super.data,
    required super.referencePrice,
    super.ceilingPrice,
    super.floorPrice,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(12, 6, 12, 6),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: vpColor.backgroundElevation2,
        boxShadow: VPBoxShadow.shadowElevation1,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            time,
            style: vpTextStyle.captionRegular.copyColor(vpColor.textPrimary),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Giá: $price',
                style: vpTextStyle.subtitle14.copyColor(color),
              ),

              const SizedBox(width: 5),

              IntradayPriceIconView(
                closePrice: data.value,
                referencePrice: referencePrice,
                ceilingPrice: ceilingPrice,
                floorPrice: floorPrice,
              ),

              const SizedBox(width: 2),

              Text(
                '$changeValue ($changePercent)',
                style: vpTextStyle.captionRegular.copyColor(color),
              ),
            ],
          ),
          Text(
            'Khối lượng: $volumeValue',
            style: vpTextStyle.captionRegular.copyColor(vpColor.textPrimary),
          ),
        ],
      ),
    );
  }
}
