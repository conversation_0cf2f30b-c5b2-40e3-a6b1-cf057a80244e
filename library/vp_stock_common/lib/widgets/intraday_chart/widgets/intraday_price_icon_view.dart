import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';

class IntradayPriceIconView extends StatelessWidget {
  const IntradayPriceIconView({
    required this.closePrice,
    required this.referencePrice,
    this.iconSize = 8,
    this.ceilingPrice,
    this.floorPrice,
    super.key,
  });

  final num closePrice;

  final num referencePrice;

  final num? ceilingPrice;

  final num? floorPrice;

  final double iconSize;

  @override
  Widget build(BuildContext context) {
    final color = StockColorUtils.colorByPrice(
      referencePrice: referencePrice,
      currentPrice: closePrice,
      ceilingPrice: ceilingPrice,
      floorPrice: floorPrice,
    );

    if (closePrice > referencePrice) {
      return Assets.icons.icArrowIncrease.svg(color: color, width: iconSize);
    }

    if (closePrice < referencePrice) {
      return CommonAssets.icons.icArrowDecrease.svg(
        color: color,
        width: iconSize,
      );
    }

    return Assets.icons.icDot.svg(
      color: color,
      width: iconSize,
      height: iconSize,
    );
  }
}
