import 'dart:math';

import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class IntradayChartAdapter {
  List<ChartData> chartData;

  double? refPrice;

  int minutesPerPoint;

  double minY;

  double maxY;

  IntradayChartAdapter({
    required this.chartData,
    this.refPrice,
    this.minutesPerPoint = 5,
    this.minY = -60,
    this.maxY = 130,
  }) {
    updateMinMaxValue();
  }

  void updateChartData(List<ChartData> chartData) {
    this.chartData = chartData;

    updateMinMaxValue();
  }

  void updateMinMaxValue() {
    if (chartData.isNotEmpty) {
      chartData.removeBetween(
        TimeOfDay(hour: 11, minute: 30),
        TimeOfDay(hour: 13, minute: 0),
      );
      chartData.removeAfter(TimeOfDay(hour: 15, minute: 0));
      chartData.removeBefore(TimeOfDay(hour: 9, minute: 0));

      minPrice = chartData.map((a) => a.value.toDouble()).reduce(min);
      maxPrice = chartData.map((a) => a.value.toDouble()).reduce(max);

      if (refPrice != null) minPrice = min(minPrice, refPrice!);
      if (refPrice != null) maxPrice = max(maxPrice, refPrice!);

      minVolume = chartData.map((a) => a.volume.toDouble()).fold(0, min);
      maxVolume = chartData.map((a) => a.volume.toDouble()).fold(0, max);
    } else if (refPrice != null) {
      minPrice = refPrice!;
      maxPrice = refPrice!;
    }
  }

  double minVolume = 0;

  double maxVolume = 0;

  double minPrice = 0;

  double maxPrice = 0;

  num? getPriceByIndex(int index) {
    final item = chartData.getElementAt(index);

    if (item == null) return null;

    return item.value;
  }

  double getPriceFromY(num y) {
    final space = maxPrice - minPrice;

    return ((y * space) / 100) + minPrice;
  }

  double getYVolume(num? volume) {
    if (volume == null) return 0;

    final space = maxVolume - minVolume;

    if (space == 0) return 0;

    return (volume - minVolume) * (minY.abs() * 0.6) / space;
  }

  double getY(num? price) {
    if (price == null) return 0;

    final space = maxPrice - minPrice;

    return space == 0 ? 50 : (price - minPrice) * 100 / space;
  }

  double get minX => 0;

  double get maxX => calculatePoints(minutesPerPoint).toDouble() + 1;

  int calculatePoints(int minutesPerPoint) {
    final morningMinutes = (11 * 60 + 30) - (9 * 60);
    final afternoonMinutes = (15 * 60) - (13 * 60);
    final totalMinutes = morningMinutes + afternoonMinutes;

    return totalMinutes ~/ minutesPerPoint;
  }
}
