import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/core/constant/stock_common_api_path.dart';
import 'package:vp_stock_common/model/bond_model.dart';
import 'package:vp_stock_common/model/current_time_model.dart';
import 'package:vp_stock_common/model/fu_top_10_price_model.dart';
import 'package:vp_stock_common/model/market_info/market_info_model.dart';
import 'package:vp_stock_common/model/market_status_model.dart';
import 'package:vp_stock_common/model/quote/fu_quote_model.dart';
import 'package:vp_stock_common/model/quote/quote_model.dart';
import 'package:vp_stock_common/model/positions/open_position_model.dart';
import 'package:vp_stock_common/model/positions/position_model.dart';
import 'package:vp_stock_common/model/stock_info_model.dart';
import 'package:vp_stock_common/model/stock_model.dart';

part 'stock_common_service.g.dart';

@RestApi()
abstract class StockCommonService {
  factory StockCommonService(Dio dio, {String baseUrl}) = _StockCommonService;

  @GET(StockCommonApi.eodStatus)
  Future<BaseResponse<bool>> getEODStatus();

  @GET(StockCommonApi.getStockInfo)
  Future<BaseResponse<StockInfoBySymbolsModel>> getStockInfoBySymbols(
      @Query('symbols') String indexCode,);

  @GET(StockCommonApi.getStockList)
  Future<BaseResponse<BasePagingResponse<StockModel>?>> getStockList({
    @Query('marketCode') String? marketCode,
    @Query('pageNo') int? pageNo,
    @Query('pageSize') int? pageSize,
  });

  @GET(StockCommonApi.getStockInfoV2)
  Future<BaseResponse<BasePagingResponse<StockInfoModel>>> getStockInfoV2({
    @Query('marketCode') String? marketCode,
    @Query('symbols') String? symbols,
    @Query('stockType') String? stockType,
    @Query('issuerName') String? issuerName,
    @Query('pageNo') int? pageNo,
    @Query('pageSize') int? pageSize,
  });

  @GET(StockCommonApi.quote)
  Future<BaseResponse<List<QuoteModel>>> quote({
    @Query('symbols') String? symbols,
  });

  @GET(StockCommonApi.fuQuote)
  Future<BaseResponse<List<FuQuoteModel>>> fuQuote({
    @Query('symbols') String? symbols,
  });

  @GET(StockCommonApi.getFuStockDetail)
  Future<BaseResponse<BasePagingResponse<StockInfoModel>>> getFuStockDetail({
    @Query('symbols') String? symbols,
    @Query('stockType') String? stockType,
    @Query('pageNo') int? pageNo,
    @Query('pageSize') int? pageSize,
  });

  @GET(StockCommonApi.getOddLotStockDetail)
  Future<BaseResponse<BasePagingResponse<StockInfoModel>>>
  getOddLotStockDetail({
    @Query('marketCode') String? marketCode,
    @Query('symbols') String? symbols,
    @Query('stockType') String? stockType,
    @Query('issuerName') String? issuerName,
    @Query('pageNo') int? pageNo,
    @Query('pageSize') int? pageSize,
  });

  @GET(StockCommonApi.getSuggestedLists)
  Future<BaseResponse<List<WatchlistModel>>> getSuggestedLists();

  @GET(StockCommonApi.getStocksByIndex)
  Future<BaseResponse<BasePagingResponse<StockInfoModel>>> getStocksByIndex({
    @Query('indexCode') String? indexCode,
    @Query('pageNo') int? pageNo,
    @Query('pageSize') int? pageSize,
  });

  @GET(StockCommonApi.getMarketInfo)
  Future<BaseResponse<MarketInfoModel>> getMarketInfo(
      @Query('indexCode') String indexCode,);

  @GET(StockCommonApi.getCorpBondList)
  Future<BaseResponse<List<BondModel>>> getCorpBondList({
    @Query('type') String? type,
  });

  @GET(StockCommonApi.getFuTop10Price)
  Future<BaseResponse<List<FuTop10PriceModel>>> getFuTop10Price({
    @Query('symbol') required String symbol,
  });

  @GET(StockCommonApi.getOpenPositions)
  Future<BaseResponse<PositionsModel>> getOpenPositions({
    @Query('symbol') required String symbol,
    @Query('accountId') required String accountId,
  });

  @GET(StockCommonApi.getCurrentTime)
  Future<BaseResponse<CurrentTimeModel>> getCurrentTime();

  @GET(StockCommonApi.getMarketStatus)
  Future<BaseResponse<List<MarketStatusModel>>> getMarketStatus({
    @Query('marketCode') String? marketCode,
  });
}
