class StockCommonApi {
  static const eodStatus = '/condition-order/common/signal-eod';

  static const createWatchlist =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/watchLists';

  static const getWatchlist =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/watchLists';

  static const watchlist =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/watchLists/{id}';

  static const getStockPortfolio =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-customer/public/v1/portfolio/stocks';

  static const getStockInfo = '/invest/api/stockInfoByList';
  static const getPriceByList =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/stockPriceByList';
  static const getPriceChartLine =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/stockPriceChartLine';

  static const getStockList =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/stockList';

  static const getStockInfoV2 =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/stockDetail';

  static const quote =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/quote';

  static const fuQuote =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/fuQuote';

  static const getFuStockDetail =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/fuStockDetail';
  static const getOddLotStockDetail =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/oddLotStockDetail';

  static const getSuggestedLists =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/suggestedLists';

  static const getStocksByIndex =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/stockDetailByIndex';
  static const getMarketInfo =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/market/intradayMarketIndex';

  static const getCorpBondList =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/corpBondList';

  static const getFuTop10Price =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/stock/fuTop10Price';

  static const getOpenPositions =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-customer/public/v1/portfolio/derivatives/positions';

  static const getCurrentTime =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/time/systemTime';

  static const getMarketStatus =
      'https://neopro-uat.vpbanks.com.vn/neo-inv-tools/public/v1/market/marketStatus';
}
