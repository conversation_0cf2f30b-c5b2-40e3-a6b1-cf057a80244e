// import 'package:flutter/material.dart';
// import 'package:vp_common/vp_common.dart';
// import 'package:vp_core/theme/bloc/theme_cubit.dart';

// class ColorExts {
//   /// Text button khi disable
//   static Color get textDisableButton =>
//       isDark() ? ColorDefine.gray700 : ColorDefine.gray500;

//   /// Shadow tooltip, trackball
//   static Color get boxShadow =>
//       isDark() ? ColorDefine.gray900 : ColorDefine.gray500;

//   /// Background
//   static Color get backgroundPrimary =>
//       ColorUtils.greenChart.withOpacity(isDark() ? 0.16 : 0.16);

//   static Color get backgroundRed =>
//       ColorUtils.red.withOpacity(isDark() ? 0.32 : 0.16);

//   static Color get backgroundBlue =>
//       ColorUtils.blue.withOpacity(isDark() ? 0.32 : 0.16);

//   static Color get backgroundYellow =>
//       ColorUtils.yellow.withOpacity(isDark() ? 0.32 : 0.1);

//   static Color bgIcon(context) {
//     if (isDark()) {
//       return themeData.gray700;
//     } else {
//       return ColorUtils.highlightBg;
//     }
//   }

//   static Color enableButton(context) {
//     if (isDark()) {
//       return ColorUtils.gray900;
//     } else {
//       return ColorUtils.highlightBg;
//     }
//   }

//   static Color black9(context) {
//     return ColorUtils.black;
//   }

//   static Color border(context) {
//     if (isDark()) {
//       return ColorUtils.transparent;
//     } else {
//       return ColorUtils.borderBg;
//     }
//   }

//   /// Input Field
//   static Color get enabledBorder => ColorUtils.borderInput;

//   static Color get focusedBorder => ColorUtils.borderBg;
// }

import 'dart:ui';

import 'package:vp_common/extensions/num_extensions.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class CommonColorUtils {
  /// Màu sắc: < 0 màu đỏ, > 0 màu xanh, = 0 thì hiển thị 0.00 màu trắng
  // static Color derivativeDiffColor({num? diff}) {
  //   if (diff == null || diff == 0) {
  //     return ColorUtils.black;
  //   }

  //   if (diff < 0) {
  //     return ColorUtils.decreaseColor;
  //   }

  //   if (diff > 0) {
  //     return ColorUtils.increaseColor;
  //   }

  //   return ColorUtils.black;
  // }

  // static Color chartColorByPrice({
  //   required double referencePrice,
  //   required num currentPrice,
  //   double? ceilingPrice,
  //   double? floorPrice,
  // }) {
  //   final ceilingValue = ceilingPrice?.toPrecision(2);
  //   final floorValue = floorPrice?.toPrecision(2);
  //   final referenceValue = referencePrice.toPrecision(2);
  //   final currentValue = currentPrice.toDouble().toPrecision(2);
  //
  //   if (ceilingValue == currentValue) {
  //     return themeData.ceilingColor;
  //   }
  //
  //   if (floorValue == currentValue) {
  //     return themeData.floorColor;
  //   }
  //
  //   if (referenceValue > currentValue) {
  //     return themeData.redChart;
  //   }
  //
  //   if (referenceValue < currentValue) {
  //     return themeData.greenChart;
  //   }
  //
  //   return themeData.yellowChart;
  // }

  static Color colorByPrice({
    required double referencePrice,
    required num currentPrice,
    double? ceilingPrice,
    double? floorPrice,
  }) {
    final ceilingValue = ceilingPrice?.toPrecision(2);
    final floorValue = floorPrice?.toPrecision(2);
    final referenceValue = referencePrice.toPrecision(2);
    final currentValue = currentPrice.toDouble().toPrecision(2);

    if (ceilingValue == currentValue) {
      return themeData.ceilingColor;
    }

    if (floorValue == currentValue) {
      return themeData.floorColor;
    }

    if (referenceValue > currentValue) {
      return themeData.decreaseColor;
    }

    if (referenceValue < currentValue) {
      return themeData.increaseColor;
    }

    return themeData.referenceColor;
  }

  // static Color colorByPriceNewDesign({
  //   required double referencePrice,
  //   required num currentPrice,
  //   double? ceilingPrice,
  //   double? floorPrice,
  // }) {
  //   final ceilingValue = ceilingPrice?.toPrecision(2);
  //   final floorValue = floorPrice?.toPrecision(2);
  //   final referenceValue = referencePrice.toPrecision(2);
  //   final currentValue = currentPrice.toDouble().toPrecision(2);

  //   if (ceilingValue == currentValue) {
  //     return ThemeUtils().color.textPricePurple;
  //   }

  //   if (floorValue == currentValue) {
  //     return ThemeUtils().color.textPriceBlue;
  //   }

  //   if (referenceValue > currentValue) {
  //     return ThemeUtils().color.textPriceRed;
  //   }

  //   if (referenceValue < currentValue) {
  //     return ThemeUtils().color.textPriceGreen;
  //   }

  //   return ThemeUtils().color.textPriceYellow;
  // }

  // static Color colorRiskPoint(num? value) {
  //   if (value != null) {
  //     if (value <= 4) {
  //       return ColorUtils.primary;
  //     } else if (value <= 8) {
  //       return ColorUtils.yellow;
  //     } else {
  //       return ColorUtils.red;
  //     }
  //   }
  //   return ColorUtils.primary;
  // }

  // static Color bidColorByPrice({
  //   required double referencePrice,
  //   required String? bidPrice,
  //   double? ceilingPrice,
  //   double? floorPrice,
  //   bool isATO = false,
  //   bool isATC = false,
  //   bool isSell = true,
  // }) {
  //   if (isSell && (isATO || isATC)) return ColorUtils.floorColor;

  //   if (!isSell && (isATO || isATC)) return ColorUtils.ceilingColor;

  //   final currentPrice = double.tryParse(bidPrice ?? '0') ?? 0;

  //   if (currentPrice == 0 && !isSell) {
  //     return ColorUtils.gray500;
  //     // return ColorUtils.primary;
  //   }

  //   if (currentPrice == 0 && isSell) {
  //     return ColorUtils.gray500;
  //     // return ColorUtils.red;
  //   }

  //   if (currentPrice == ceilingPrice) {
  //     return ColorUtils.ceilingColor;
  //   }

  //   if (currentPrice == floorPrice) {
  //     return ColorUtils.floorColor;
  //   }

  //   if (currentPrice < referencePrice) {
  //     return ColorUtils.decreaseColor;
  //   }

  //   if (currentPrice > referencePrice) {
  //     return ColorUtils.increaseColor;
  //   }

  //   return ColorUtils.referenceColor;
  // }

  static Color colorValue(num? price) {
    if (price != null) {
      if (price > 0) {
        return themeData.primary;
      } else if (price < 0) {
        return themeData.red;
      }
    }
    return themeData.yellow;
  }

  // static Color colorValueNewDesign(num? price) {
  //   if (price != null) {
  //     if (price > 0) {
  //       return ThemeUtils().color.textPriceGreen;
  //     } else if (price < 0) {
  //       return ThemeUtils().color.textPriceRed;
  //     }
  //   }
  //   return ThemeUtils().color.textPriceYellow;
  // }

  static Color chartColorValue(num? price) {
    if (price != null) {
      if (price > 0) {
        return themeData.greenChart;
      } else if (price < 0) {
        return themeData.redChart;
      }
    }
    return themeData.yellowChart;
  }

  // static List<Color> colorsChart = [
  //   ColorUtils.yellow,
  //   ColorUtils.primary,
  //   ColorUtils.blueChart,
  //   ColorUtils.ceilingColor,
  //   ColorUtils.red,
  // ];

  static Color colorEvent(String type) {
    switch (type) {
      case 'CashDividend':
        return themeData.primary;
      case 'StockDividend':
        return themeData.red;
      case 'BusinessResults':
        return themeData.yellow;
      case 'Listing':
        return themeData.blue;
      case 'RightsIssue':
        return themeData.purple;
      case 'InternalTransaction':
        return const Color(0xffF96D41);
      case 'Other':
        return themeData.borderPopUp;
    }
    return themeData.black;
  }
}
