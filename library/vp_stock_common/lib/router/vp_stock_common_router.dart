enum VPStockCommonRouter {
  search('/noauth-search'),
  bondSearch('/noauth-bondSearch'),
  pdfOrWebView('/noauth-pdfOrWebView'),
  tradingViewFullscreen('/noauth-tradingviewFullscreen');

  final String routeName;

  const VPStockCommonRouter(this.routeName);
}

enum StockCommonRouterName {
  search('/noauth-search'),
  bondSearch('/noauth-bondSearch'),
  signIn('/signIn'),
  priceBoard('/noauth-priceBoard'),
  fuStockDetail('/noauth-fuStockDetail'),
  stockDetail('/noauth-stockDetail'),
  marketDetail('/market'),
  pdfOrWebView('/noauth-pdfOrWebView'),
  tradingViewFullscreen('/noauth-tradingviewFullscreen');


  final String routeName;

  const StockCommonRouterName(this.routeName);
}
