import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

part 'place_order_args.g.dart';

@GoRouterParams()
class PlaceOrderArgs {
  final SubAccountType? subAccountType;

  final OrderAction action;

  final String? symbol;

  final num? price;

  final String? stockRecommendationId;

  final String? reCustodyCd;

  final PlaceOrderType orderType;

  PlaceOrderArgs({
    String? symbol,
    this.action = OrderAction.buy,
    this.subAccountType,
    this.price,
    this.stockRecommendationId,
    this.reCustodyCd,
    this.orderType = PlaceOrderType.normal,
  }) : symbol =
           orderType == PlaceOrderType.normal
               ? symbol ?? _getLastSymbolFromCache()
               : symbol;

  /// Get the last entered stock symbol from cache
  static String _getLastSymbolFromCache() {
    const keySymbolTradingCurrent = "symbol_trading_current";
    final cachedSymbols = SharedPref.getString(keySymbolTradingCurrent);
    final symbolList =
        cachedSymbols.split(',').where((e) => e.isNotEmpty).toList();

    if (symbolList.isNotEmpty) {
      return symbolList.first;
    }
    return 'VPB'; // Default fallback
  }

  PlaceOrderArgs copyWith({
    SubAccountType? subAccountType,
    OrderAction? action,
    String? symbol,
    num? price,
    String? stockRecommendationId,
    String? reCustodyCd,
  }) {
    return PlaceOrderArgs(
      symbol: symbol ?? this.symbol,
      action: action ?? this.action,
      subAccountType: subAccountType ?? this.subAccountType,
      price: price ?? this.price,
      stockRecommendationId:
          stockRecommendationId ?? this.stockRecommendationId,
      reCustodyCd: reCustodyCd ?? this.reCustodyCd,
      orderType: orderType,
    );
  }
}
