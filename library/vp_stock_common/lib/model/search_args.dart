import 'package:vp_stock_common/model/enum/market_code.dart';
import 'package:vp_stock_common/model/enum/stock_type.dart';
import 'package:vp_stock_common/model/stock_model.dart';

import 'package:vp_common/vp_common.dart';

part 'search_args.g.dart';

enum SearchItemAction { openDetail, pickAndReturn }

@GoRouterParams()
class SearchArgs {
  SearchArgs({
    this.hint,
    this.stockTypes,
    this.marketCodes,
    this.showAddSymbolToWatchlistButton = true,
    this.itemAction = SearchItemAction.openDetail,
  });

  final String? hint;

  final List<MarketCode>? marketCodes;

  final List<StockType>? stockTypes;

  final SearchItemAction itemAction;

  final bool showAddSymbolToWatchlistButton;
}
