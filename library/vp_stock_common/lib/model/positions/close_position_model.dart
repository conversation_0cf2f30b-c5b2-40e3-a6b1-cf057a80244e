import 'package:json_annotation/json_annotation.dart';

part 'close_position_model.g.dart';

@JsonSerializable()
class ClosePositionModel {
  ClosePositionModel({
    this.rowNums,
    this.symbol,
    this.accountId,
    this.productTypeCd,
    this.qty,
    this.vwap,
    this.svwap,
    this.diff,
    this.vrDebtvmAmt,
    this.orderId,
    this.closeTime,
    this.side,
  });

  num? rowNums;
  String? symbol;
  String? accountId;
  String? productTypeCd;
  num? qty;
  num? vwap;
  num? svwap;
  num? diff;
  num? nValue;
  num? vrDebtvmAmt;
  String? orderId;
  String? closeTime;
  String? side;

  factory ClosePositionModel.fromJson(Map<String, dynamic> json) =>
      _$ClosePositionModelFromJson(json);

  Map<String, dynamic> toJson() => _$ClosePositionModelToJson(this);

  static List<ClosePositionModel> fromListJson(dynamic json) {
    final list = <ClosePositionModel>[];
    try {
      final array =
          json is Map ? json['content'] as List? ?? [] : json as List? ?? [];
      for (var v in array) {
        list.add(ClosePositionModel.fromJson(v));
      }
      return list;
    } catch (e) {
      print('Exception in fromListJson: $e');
      return list;
    }
  }
}
