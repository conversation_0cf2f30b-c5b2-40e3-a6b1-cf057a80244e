import 'dart:ui';

import 'package:json_annotation/json_annotation.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';

part 'open_position_model.g.dart';

@JsonSerializable()
class OpenPositionModel {
  OpenPositionModel({
    this.accountId,
    this.custodyCd,
    this.symbol,
    this.quoteId,
    this.isTpsl,
    this.qty,
    this.isClose,
    this.isNet,
    this.pendingLQty,
    this.pendingSQty,
    this.dsp,
    this.nonrPlAmt,
    this.position,
    this.priceSecured,
    this.nValue,
    this.vwap,
    this.percentNonrPLAmt,
    this.totalPlAmt,
    this.vrImAmt,
    this.vrDebtVmAmt,
    this.diff,
    this.vmAmt,
    this.lastChange,
  });

  String? accountId;
  String? custodyCd;
  String? symbol;
  String? quoteId;
  String? isTpsl;
  num? qty;
  String? isClose;
  String? isNet;
  num? pendingLQty;
  num? pendingSQty;
  num? dsp;
  num? nonrPlAmt;
  String? position;
  num? priceSecured;
  num? nValue;
  num? vwap;
  num? percentNonrPLAmt;
  @JsonKey(name: 'totalPLAmt')
  num? totalPlAmt;
  num? vrImAmt;
  num? vrDebtVmAmt;
  num? diff;
  num? vmAmt;
  String? lastChange;

  factory OpenPositionModel.fromJson(Map<String, dynamic> json) =>
      _$OpenPositionModelFromJson(json);

  Map<String, dynamic> toJson() => _$OpenPositionModelToJson(this);

  num get valueQty {
    if (qty == null || qty == 0) return 0;
    if ((qty ?? 0) > 0) return qty ?? 0;
    return (qty ?? 0).abs();
  }

  Color get colorQty {
    if (qty == null || qty == 0) return vpColor.textPrimary;
    if ((qty ?? 0) > 0) return vpColor.textPriceGreen;
    return vpColor.textPriceRed;
  }

  bool get canSetStopLoss => isTpsl == 'Y';
  bool get hasPendingTpslOrders => isTpsl == 'N';
  bool get isLongPosition => qty != null && qty! > 0;
  bool get enableButton => isClose == 'Y'; 
}
