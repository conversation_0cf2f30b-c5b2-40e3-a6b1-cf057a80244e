// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'close_position_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ClosePositionModel _$ClosePositionModelFromJson(Map<String, dynamic> json) =>
    ClosePositionModel(
      rowNums: json['rowNums'] as num?,
      symbol: json['symbol'] as String?,
      accountId: json['accountId'] as String?,
      productTypeCd: json['productTypeCd'] as String?,
      qty: json['qty'] as num?,
      vwap: json['vwap'] as num?,
      svwap: json['svwap'] as num?,
      diff: json['diff'] as num?,
      vrDebtvmAmt: json['vrDebtvmAmt'] as num?,
      orderId: json['orderId'] as String?,
      closeTime: json['closeTime'] as String?,
      side: json['side'] as String?,
    )..nValue = json['nValue'] as num?;

Map<String, dynamic> _$ClosePositionModelToJson(ClosePositionModel instance) =>
    <String, dynamic>{
      'rowNums': instance.rowNums,
      'symbol': instance.symbol,
      'accountId': instance.accountId,
      'productTypeCd': instance.productTypeCd,
      'qty': instance.qty,
      'vwap': instance.vwap,
      'svwap': instance.svwap,
      'diff': instance.diff,
      'nValue': instance.nValue,
      'vrDebtvmAmt': instance.vrDebtvmAmt,
      'orderId': instance.orderId,
      'closeTime': instance.closeTime,
      'side': instance.side,
    };
