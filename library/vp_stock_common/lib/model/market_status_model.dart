import 'package:json_annotation/json_annotation.dart';

part 'market_status_model.g.dart';

@JsonSerializable()
class MarketStatusModel {
  final String? marketCode;
  final String? indexCode;
  final String? marketStatus;

  MarketStatusModel({this.marketCode, this.indexCode, this.marketStatus});

  factory MarketStatusModel.fromJson(Map<String, dynamic> json) =>
      _$MarketStatusModelFromJson(json);
}
