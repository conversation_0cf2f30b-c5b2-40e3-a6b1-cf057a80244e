// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'place_order_args.dart';

// **************************************************************************
// FromQueryParamsGenerator
// **************************************************************************

extension PlaceOrderArgsExtensions on PlaceOrderArgs {
  static PlaceOrderArgs fromQueryParams(Map<String, String> params) {
    return PlaceOrderArgs(
      symbol: params['symbol'],
      action: _tryParseEnumOrderAction(params['action']) ?? OrderAction.buy,
      subAccountType: _tryParseEnumSubAccountType(params['subAccountType']),
      price: num.tryParse(params['price'] ?? ''),
      stockRecommendationId: params['stockRecommendationId'],
      reCustodyCd: params['reCustodyCd'],
      orderType:
          _tryParseEnumPlaceOrderType(params['orderType']) ??
          PlaceOrderType.normal,
    );
  }

  Map<String, String> toQueryParams() {
    return {
      if (subAccountType != null) 'subAccountType': subAccountType!.name,
      'action': action.name,
      if (symbol != null) 'symbol': symbol!.toString(),
      if (price != null) 'price': price!.toString(),
      if (stockRecommendationId != null)
        'stockRecommendationId': stockRecommendationId!.toString(),
      if (reCustodyCd != null) 'reCustodyCd': reCustodyCd!.toString(),
      'orderType': orderType.name,
    };
  }

  static OrderAction? _tryParseEnumOrderAction(String? name) {
    if (name == null) return null;
    return OrderAction.values.cast<OrderAction?>().firstWhere(
      (e) => e!.name == name,
      orElse: () => null,
    );
  }

  static SubAccountType? _tryParseEnumSubAccountType(String? name) {
    if (name == null) return null;
    return SubAccountType.values.cast<SubAccountType?>().firstWhere(
      (e) => e!.name == name,
      orElse: () => null,
    );
  }

  static PlaceOrderType? _tryParseEnumPlaceOrderType(String? name) {
    if (name == null) return null;
    return PlaceOrderType.values.cast<PlaceOrderType?>().firstWhere(
      (e) => e!.name == name,
      orElse: () => null,
    );
  }
}
