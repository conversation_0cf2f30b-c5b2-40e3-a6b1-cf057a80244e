enum MarketStatus {
  /// Trước phiên
  preOpen("PO"),

  /// Phiên Buy In
  buyIn("B"),

  /// Phiên ATO
  ato("ATO"),

  /// Phiên liên tục
  continuous("LO"),

  /// Phiên nghỉ trưa
  lunchBreak("L"),

  /// Phiên ATC
  atc("ATC"),

  /// Phiên thỏa thuận
  putThrough("PT"),

  /// Phiên sau giờ (PLO)
  plo("PLO"),

  /// Đóng cửa
  close("C"),

  /// Kết thúc nghỉ giữa khớp lệnh liên tục
  endBreakContinuous("F"),

  /// Ngừng giao dịch
  halt("H"),

  /// Bắt đầu EOD
  startEod("G"),

  /// Kết thúc EOD
  endEod("J"),

  /// Trước giờ
  preTime("PRE");

  final String value;

  const MarketStatus(this.value);

  String get title {
    switch (this) {
      case MarketStatus.preOpen:
        return "Trước phiên";
      case MarketStatus.buyIn:
        return "Phiên Buy In";
      case MarketStatus.ato:
        return "Phiên ATO";
      case MarketStatus.continuous:
        return "Liên tục";
      case MarketStatus.lunchBreak:
        return "Nghỉ trưa";
      case MarketStatus.atc:
        return "Phiên ATC";
      case MarketStatus.putThrough:
        return "Phiên thỏa thuận";
      case MarketStatus.plo:
        return "Phiên sau giờ (PLO)";
      case MarketStatus.close:
        return "Đóng cửa";
      case MarketStatus.endBreakContinuous:
        return "Kết thúc nghỉ giữa khớp lệnh liên tục";
      case MarketStatus.halt:
        return "Ngừng giao dịch";
      case MarketStatus.startEod:
        return "Bắt đầu EOD";
      case MarketStatus.endEod:
        return "Kết thúc EOD";
      case MarketStatus.preTime:
        return "Trước giờ";
    }
  }
}
