import 'package:vp_socket/vp_socket.dart';

mixin MarketSessionSocketMixin {
  VPInvestSub? _topic;

  ListenerHandle? _listenerHandle;

  final _socket = SocketFactory.get(SocketType.stock) as IsolateInvestSocket;

  void subscribeMarketSession(Set<String>? marketCodes) {
    if (marketCodes == null || marketCodes.isEmpty) return;

    _topic = VPStockInfoSub(
      symbols: marketCodes,
      channel: VPSocketChannel.marketSession.name,
    );

    _socket.subscribe(_topic!);

    _listenerHandle = _socket.addListener(
      _topic!,
      listener: (data) => _onMarketSessionListener(data),
      selector:
          (_, data) =>
              data is VPMarketSessionData && marketCodes.contains(data.symbol),
    );
  }

  void unsubscribeMarketSession() {
    _listenerHandle?.dispose();
  }

  void _onMarketSessionListener(VPSocketData? data) {
    if (data is VPMarketSessionData) {
      onSocketMarketSessionListener(data);
    }
  }

  void onSocketMarketSessionListener(VPMarketSessionData data) {}
}
