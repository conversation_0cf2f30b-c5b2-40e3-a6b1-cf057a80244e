import 'package:flutter/material.dart';
import 'package:vp_design_system/themes/vps_token_color.dart';

extension ColorOld on ThemeData {
  Color get bgMain {
    if (brightness == Brightness.light) {
      return const Color(0xffffffff);
    } else {
      return const Color(0xff14141E);
    }
  }

  Color get primary {
    return extension<CustomColors>()!.textBrand;
  }

  Color get highlightPopup {
    if (brightness == Brightness.light) {
      return const Color(0xffF5F5F4);
    } else {
      return const Color(0xff272730);
    }
  }

  Color get gray700 {
    if (brightness == Brightness.light) {
      return const Color(0xff616161);
    } else {
      return const Color(0xffE0E0E0);
    }
  }

  Color get bgBottomBar {
    if (brightness == Brightness.light) {
      return const Color(0xffffffff);
    } else {
      return const Color(0xff1C1C28);
    }
  }

  Color get yellow2 {
    if (brightness == Brightness.light) {
      return const Color(0xFFFFF5D6);
    } else {
      return const Color(0xFFC79300);
    }
  }

  Color get yellow {
    if (brightness == Brightness.light) {
      return const Color(0xFFFEB700);
    } else {
      return const Color(0xFFFEB700);
    }
  }

  Color get red {
    if (brightness == Brightness.light) {
      return const Color(0xFFFF362F);
    } else {
      return const Color(0xFFFF362F);
    }
  }

  Color get buttonEnableBg {
    if (brightness == Brightness.light) {
      return const Color(0xff00A36A);
    } else {
      return const Color(0xff00A36A);
    }
  }

  Color get gray100 {
    if (brightness == Brightness.light) {
      return const Color(0xffEAEBEA);
    } else {
      return const Color(0xff212121);
    }
  }

  Color get gray300 {
    if (brightness == Brightness.light) {
      return const Color(0xffE0E0E0);
    } else {
      return const Color(0xff616161);
    }
  }

  Color get gray500 {
    if (brightness == Brightness.light) {
      return const Color(0xff8E8E8E);
    } else {
      return const Color(0xff9E9E9E);
    }
  }

  Color get gray900 {
    if (brightness == Brightness.light) {
      return const Color(0xff212121);
    } else {
      return const Color(0xffEAEBEA);
    }
  }

  Color get hightLightBg {
    if (brightness == Brightness.light) {
      return const Color(0xffF5F5F4);
    } else {
      return const Color(0xff272730);
    }
  }

  /// TextButton
  Color get textEnable {
    if (brightness == Brightness.light) {
      return const Color(0xffffffff);
    } else {
      return const Color(0xffffffff);
    }
  }

  Color get bgPopup {
    if (brightness == Brightness.light) {
      return const Color(0xffffffff);
    } else {
      return const Color(0xff21212e);
    }
  }

  Color get overlayBottomSheet {
    if (brightness == Brightness.light) {
      return const Color(0xff0f0f0f).withValues(alpha: 0.5);
    } else {
      return const Color(0xff0f0f0f).withValues(alpha: 0.6);
    }
  }

  Color get divider {
    if (brightness == Brightness.light) {
      return const Color(0xffEAEBEA);
    } else {
      return const Color(0xff353535);
    }
  }

  Color get eyeColor {
    if (brightness == Brightness.light) {
      return const Color(0xff616161);
    } else {
      return Colors.white;
    }
  }

  Color get icon {
    if (brightness == Brightness.light) {
      return gray700;
    } else {
      return gray100;
    }
  }

  Color get text500 {
    if (brightness == Brightness.light) {
      return const Color(0xff8E8E8E);
    } else {
      return const Color(0xff9E9E9E);
    }
  }

  Color get primaryDark {
    if (brightness == Brightness.light) {
      return const Color(0xFF00A36A);
    } else {
      return const Color(0xFF00A36A);
    }
  }

  Color get buttonDisableBg {
    if (brightness == Brightness.light) {
      return const Color(0xffF5F5F4);
    } else {
      return const Color(0xff272730);
    }
  }

  Color get borderBg {
    if (brightness == Brightness.light) {
      return const Color(0xff616161);
    } else {
      return const Color(0xffe0e0e0);
    }
  }

  Color get border {
    if (brightness == Brightness.light) {
      return borderBg;
    } else {
      return Colors.transparent;
    }
  }

  Color get buttonTopBottomSheet {
    if (brightness == Brightness.light) {
      return gray500;
    } else {
      return Colors.white;
    }
  }

  Color get white {
    if (brightness == Brightness.light) {
      return const Color(0xffffffff);
    } else {
      return const Color(0xff0f0f0f);
    }
  }

  Color get highlightBg {
    if (brightness == Brightness.light) {
      return const Color(0xffF5F5F4);
    } else {
      return const Color(0xff272730);
    }
  }

  Color get increaseColor {
    if (brightness == Brightness.light) {
      return const Color(0xFF1CC74C);
    } else {
      return const Color(0xFF1CC74C);
    }
  }

  Color get decreaseColor {
    if (brightness == Brightness.light) {
      return const Color(0xFFFF362F);
    } else {
      return const Color(0xFFFF362F);
    }
  }

  Color get referenceColor {
    if (brightness == Brightness.light) {
      return const Color(0xFFFEB700);
    } else {
      return const Color(0xFFFEB700);
    }
  }

  Color get borderInput {
    if (brightness == Brightness.light) {
      return const Color(0xff616161);
    } else {
      return Colors.transparent;
    }
  }

  /// Shadow tooltip, trackball
  Color get boxShadow {
    if (brightness == Brightness.light) {
      return gray500;
    } else {
      return gray900;
    }
  }

  Color get skeletonBase {
    if (brightness == Brightness.light) {
      return const Color(0xffF1EFEF);
    } else {
      return const Color(0xFF1F1F1F);
    }
  }

  Color get skeletonHighLight {
    if (brightness == Brightness.light) {
      return const Color(0xffF9F8F8);
    } else {
      return const Color(0xFF373737);
    }
  }

  Color get bgSnackBar {
    if (brightness == Brightness.light) {
      return const Color(0xffffffff);
    } else {
      return const Color(0xff252B39);
    }
  }

  Color get redDark {
    if (brightness == Brightness.light) {
      return const Color(0xffAD1D26);
    } else {
      return const Color(0xFFFF362F);
    }
  }

  Color get bgPopupTutorial {
    if (brightness == Brightness.light) {
      return const Color(0xFFF5F5F4);
    } else {
      return const Color(0xff21212e);
    }
  }

  Color get primary16 {
    return primary.withValues(alpha: 0.16);
  }

  Color get blue16 {
    if (brightness == Brightness.light) {
      return blue.withOpacity(0.16);
    } else {
      return blue.withOpacity(0.16);
    }
  }

  Color get red16 {
    if (brightness == Brightness.light) {
      return const Color(0xFFFF362F).withOpacity(0.16);
    } else {
      return const Color(0xFFFF362F).withOpacity(0.16);
    }
  }

  Color get blue {
    if (brightness == Brightness.light) {
      return const Color(0xFF55B4FF);
    } else {
      return const Color(0xFF55B4FF);
    }
  }

  Color get black {
    if (brightness == Brightness.light) {
      return const Color(0xff0f0f0f);
    } else {
      return const Color(0xffffffff);
    }
  }

  Color get ceilingColor {
    if (brightness == Brightness.light) {
      return const Color(0xFFFF18FF);
    } else {
      return const Color(0xFFFF18FF);
    }
  }

  Color get blueChart {
    return const Color(0xFF2196F3);
  }

  Color get greenChart {
    return const Color(0xFF1CC74C);
  }

  Color get redChart {
    return const Color(0xFFFF362F);
  }

  Color get yellowChart {
    return const Color(0xFFFEB700);
  }

  Color get orange {
    return const Color(0xffF96D41);
  }

  Color get orange16 {
    return const Color(0x29f96d41);
  }

  Color get yellow16 {
    if (brightness == Brightness.light) {
      return yellow.withOpacity(0.16);
    } else {
      return yellow.withOpacity(0.16);
    }
  }

  Color get purple {
    if (brightness == Brightness.light) {
      return const Color(0xFFFF18FF);
    } else {
      return const Color(0xFFFF18FF);
    }
  }

  Color get bgInput {
    if (brightness == Brightness.light) {
      return const Color(0xffffffff);
    } else {
      return const Color(0xff272730);
    }
  }

  Color get colorIcon {
    if (brightness == Brightness.light) {
      return const Color(0xff616161);
    } else {
      return const Color(0xffEAEBEA);
    }
  }

  Color get highLightPopup {
    if (brightness == Brightness.light) {
      return const Color(0xffF5F5F4);
    } else {
      return const Color(0xff272730);
    }
  }

  Color get borderDisable {
    if (brightness == Brightness.light) {
      return const Color(0xffeaebea);
    } else {
      return const Color(0xff353535);
    }
  }

  Color get enableButton {
    if (brightness == Brightness.light) {
      return highlightBg;
    } else {
      return gray900;
    }
  }

  Color get focus {
    if (brightness == Brightness.light) {
      return const Color(0xff14141E);
    } else {
      return const Color(0xffffffff);
    }
  }

  Color get displayLarge {
    if (brightness == Brightness.light) {
      return const Color(0xff14141E);
    } else {
      return const Color(0xffffffff);
    }
  }

  Color get borderPopUp {
    if (brightness == Brightness.light) {
      return Color(0xff9E9E9E);
    } else {
      return Color(0xff9E9E9E);
    }
  }

  Color get iconDisabled {
    if (brightness == Brightness.light) {
      return const Color(0xFF9E9E9E);
    } else {
      return const Color(0xFF3D3D3D);
    }
  }

  Color get floorColor {
    if (brightness == Brightness.light) {
      return const Color(0xFF55B4FF);
    } else {
      return const Color(0xFF55B4FF);
    }
  }

  Color get strokeBold {
    if (brightness == Brightness.light) {
      return const Color(0xFFE0E0E0);
    } else {
      return const Color(0xFF7D7D7D);
    }
  }

  Color get backgroundElevation1 {
    if (brightness == Brightness.light) {
      return const Color(0xFFFFFFFF);
    } else {
      return const Color(0xFF282828);
    }
  }

  Color get textGreen {
    if (brightness == Brightness.light) {
      return const Color(0xFF19B545);
    } else {
      return const Color(0xFF1CC74C);
    }
  }

  Color get bgUtilities {
    if (brightness == Brightness.light) {
      return const Color(0xffe2f8ee);
    } else {
      return const Color(0xff255244);
    }
  }

  Color get text {
    if (brightness == Brightness.light) {
      return const Color(0xff0f0f0f);
    } else {
      return const Color(0xffffffff);
    }
  }

  Color get text900 {
    if (brightness == Brightness.light) {
      return const Color(0xff212121);
    } else {
      return const Color(0xffEAEBEA);
    }
  }

  Color get bgButtonSwitch {
    if (brightness == Brightness.light) {
      return const Color(0xfff5f5f4);
    } else {
      return const Color(0xfff5f5f4);
    }
  }

  Color get yellowLight {
    if (brightness == Brightness.light) {
      return const Color(0xFFFEB700);
    } else {
      return const Color(0xFFFEB700);
    }
  }

  Color get redLight2 {
    if (brightness == Brightness.light) {
      return const Color(0xffff867b);
    } else {
      return const Color(0xffff867b);
    }
  }

  Color get blueChartLight {
    if (brightness == Brightness.light) {
      return const Color(0xFF0CB6FF);
    } else {
      return const Color(0xFF0CB6FF);
    }
  }

  Color get bgHighLightDerivative {
    if (brightness == Brightness.light) {
      return const Color(0xffffffff);
    } else {
      return const Color(0xFF272730);
    }
  }

  Color get iconPrimary {
    if (brightness == Brightness.light) {
      return const Color(0xFF00A36A);
    } else {
      return const Color(0xFF00A36A);
    }
  }

  Color get textHint {
    if (brightness == Brightness.light) {
      return const Color(0xff8E8E8E);
    } else {
      return const Color(0xff8E8E8E);
    }
  }

  Color get errorBgInput {
    if (brightness == Brightness.light) {
      return const Color(0xFFFF362F).withOpacity(0.16);
    } else {
      return const Color(0xFFFF362F).withOpacity(0.32);
    }
  }

  Color get backgroundRed {
    if (brightness == Brightness.light) {
      return red.withOpacity(0.32);
    } else {
      return red.withOpacity(0.16);
    }
  }

  Color get text700 {
    if (brightness == Brightness.light) {
      return const Color(0xff616161);
    } else {
      return const Color(0xffE0E0E0);
    }
  }

  Color get primaryColor2 {
    if (brightness == Brightness.light) {
      return const Color(0xFF00A36A);
    } else {
      return const Color(0xFF00A36A);
    }
  }

  Color get cursorTextField {
    if (brightness == Brightness.light) {
      return const Color(0xff14141E);
    } else {
      return const Color(0xffF5F5F4);
    }
  }

  Color get bgLoading {
    if (brightness == Brightness.light) {
      return black.withOpacity(0.3);
    } else {
      return black.withOpacity(0.7);
    }
  }

  Color get skeletonBaseDark {
    if (brightness == Brightness.light) {
      return const Color(0xff2B2B34);
    } else {
      return const Color(0xff2B2B34);
    }
  }

  Color get backgroundPrimary {
    if (brightness == Brightness.light) {
      return greenChart.withOpacity(0.16);
    } else {
      return greenChart.withOpacity(0.16);
    }
  }

  Color get purpleDark {
    return const Color(0xFFFF18FF);
  }

  Color get blueDark {
    return const Color(0xFF55B4FF);
  }

  Color get textPriceGreen {
    if (brightness == Brightness.light) {
      return const Color.fromRGBO(6, 177, 117, 1);
    } else {
      return const Color.fromRGBO(5, 193, 127, 1);
    }
  }

  Color get textPriceRed {
    if (brightness == Brightness.light) {
      return const Color.fromRGBO(255, 74, 69, 1);
    } else {
      return const Color.fromRGBO(253, 81, 76, 1);
    }
  }
}
