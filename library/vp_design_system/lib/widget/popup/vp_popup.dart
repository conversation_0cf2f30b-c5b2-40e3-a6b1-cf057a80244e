import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vp_design_system/themes/utils.dart';
import 'package:vp_design_system/widget/popup/vp_popup_extension.dart';

import 'vp_popup_common.dart';

class VPPopup extends StatelessWidget {
  const VPPopup(
    this.builder, {
    super.key,
    this.padding = EdgeInsets.zero,
    this.contentPadding = EdgeInsets.zero,
    this.icon,
    this.buttons = const [],
    this.buttonLayout,
  });

  factory VPPopup.bottomSheet(Widget builder) =>
      VPPopup(builder).icHandle.copyWith(
            padding: const EdgeInsets.only(left: 20, right: 20, top: 20),
            contentPadding: const EdgeInsets.symmetric(vertical: 20),
          );

  factory VPPopup.bottomAction({required List<Widget> items}) => VPPopup(
        Builder(
          builder: (context) => Container(
            decoration: BoxDecoration(
              color: context.colors.backgroundElevation0,
              borderRadius: BorderRadius.circular(12),
            ),
            child: ListView.separated(
              itemBuilder: (_, index) => items[index],
              separatorBuilder: (context, _) =>
                  Divider(height: 1, color: context.colors.strokeNormal),
              itemCount: items.length,
              physics: const BouncingScrollPhysics(),
              shrinkWrap: true,
            ),
          ),
        ),
      ).copyWith(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        contentPadding: const EdgeInsets.only(bottom: 8),
      );

  factory VPPopup.threeButton({
    required String title,
    required String content,
  }) =>
      VPPopup(CommonDialog(title: title, content: content)).copyWith(
        padding: const EdgeInsets.all(20),
        contentPadding: const EdgeInsets.only(bottom: 16),
        buttonLayout: (buttons) {
          assert(buttons.length == 3,
              'Expected exactly 3 buttons, but got ${buttons.length}.');
          return Row(
            children: [
              buttons[0],
              const SizedBox(width: 60),
              Expanded(child: buttons[1]),
              const SizedBox(width: 8),
              Expanded(child: buttons[2]),
            ],
          );
        },
      );

  factory VPPopup.outlineAndPrimaryButton({
    required String title,
    required String content,
  }) =>
      VPPopup(CommonDialog(title: title, content: content)).copyWith(
        padding: const EdgeInsets.all(20),
        contentPadding: const EdgeInsets.only(bottom: 16),
        buttonLayout: (buttons) {
          assert(buttons.length == 2,
              'Expected exactly 2 buttons, but got ${buttons.length}.');
          return Row(
            children: [
              Expanded(child: buttons[0]),
              const SizedBox(width: 8),
              Expanded(child: buttons[1]),
            ],
          );
        },
      );

  factory VPPopup.textAndPrimaryButton({
    required String title,
    required String content,
  }) =>
      VPPopup(CommonDialog(title: title, content: content)).copyWith(
        padding: const EdgeInsets.all(20),
        contentPadding: const EdgeInsets.only(bottom: 16),
        buttonLayout: (buttons) {
          assert(buttons.length == 2,
              'Expected exactly 2 buttons, but got ${buttons.length}.');
          return Row(
            children: [
              buttons[0],
              const SizedBox(width: 60),
              Expanded(child: buttons[1]),
            ],
          );
        },
      );

  factory VPPopup.oneButton({
    required String title,
    required String content,
    Widget? image,
  }) =>
      VPPopup(CommonDialog(title: title, content: content, image: image))
          .copyWith(
        padding: const EdgeInsets.all(20),
        contentPadding: const EdgeInsets.only(bottom: 16),
        buttonLayout: (buttons) {
          assert(buttons.length == 1,
              'Expected exactly 1 buttons, but got ${buttons.length}.');
          return Row(children: [Expanded(child: buttons.first)]);
        },
      );

  factory VPPopup.custom({
    required Widget child,
    EdgeInsets? padding,
    EdgeInsets? contentPadding,
  }) =>
      VPPopup(child).copyWith(
        padding: padding,
        contentPadding: contentPadding,
      );

  final Widget builder;
  final EdgeInsets padding;
  final EdgeInsets contentPadding;
  final dynamic icon;
  final List<Widget> buttons;
  final Widget Function(List<Widget> buttons)? buttonLayout;

  VPPopup copyWith({
    EdgeInsets? padding,
    EdgeInsets? contentPadding,
    dynamic icon,
    Widget? button,
    Widget Function(List<Widget> buttons)? buttonLayout,
  }) =>
      VPPopup(
        builder,
        key: key,
        padding: padding ?? this.padding,
        contentPadding: contentPadding ?? this.contentPadding,
        icon: icon ?? this.icon,
        buttons: [...buttons, if (button != null) button],
        buttonLayout: buttonLayout ?? this.buttonLayout,
      );

  Widget get popupIcon => icon is String
      ? SvgPicture.asset(icon as String)
      : icon is Widget
          ? icon as Widget
          : const SizedBox.shrink();

  Widget get popupBody => Padding(padding: contentPadding, child: this);

  Widget get popupButtons =>
      buttonLayout?.call(buttons) ??
      (buttons.isNotEmpty
          ? Row(
              children: List.generate(
                buttons.length * 2 - 1,
                (index) => index.isEven
                    ? Expanded(child: buttons[index ~/ 2])
                    : const SizedBox(width: 8),
              ),
            )
          : const SizedBox.shrink());

  @override
  Widget build(BuildContext context) => builder;
}
