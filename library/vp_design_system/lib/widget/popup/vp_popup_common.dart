import 'package:flutter/material.dart';
import 'package:vp_design_system/themes/utils.dart';

class CommonDialog extends StatelessWidget {
  const CommonDialog({
    required this.title,
    required this.content,
    this.image,
    super.key,
  });

  final String title;

  final String content;

  final Widget? image;

  @override
  Widget build(BuildContext context) => Column(
        children: [
          if (image != null) image!,
          const SizedBox(height: 16),
          Text(
            title,
            style: context.textStyle.headineBold6
                ?.copyWith(color: context.colors.textPrimary),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: context.textStyle.body14
                ?.copyWith(color: context.colors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      );
}
