import 'package:flutter/material.dart';
import 'package:vp_design_system/themes/utils.dart';
import '../../gen/assets.gen.dart';
import 'vp_popup.dart';

extension VPPopupExt on VPPopup {
  VPPopup get icHandle => copyWith(
        icon: Builder(
          builder: (context) => DesignAssets.icons.dialog.icHandle.svg(
            colorFilter: ColorFilter.mode(
              context.colors.strokeNormal,
              BlendMode.srcIn,
            ),
          ),
        ),
      );

  Future showDialog(
    BuildContext context, {
    Color? backgroundColor,
    bool barrierDismissible = false,
    EdgeInsets insetPadding = const EdgeInsets.all(12),
    BorderRadius borderRadius = const BorderRadius.all(Radius.circular(12)),
  }) =>
      showGeneralDialog(
        context: context,
        barrierLabel: '',
        barrierDismissible: barrierDismissible,
        pageBuilder: (_, __, ___) => Dialog(
          elevation: 0,
          insetPadding: insetPadding,
          backgroundColor:
              backgroundColor ?? context.colors.backgroundElevation0,
          shape: RoundedRectangleBorder(borderRadius: borderRadius),
          child: Padding(
            padding: padding,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [popupIcon, popupBody, popupButtons],
            ),
          ),
        ),
      );

  Future<dynamic> showAction(
    BuildContext context, {
    bool isDismissible = true,
    double marginTop = 0.0,
    Color? backgroundColor,
  }) =>
      showSheet(
        context,
        marginTop: marginTop,
        isDismissible: isDismissible,
        borderRadius: BorderRadius.zero,
        backgroundColor: backgroundColor ?? Colors.transparent,
      );

  Future<dynamic> showSheet(
    BuildContext context, {
    double heightFactor = 0.85,
    double marginTop = 0.0,
    bool isExpanded = false,
    bool isDismissible = true,
    Color? backgroundColor,
    BorderRadius borderRadius = const BorderRadius.all(Radius.circular(12)),
  }) =>
      showModalBottomSheet(
        context: context,
        elevation: 0,
        isScrollControlled: true,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height -
              MediaQuery.of(Navigator.of(context).context).padding.top -
              marginTop,
        ),
        backgroundColor: backgroundColor ?? context.colors.backgroundElevation0,
        shape: RoundedRectangleBorder(borderRadius: borderRadius),
        isDismissible: isDismissible,
        builder: (context) {
          final child = ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.sizeOf(context).height * heightFactor,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                popupIcon,
                Flexible(
                  fit: isExpanded ? FlexFit.tight : FlexFit.loose,
                  child: popupBody,
                ),
                popupButtons,
              ],
            ),
          );

          final bottomPadding = MediaQuery.of(context).viewInsets.bottom;

          return Padding(
            padding: EdgeInsets.only(bottom: bottomPadding),
            child: SafeArea(
              child: Padding(
                padding: padding,
                child: child,
              ),
            ),
          );
        },
      );
}
