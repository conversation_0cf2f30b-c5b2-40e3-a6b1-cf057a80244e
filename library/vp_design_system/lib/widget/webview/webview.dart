import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_design_system/themes/utils.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

typedef Cookie = ({String name, String value, String domain, String path});

enum WebViewStatus { loading, error, done }

class VPWebView extends StatefulWidget {
  const VPWebView({
    this.url,
    this.html,
    this.cookie,
    this.showRetryWhenError = false,
    this.showLoadingIndicator = true,
    this.backgroundColor,
    this.onPageFinished,
    this.onPageStarted,
    this.onProgress,
    this.onWebResourceError,
    this.onUrlChange,
    this.onNavigationRequest,
    this.onConsoleMessage,
    this.onControllerInitialized,
    this.onMessageReceived,
    this.loadingBuilder,
    this.clearCache = false,
    this.clearLocalStorage = false,
    this.gestureRecognizers = const <Factory<OneSequenceGestureRecognizer>>{},
    this.needAccessToken = false,
    super.key,
  });

  final String? url;

  final String? html;

  final Cookie? cookie;

  final void Function(String url)? onPageStarted;

  final void Function(String url)? onPageFinished;

  final void Function(int progress)? onProgress;

  final void Function(WebResourceError error)? onWebResourceError;

  final void Function(UrlChange change)? onUrlChange;

  final void Function(JavaScriptMessage)? onMessageReceived;

  final FutureOr<NavigationDecision> Function(NavigationRequest request)?
      onNavigationRequest;

  final void Function(JavaScriptConsoleMessage message)? onConsoleMessage;

  final ValueChanged<WebViewController>? onControllerInitialized;

  final Set<Factory<OneSequenceGestureRecognizer>> gestureRecognizers;

  final Widget Function(BuildContext)? loadingBuilder;

  final Color? backgroundColor;

  final bool showLoadingIndicator;

  final bool clearCache;

  final bool clearLocalStorage;

  final bool showRetryWhenError;

  final bool needAccessToken;

  @override
  State<VPWebView> createState() => _VPWebViewState();
}

class _VPWebViewState extends State<VPWebView> {
  late WebViewController controller;
  late final WebViewCookieManager _cookieManager = WebViewCookieManager();

  WebViewStatus status = WebViewStatus.loading;

  WebViewController _getController() {
    late final PlatformWebViewControllerCreationParams params;

    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }

    final controller = WebViewController.fromPlatformCreationParams(params);

    if (controller.platform is AndroidWebViewController) {
      (controller.platform as AndroidWebViewController)
        ..setMediaPlaybackRequiresUserGesture(false)
        ..setOnPlatformPermissionRequest((request) => request.grant());
    }

    if (controller.platform is WebKitWebViewController) {
      (controller.platform as WebKitWebViewController)
          .setOnPlatformPermissionRequest((request) => request.grant());
    }

    if (widget.clearCache) {
      controller.clearCache();
    }

    if (widget.clearLocalStorage) {
      controller.clearLocalStorage();
    }

    controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(widget.backgroundColor ?? Colors.transparent)
      ..addJavaScriptChannel(
        'NativeJavascriptInterface',
        onMessageReceived: (data) => widget.onMessageReceived!(data),
      )
      ..addJavaScriptChannel('clickBackButton',
          onMessageReceived: (data) => widget.onMessageReceived!(data),)
      ..addJavaScriptChannel('copyText',
          onMessageReceived: (data) => widget.onMessageReceived!(data),)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: widget.onProgress,
          onPageStarted: widget.onPageStarted,
          onPageFinished: onPageFinished,
          onWebResourceError: onWebResourceError,
          onUrlChange: widget.onUrlChange,
          onNavigationRequest: widget.onNavigationRequest,
        ),
      );

    if (widget.onConsoleMessage != null) {
      controller.setOnConsoleMessage(widget.onConsoleMessage!);
    }

    return controller;
  }

  void onPageFinished(String url) {
    setState(() => status = WebViewStatus.done);

    widget.onPageFinished?.call(url);
  }

  void onWebResourceError(WebResourceError error) {
    setState(() => status = WebViewStatus.error);

    widget.onWebResourceError?.call(error);
  }

  Future<void> _onSetCookie() async {
    if (widget.needAccessToken) {
      final uri = Uri.parse(widget.url!);
      await _cookieManager.setCookie(
        WebViewCookie(
          name: 'access_token',
          value: SharedPref.getString(KeyShared.accessToken),
          domain: uri.host,
          path: uri.path,
        ),
      );
    }

    if (widget.url.hasData) {
      await controller.loadRequest(Uri.parse(widget.url!));
    } else if (widget.html.hasData) {
      await controller.loadHtmlString(widget.html!);
    }
  }

  @override
  void didUpdateWidget(covariant VPWebView oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.url != null && widget.url != oldWidget.url) {
      status = WebViewStatus.loading;
      controller.loadRequest(Uri.parse(widget.url!));
    }
  }

  @override
  void initState() {
    super.initState();

    if (widget.cookie != null) {
      WebViewCookieManager().setCookie(
        WebViewCookie(
          name: widget.cookie!.name,
          value: widget.cookie!.value,
          domain: widget.cookie!.domain,
          path: widget.cookie!.path,
        ),
      );
    }

    controller = _getController();

    widget.onControllerInitialized?.call(controller);
    _onSetCookie();
  }

  void onReload() {
    controller.reload();
    setState(() => status == WebViewStatus.loading);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        if (status != WebViewStatus.error)
          WebViewWidget(
            controller: controller,
            gestureRecognizers: widget.gestureRecognizers,
          ),

        /// error view
        if (widget.showRetryWhenError && status == WebViewStatus.error)
          ErrorWithRetryView(onRetry: onReload),

        /// loading view
        Visibility(
          visible:
              widget.showLoadingIndicator && status == WebViewStatus.loading,
          child: widget.loadingBuilder?.call(context) ?? const VPInnerLoading(),
        ),
      ],
    );
  }
}
