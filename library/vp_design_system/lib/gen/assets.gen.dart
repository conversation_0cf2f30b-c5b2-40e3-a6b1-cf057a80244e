/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:lottie/lottie.dart' as _lottie;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// Directory path: assets/icons/add
  $AssetsIconsAddGen get add => const $AssetsIconsAddGen();

  /// Directory path: assets/icons/appbar
  $AssetsIconsAppbarGen get appbar => const $AssetsIconsAppbarGen();

  /// Directory path: assets/icons/checkbox
  $AssetsIconsCheckboxGen get checkbox => const $AssetsIconsCheckboxGen();

  /// Directory path: assets/icons/chip
  $AssetsIconsChipGen get chip => const $AssetsIconsChipGen();

  /// Directory path: assets/icons/close
  $AssetsIconsCloseGen get close => const $AssetsIconsCloseGen();

  /// Directory path: assets/icons/delete
  $AssetsIconsDeleteGen get delete => const $AssetsIconsDeleteGen();

  /// Directory path: assets/icons/dialog
  $AssetsIconsDialogGen get dialog => const $AssetsIconsDialogGen();

  /// Directory path: assets/icons/dropdown
  $AssetsIconsDropdownGen get dropdown => const $AssetsIconsDropdownGen();

  /// File path: assets/icons/handle.svg
  SvgGenImage get handle => const SvgGenImage('assets/icons/handle.svg');

  /// File path: assets/icons/ic_clear_text.svg
  SvgGenImage get icClearText =>
      const SvgGenImage('assets/icons/ic_clear_text.svg');

  /// File path: assets/icons/ic_codepilot.svg
  SvgGenImage get icCodepilot =>
      const SvgGenImage('assets/icons/ic_codepilot.svg');

  /// File path: assets/icons/ic_drag.svg
  SvgGenImage get icDrag => const SvgGenImage('assets/icons/ic_drag.svg');

  /// File path: assets/icons/ic_filter.svg
  SvgGenImage get icFilter => const SvgGenImage('assets/icons/ic_filter.svg');

  /// File path: assets/icons/ic_hide_text.svg
  SvgGenImage get icHideText =>
      const SvgGenImage('assets/icons/ic_hide_text.svg');

  /// File path: assets/icons/ic_home_ai.svg
  SvgGenImage get icHomeAi => const SvgGenImage('assets/icons/ic_home_ai.svg');

  /// File path: assets/icons/ic_noti.svg
  SvgGenImage get icNoti => const SvgGenImage('assets/icons/ic_noti.svg');

  /// File path: assets/icons/ic_require_login.svg
  SvgGenImage get icRequireLogin =>
      const SvgGenImage('assets/icons/ic_require_login.svg');

  /// File path: assets/icons/ic_search.svg
  SvgGenImage get icSearch => const SvgGenImage('assets/icons/ic_search.svg');

  /// File path: assets/icons/ic_show_text.svg
  SvgGenImage get icShowText =>
      const SvgGenImage('assets/icons/ic_show_text.svg');

  /// File path: assets/icons/ic_trash_new.svg
  SvgGenImage get icTrashNew =>
      const SvgGenImage('assets/icons/ic_trash_new.svg');

  /// File path: assets/icons/ic_zoom.svg
  SvgGenImage get icZoom => const SvgGenImage('assets/icons/ic_zoom.svg');

  /// Directory path: assets/icons/no_data
  $AssetsIconsNoDataGen get noData => const $AssetsIconsNoDataGen();

  /// Directory path: assets/icons/share
  $AssetsIconsShareGen get share => const $AssetsIconsShareGen();

  /// Directory path: assets/icons/snackBar
  $AssetsIconsSnackBarGen get snackBar => const $AssetsIconsSnackBarGen();

  /// List of all assets
  List<SvgGenImage> get values => [
        handle,
        icClearText,
        icCodepilot,
        icDrag,
        icFilter,
        icHideText,
        icHomeAi,
        icNoti,
        icRequireLogin,
        icSearch,
        icShowText,
        icTrashNew,
        icZoom
      ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// Directory path: assets/images/error
  $AssetsImagesErrorGen get error => const $AssetsImagesErrorGen();

  /// File path: assets/images/none.png
  AssetGenImage get none => const AssetGenImage('assets/images/none.png');

  /// File path: assets/images/search.png
  AssetGenImage get search => const AssetGenImage('assets/images/search.png');

  /// List of all assets
  List<AssetGenImage> get values => [none, search];
}

class $AssetsLottieGen {
  const $AssetsLottieGen();

  /// File path: assets/lottie/vpbank_loading.json
  LottieGenImage get vpbankLoading =>
      const LottieGenImage('assets/lottie/vpbank_loading.json');

  /// List of all assets
  List<LottieGenImage> get values => [vpbankLoading];
}

class $AssetsIconsAddGen {
  const $AssetsIconsAddGen();

  /// File path: assets/icons/add/ic_add_square.svg
  SvgGenImage get icAddSquare =>
      const SvgGenImage('assets/icons/add/ic_add_square.svg');

  /// File path: assets/icons/add/ic_circle_add.svg
  SvgGenImage get icCircleAdd =>
      const SvgGenImage('assets/icons/add/ic_circle_add.svg');

  /// List of all assets
  List<SvgGenImage> get values => [icAddSquare, icCircleAdd];
}

class $AssetsIconsAppbarGen {
  const $AssetsIconsAppbarGen();

  /// File path: assets/icons/appbar/ic_back.svg
  SvgGenImage get icBack =>
      const SvgGenImage('assets/icons/appbar/ic_back.svg');

  /// File path: assets/icons/appbar/ic_close.svg
  SvgGenImage get icClose =>
      const SvgGenImage('assets/icons/appbar/ic_close.svg');

  /// List of all assets
  List<SvgGenImage> get values => [icBack, icClose];
}

class $AssetsIconsCheckboxGen {
  const $AssetsIconsCheckboxGen();

  /// File path: assets/icons/checkbox/ic_checkbox_disable.svg
  SvgGenImage get icCheckboxDisable =>
      const SvgGenImage('assets/icons/checkbox/ic_checkbox_disable.svg');

  /// File path: assets/icons/checkbox/ic_checkbox_minus.svg
  SvgGenImage get icCheckboxMinus =>
      const SvgGenImage('assets/icons/checkbox/ic_checkbox_minus.svg');

  /// File path: assets/icons/checkbox/ic_checked.svg
  SvgGenImage get icChecked =>
      const SvgGenImage('assets/icons/checkbox/ic_checked.svg');

  /// File path: assets/icons/checkbox/ic_circle_checked.svg
  SvgGenImage get icCircleChecked =>
      const SvgGenImage('assets/icons/checkbox/ic_circle_checked.svg');

  /// File path: assets/icons/checkbox/ic_circle_checked_disable.svg
  SvgGenImage get icCircleCheckedDisable =>
      const SvgGenImage('assets/icons/checkbox/ic_circle_checked_disable.svg');

  /// File path: assets/icons/checkbox/ic_circle_uncheck.svg
  SvgGenImage get icCircleUncheck =>
      const SvgGenImage('assets/icons/checkbox/ic_circle_uncheck.svg');

  /// File path: assets/icons/checkbox/ic_uncheck.svg
  SvgGenImage get icUncheck =>
      const SvgGenImage('assets/icons/checkbox/ic_uncheck.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
        icCheckboxDisable,
        icCheckboxMinus,
        icChecked,
        icCircleChecked,
        icCircleCheckedDisable,
        icCircleUncheck,
        icUncheck
      ];
}

class $AssetsIconsChipGen {
  const $AssetsIconsChipGen();

  /// File path: assets/icons/chip/ic_left.svg
  SvgGenImage get icLeft => const SvgGenImage('assets/icons/chip/ic_left.svg');

  /// List of all assets
  List<SvgGenImage> get values => [icLeft];
}

class $AssetsIconsCloseGen {
  const $AssetsIconsCloseGen();

  /// File path: assets/icons/close/ic_close_game.svg
  SvgGenImage get icCloseGame =>
      const SvgGenImage('assets/icons/close/ic_close_game.svg');

  /// List of all assets
  List<SvgGenImage> get values => [icCloseGame];
}

class $AssetsIconsDeleteGen {
  const $AssetsIconsDeleteGen();

  /// File path: assets/icons/delete/ic_delete.svg
  SvgGenImage get icDelete =>
      const SvgGenImage('assets/icons/delete/ic_delete.svg');

  /// List of all assets
  List<SvgGenImage> get values => [icDelete];
}

class $AssetsIconsDialogGen {
  const $AssetsIconsDialogGen();

  /// File path: assets/icons/dialog/ic_done.svg
  SvgGenImage get icDone =>
      const SvgGenImage('assets/icons/dialog/ic_done.svg');

  /// File path: assets/icons/dialog/ic_handle.svg
  SvgGenImage get icHandle =>
      const SvgGenImage('assets/icons/dialog/ic_handle.svg');

  /// List of all assets
  List<SvgGenImage> get values => [icDone, icHandle];
}

class $AssetsIconsDropdownGen {
  const $AssetsIconsDropdownGen();

  /// File path: assets/icons/dropdown/ic_arrow_bottom.svg
  SvgGenImage get icArrowBottom =>
      const SvgGenImage('assets/icons/dropdown/ic_arrow_bottom.svg');

  /// File path: assets/icons/dropdown/ic_arrow_top.svg
  SvgGenImage get icArrowTop =>
      const SvgGenImage('assets/icons/dropdown/ic_arrow_top.svg');

  /// List of all assets
  List<SvgGenImage> get values => [icArrowBottom, icArrowTop];
}

class $AssetsIconsNoDataGen {
  const $AssetsIconsNoDataGen();

  /// File path: assets/icons/no_data/ic_nodata.svg
  SvgGenImage get icNodata =>
      const SvgGenImage('assets/icons/no_data/ic_nodata.svg');

  /// List of all assets
  List<SvgGenImage> get values => [icNodata];
}

class $AssetsIconsShareGen {
  const $AssetsIconsShareGen();

  /// File path: assets/icons/share/ic_share.svg
  SvgGenImage get icShare =>
      const SvgGenImage('assets/icons/share/ic_share.svg');

  /// List of all assets
  List<SvgGenImage> get values => [icShare];
}

class $AssetsIconsSnackBarGen {
  const $AssetsIconsSnackBarGen();

  /// File path: assets/icons/snackBar/ic_alert.svg
  SvgGenImage get icAlert =>
      const SvgGenImage('assets/icons/snackBar/ic_alert.svg');

  /// File path: assets/icons/snackBar/ic_error.svg
  SvgGenImage get icError =>
      const SvgGenImage('assets/icons/snackBar/ic_error.svg');

  /// File path: assets/icons/snackBar/ic_information.svg
  SvgGenImage get icInformation =>
      const SvgGenImage('assets/icons/snackBar/ic_information.svg');

  /// File path: assets/icons/snackBar/ic_success.svg
  SvgGenImage get icSuccess =>
      const SvgGenImage('assets/icons/snackBar/ic_success.svg');

  /// List of all assets
  List<SvgGenImage> get values => [icAlert, icError, icInformation, icSuccess];
}

class $AssetsImagesErrorGen {
  const $AssetsImagesErrorGen();

  /// File path: assets/images/error/error.png
  AssetGenImage get error =>
      const AssetGenImage('assets/images/error/error.png');

  /// List of all assets
  List<AssetGenImage> get values => [error];
}

class DesignAssets {
  DesignAssets._();

  static const String package = 'vp_design_system';

  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsLottieGen lottie = $AssetsLottieGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  static const String package = 'vp_design_system';

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => 'packages/vp_design_system/$_assetName';
}

class SvgGenImage {
  const SvgGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = false;

  const SvgGenImage.vec(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  static const String package = 'vp_design_system';

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter: colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => 'packages/vp_design_system/$_assetName';
}

class LottieGenImage {
  const LottieGenImage(
    this._assetName, {
    this.flavors = const {},
  });

  final String _assetName;
  final Set<String> flavors;

  static const String package = 'vp_design_system';

  _lottie.LottieBuilder lottie({
    Animation<double>? controller,
    bool? animate,
    _lottie.FrameRate? frameRate,
    bool? repeat,
    bool? reverse,
    _lottie.LottieDelegates? delegates,
    _lottie.LottieOptions? options,
    void Function(_lottie.LottieComposition)? onLoaded,
    _lottie.LottieImageProviderFactory? imageProviderFactory,
    Key? key,
    AssetBundle? bundle,
    Widget Function(
      BuildContext,
      Widget,
      _lottie.LottieComposition?,
    )? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    double? width,
    double? height,
    BoxFit? fit,
    AlignmentGeometry? alignment,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
    bool? addRepaintBoundary,
    FilterQuality? filterQuality,
    void Function(String)? onWarning,
  }) {
    return _lottie.Lottie.asset(
      _assetName,
      controller: controller,
      animate: animate,
      frameRate: frameRate,
      repeat: repeat,
      reverse: reverse,
      delegates: delegates,
      options: options,
      onLoaded: onLoaded,
      imageProviderFactory: imageProviderFactory,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      package: package,
      addRepaintBoundary: addRepaintBoundary,
      filterQuality: filterQuality,
      onWarning: onWarning,
    );
  }

  String get path => _assetName;

  String get keyName => 'packages/vp_design_system/$_assetName';
}
