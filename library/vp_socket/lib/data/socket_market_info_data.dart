import 'package:json_annotation/json_annotation.dart';
import 'package:vp_socket/data/socket_data.dart';
import 'package:vp_socket/extensions/num_helper.dart';

part 'socket_market_info_data.g.dart';

@JsonSerializable()
class VPMarketInfoData extends VPSocketInvestData {
  // ["null", "string"]
  @DynamicToNumConverter()
  @JsonKey(name: 'indexValue')
  final num? marketIndex;

  // ["null", "float"]
  @DynamicToNumConverter()
  final num? indexChange;

  // ["null", "float"]
  @DynamicToNumConverter()
  final num? indexPercentChange;

  // ["null", "long"]
  @DynamicToNumConverter()
  final num? totalVolume;

  // ["null", "long"]
  @DynamicToNumConverter()
  final num? totalValue;

  final String? marketStatus;

  // ["null", "int"]
  @DynamicToNumConverter()
  final num? advances;

  // ["null", "int"]
  @DynamicToNumConverter()
  final num? declines;

  // ["null", "int"]
  @DynamicToNumConverter()
  final num? noChange;

  // ["null", "int"]
  @DynamicToNumConverter()
  final num? numberOfCe;

  // ["null", "int"]
  @DynamicToNumConverter()
  final num? numberOfFl;

  // ["null", "long"]
  @DynamicToNumConverter()
  final num? oddLotTotalVolume;

  // ["null", "long"]
  @DynamicToNumConverter()
  final num? oddLotTotalValue;

  @JsonKey(name: 'indexCode', defaultValue: '')
  final String marketCode;

  final String? indexTime;

  VPMarketInfoData({
    required super.channel,
    required this.marketCode,
    this.marketIndex,
    this.indexTime,
    this.marketStatus,
    this.indexChange,
    this.indexPercentChange,
    this.totalVolume,
    this.totalValue,
    this.advances,
    this.declines,
    this.noChange,
    this.numberOfCe,
    this.numberOfFl,
    this.oddLotTotalVolume,
    this.oddLotTotalValue,
  }) : super(symbol: marketCode);

  factory VPMarketInfoData.fromJson(Map<String, dynamic> json) =>
      _$VPMarketInfoDataFromJson(json);

  Map<String, dynamic> toJson() => _$VPMarketInfoDataToJson(this);
}
