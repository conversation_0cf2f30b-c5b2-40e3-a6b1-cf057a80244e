import 'dart:async';
import 'package:flutter/widgets.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:vp_socket/base/base.dart';
import 'package:vp_socket/extensions/throttle.dart';

mixin SocketLifecycleConnectivityMixin on BaseSocket {
  var appInBackground = false;
  var networkAvailable = true;

  final Throttle _throttle = Throttle(const Duration(seconds: 1));

  bool get isReconnectAllowed => !appInBackground;

  void closeNoRetry() {}

  void connectNow() {}

  void _ensurePolicyConnect() {
    if (!appInBackground) {
      _throttle.call(() => connectNow());
    }
  }

  Future<void> appBackground() async {
    appInBackground = true;
    closeNoRetry();
  }

  Future<void> appForeground() async {
    appInBackground = false;
    _ensurePolicyConnect();
  }

  Future<void> networkDown() async {
    networkAvailable = false;
    closeNoRetry();
  }

  Future<void> networkUp() async {
    networkAvailable = true;
    _ensurePolicyConnect();
  }

  @override
  Future<void> dispose() async {
    _throttle.dispose();
  }
}

class SocketLifecycleConnectivityObserver with WidgetsBindingObserver {
  SocketLifecycleConnectivityObserver(
    this.socket, {
    this.debounce = const Duration(milliseconds: 400),
  });

  final SocketLifecycleConnectivityMixin socket;
  final Duration debounce;

  StreamSubscription? _connSub;

  Timer? _netDebounce, _appDebounce;

  bool? _hasConnection;
  bool _stopped = false;

  AppLifecycleState? _appLifecycleState;

  final Throttle _throttle = Throttle(const Duration(seconds: 2));

  Future<void> start() async {
    WidgetsBinding.instance.addObserver(this);

    _stopped = false;

    _appLifecycleState = WidgetsBinding.instance.lifecycleState;

    _connSub = Connectivity().onConnectivityChanged.listen((results) async {
      _throttle.call(() => _handleNetworkChange(results));
    });
  }

  void stop() {
    WidgetsBinding.instance.removeObserver(this);

    _throttle.dispose();
    _stopped = true;
    _connSub?.cancel();
    _connSub = null;
    _netDebounce?.cancel();
    _netDebounce = null;
    _appDebounce?.cancel();
    _appDebounce = null;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (_stopped) return;
    final prev = _appLifecycleState;
    if (prev == state) return;

    if (state == AppLifecycleState.resumed &&
        prev != AppLifecycleState.resumed) {
      _scheduleAppDebounced(() => socket.appForeground());
    } else if (state != AppLifecycleState.resumed &&
        prev == AppLifecycleState.resumed) {
      _scheduleAppDebounced(() => socket.appBackground());
    }
    _appLifecycleState = state;
  }

  Future _handleNetworkChange(List<ConnectivityResult> result) async {
    if (_stopped) return;

    final hasConnection = !result.contains(ConnectivityResult.none);

    if (_hasConnection == hasConnection) return;

    _hasConnection = hasConnection;

    if (hasConnection) {
      socket.networkUp();
    } else {
      socket.networkDown();
    }
  }

  void _scheduleAppDebounced(void Function() f) {
    _appDebounce?.cancel();
    _appDebounce = Timer(debounce, () {
      if (!_stopped) f();
    });
  }
}
