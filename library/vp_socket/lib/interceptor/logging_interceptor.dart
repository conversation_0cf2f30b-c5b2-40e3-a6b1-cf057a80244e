import 'package:flutter/foundation.dart';
import 'package:vp_socket/data/enum/socket_event.dart';
import 'package:vp_socket/data/socket_context.dart';
import 'package:vp_socket/interceptor/socket_interceptor.dart';

class LoggingInterceptor extends SocketInterceptor {
  @override
  Future<bool> onEvent(SocketEvent event, SocketContext ctx,
      {Object? payload, Object? error, StackTrace? stackTrace}) async {
    String text =
        "[${ctx.socketType}] time=${DateTime.now()} connId=${ctx.connId} state=${ctx.state} event=$event payload=$payload error=$error";

    if (ctx.delayMs != null) {
      text += ' delayMs=${ctx.delayMs}';
    }

    if (ctx.attempt != null) {
      text += ' attempt=${ctx.attempt}';
    }

    debugPrint(text);

    return false;
  }
}
