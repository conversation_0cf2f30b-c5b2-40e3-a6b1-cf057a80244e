name: vp_common
description: A starting point for Dart libraries or applications.
version: 1.0.0
# repository: https://github.com/my_org/my_repo

environment:
  sdk: ^3.3.4

# Add regular dependencies here.
dependencies:
  intl: 
  flutter:
    sdk: flutter

  shared_preferences:
  cached_network_image:
  shake:
  debounce_throttle:
  tiengviet: 1.0.0
  network_info_plus:
  vp_design_system:
    path: ../vp_design_system
  vp_stock_common:
    path: ../vp_stock_common

  lottie:
  flutter_svg:

  multiple_localization:
  shimmer:
  auto_size_text:
  get_it:
  collection: ^1.19.1
  flutter_bloc:
  uuid: ^4.5.1
  url_launcher:
  path_provider:
  image_gallery_saver:
  device_info_plus:
  permission_handler:
  alice:
  fluttertoast:
  equatable:
  package_info_plus:
  source_gen:
  build:
  analyzer:
  json_annotation:
  event_bus:

dependency_overrides:
  flutter_gen_core: ^5.10.0

dev_dependencies:
  test: ^1.24.0
  build_runner:
  flutter_lints: ^5.0.0
  custom_lint:
  json_serializable:
  retrofit_generator:
  flutter_gen_runner:
  intl_utils:


flutter_intl:
  enabled: true
  class_name: VPCommonLocalize


flutter_gen:
  output: lib/generated/
  line_length: 80
  assets:
    outputs:
      package_parameter_enabled: true

  # Optional
  integrations:
    image: true
    flutter_svg: true
flutter:

  # To add assets to your package, add an assets section, like this:
  assets:
    - assets/icons/
    - assets/icons/edit/
    - assets/icons/info/
    - assets/json/
    - assets/images/
    - assets/images/tutorial/dark/
    - assets/images/tutorial/light/
   