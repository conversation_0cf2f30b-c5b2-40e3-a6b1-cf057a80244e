import 'dart:async';

import 'package:analyzer/dart/constant/value.dart';
import 'package:analyzer/dart/element/element.dart';
import 'package:analyzer/dart/element/nullability_suffix.dart';
import 'package:analyzer/dart/element/type.dart';
import 'package:build/build.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:source_gen/source_gen.dart';

import 'package:vp_common/gen/from_query_params.dart';

class FromQueryParamsGenerator extends GeneratorForAnnotation<GoRouterParams> {
  @override
  FutureOr<String> generateForAnnotatedElement(
    Element element,
    ConstantReader annotation,
    BuildStep buildStep,
  ) {
    if (element is! ClassElement) return '';
    final className = element.name;

    // 🔒 Helpers riêng cho MỖI class
    final enumHelpers = StringBuffer();
    final addedEnumHelpers = <String>{};
    final addedEnumListHelpers = <String>{};

    final buffer = StringBuffer();
    buffer.writeln('extension ${className}Extensions on $className {');

    // ====== fromQueryParams ======
    buffer.writeln(
      '  static $className fromQueryParams(Map<String, String> params) {',
    );

    final constructor = element.unnamedConstructor;
    if (constructor == null) {
      throw InvalidGenerationSourceError(
        'Class $className must have an unnamed constructor.',
        element: element,
      );
    }

    if (constructor.parameters.any((p) => !p.isNamed)) {
      throw InvalidGenerationSourceError(
        'Only named parameters are supported in the unnamed constructor of $className.',
        element: element,
      );
    }

    final paramDefaults = {
      for (final p in constructor.parameters) p.name: p.defaultValueCode,
    };

    buffer.writeln('    return $className(');

    for (final p in constructor.parameters) {
      final name = p.name;
      final type = p.type;
      final typeStr = type.getDisplayString(withNullability: false);
      final isNullable = type.nullabilitySuffix == NullabilitySuffix.question;

      final field = element.getField(name);

      if (_isIgnoredParam(p) || _isIgnoredField(field)) continue;

      final jsonDefault =
          field == null ? null : _getDefaultValueFromJsonKey(field);
      final defaultValue = jsonDefault ?? paramDefaults[name];

      final parseCode = _generateParseCode(
        name,
        type,
        typeStr,
        isNullable,
        defaultValue,
        enumHelpers,
        addedEnumHelpers,
        addedEnumListHelpers,
      );

      buffer.writeln('      $name: $parseCode,');
    }

    buffer.writeln('    );');
    buffer.writeln('  }');

    // ====== toQueryParams ======
    buffer.writeln('  Map<String, String> toQueryParams() {');
    buffer.writeln('    return {');

    final ownFields = element.fields.where(
      (f) => !f.isStatic && !f.isSynthetic,
    );

    final ignoreChecker = const TypeChecker.fromRuntime(IgnoreGoRouterField);

    for (final field in ownFields) {
      if (ignoreChecker.hasAnnotationOfExact(field)) continue;

      final name = field.name;
      final type = field.type;

      String valueExpr;
      if (_isEnum(type)) {
        valueExpr = _isNullable(type) ? '$name!.name' : '$name.name';
      } else if (_isListOfEnum(type)) {
        valueExpr = _isNullable(type)
            ? "$name!.map((e) => e.name).join(',')"
            : "$name.map((e) => e.name).join(',')";
      } else if (_isListPrimitive(type)) {
        valueExpr = _isNullable(type)
            ? "$name!.map((e) => e.toString()).join(',')"
            : "$name.map((e) => e.toString()).join(',')";
      } else {
        valueExpr =
            _isNullable(type) ? '$name!.toString()' : '$name.toString()';
      }

      buffer.writeln(
        "      ${_isNullable(type) ? 'if ($name != null) ' : ''}'$name': $valueExpr,",
      );
    }

    buffer.writeln('    };');
    buffer.writeln('  }');

    // ====== Enum helpers (riêng cho class này) ======
    buffer.write(enumHelpers.toString());

    buffer.writeln('}');
    return buffer.toString();
  }

  // ===================== internals =====================

  String _generateParseCode(
    String name,
    DartType type,
    String typeStr,
    bool isNullable,
    String? defaultValue,
    StringBuffer enumHelpers,
    Set<String> addedEnumHelpers,
    Set<String> addedEnumListHelpers,
  ) {
    final param = "params['$name']";
    final hasDefault =
        !(defaultValue == null || defaultValue.toLowerCase() == 'null');

    final isRequired = !isNullable && !hasDefault;

    // Enum
    if (_isEnum(type)) {
      final enumName = type.getDisplayString(withNullability: false);
      _ensureEnumHelper(enumName, enumHelpers, addedEnumHelpers);
      final parse = '_tryParseEnum$enumName($param)';

      if (isRequired) return '$parse!';

      return hasDefault ? '$parse ?? $defaultValue' : parse;
    }

    // List<Enum>
    if (_isListOfEnum(type)) {
      final enumType = (type as ParameterizedType).typeArguments.first;
      final enumName = enumType.getDisplayString(withNullability: false);
      _ensureEnumHelper(enumName, enumHelpers, addedEnumHelpers);
      _ensureEnumListHelper(enumName, enumHelpers, addedEnumListHelpers);
      final parse = '_tryParseEnumList$enumName($param)';

      if (isRequired) return '$parse!';

      return hasDefault ? '$parse ?? $defaultValue' : parse;
    }

    // List<primitive>
    if (_isListPrimitive(type)) {
      final innerType = (type as ParameterizedType)
          .typeArguments
          .first
          .getDisplayString(withNullability: false);

      final operator = isRequired ? '!' : '?';

      String cast;
      switch (innerType) {
        case 'int':
          cast = "$param$operator.split(',').map(int.parse).toList()";
          break;
        case 'double':
          cast = "$param$operator.split(',').map(double.parse).toList()";
          break;
        case 'num':
          cast = "$param$operator.split(',').map(num.parse).toList()";
          break;
        case 'bool':
          cast = "$param$operator.split(',').map((e) => e == 'true').toList()";
          break; // tránh fall-through
        case 'String':
          cast = "$param$operator.split(',').toList()";
          break;
        default:
          throw UnsupportedError('Unsupported list item type: $innerType');
      }

      return hasDefault ? '$cast ?? $defaultValue' : cast;
    }

    // Primitive đơn
    String cast;
    switch (typeStr) {
      case 'int':
        cast = "int.tryParse($param ?? '')";
        break;
      case 'double':
        cast = "double.tryParse($param ?? '')";
        break;
      case 'bool':
        cast = "$param == 'true'";
        break;
      case 'String':
        cast = param;
        break;
      case 'num':
        cast = "num.tryParse($param ?? '')";
        break;
      default:
        throw UnsupportedError('Unsupported field type: $typeStr');
    }

    if (isRequired) return '$cast!';

    return hasDefault ? '$cast ?? $defaultValue' : cast;
  }

  // ====== helpers ======

  bool _isEnum(DartType type) => type.element is EnumElement;

  bool _isNullable(DartType type) =>
      type.nullabilitySuffix == NullabilitySuffix.question;

  bool _isListOfEnum(DartType type) {
    return type is ParameterizedType &&
        type.element?.name == 'List' &&
        type.typeArguments.isNotEmpty &&
        _isEnum(type.typeArguments.first);
  }

  /// Chỉ nhận List của kiểu nguyên thủy: int/double/num/bool/String
  bool _isListPrimitive(DartType type) {
    if (type is! ParameterizedType ||
        type.element?.name != 'List' ||
        type.typeArguments.isEmpty) {
      return false;
    }
    final t = type.typeArguments.first.getDisplayString(withNullability: false);
    return t == 'int' ||
        t == 'double' ||
        t == 'num' ||
        t == 'bool' ||
        t == 'String';
  }

  void _ensureEnumHelper(
    String enumName,
    StringBuffer enumHelpers,
    Set<String> added,
  ) {
    final name = '_tryParseEnum$enumName';
    if (added.add(name)) {
      enumHelpers.writeln('  static $enumName? $name(String? name) {');
      enumHelpers.writeln('    if (name == null) return null;');
      enumHelpers.writeln(
        '    return $enumName.values.cast<$enumName?>().firstWhere((e) => e!.name == name, orElse: () => null);',
      );
      enumHelpers.writeln('  }\n');
    }
  }

  void _ensureEnumListHelper(
    String enumName,
    StringBuffer enumHelpers,
    Set<String> added,
  ) {
    final name = '_tryParseEnumList$enumName';
    if (added.add(name)) {
      final single = '_tryParseEnum$enumName';
      enumHelpers.writeln('  static List<$enumName>? $name(String? raw) {');
      enumHelpers.writeln('    if (raw == null) return null;');
      enumHelpers.writeln(
        "    return raw.split(',').map((e) => $single(e)).whereType<$enumName>().toList();",
      );
      enumHelpers.writeln('  }\n');
    }
  }

  String? _getDefaultValueFromJsonKey(FieldElement field) {
    final jsonKeyAnn = TypeChecker.fromRuntime(
      JsonKey,
    ).firstAnnotationOfExact(field);
    if (jsonKeyAnn == null) return null;

    final reader = ConstantReader(jsonKeyAnn);
    final defaultValueReader = reader.peek('defaultValue');
    if (defaultValueReader == null || defaultValueReader.isNull) return null;

    final DartObject dartObj = defaultValueReader.objectValue;

    if (dartObj.toBoolValue() != null) {
      return dartObj.toBoolValue().toString();
    } else if (dartObj.toIntValue() != null) {
      return dartObj.toIntValue().toString();
    } else if (dartObj.toDoubleValue() != null) {
      return dartObj.toDoubleValue().toString();
    } else if (dartObj.toStringValue() != null) {
      final s = dartObj.toStringValue()!;
      final escaped = s.replaceAll(r"'", r"\'");
      return "'$escaped'";
    }

    if (dartObj.type != null && dartObj.type!.element is EnumElement) {
      final enumType = dartObj.type!;
      final enumClassName = enumType.getDisplayString(withNullability: false);
      final enumName = dartObj.getField('name')?.toStringValue();
      if (enumName != null) {
        return '$enumClassName.$enumName';
      }
    }

    return null;
  }

  bool _isIgnoredField(FieldElement? field) {
    if (field == null) return false;
    // Bỏ qua nếu có @IgnoreGoRouterField
    if (const TypeChecker.fromRuntime(
      IgnoreGoRouterField,
    ).hasAnnotationOfExact(field)) {
      return true;
    }
    return false;
  }

  bool _isIgnoredParam(ParameterElement param) {
    // Check annotation trực tiếp trên param của constructor
    for (final meta in param.metadata) {
      final obj = meta.computeConstantValue();
      if (obj == null) continue;
      if (obj.type?.element?.name == 'IgnoreGoRouterField') {
        return true;
      }
    }
    return false;
  }
}
