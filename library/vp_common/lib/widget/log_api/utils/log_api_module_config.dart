import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shake/shake.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_common/widget/log_api/screens/log_api_main_screen.dart';

bool enable = false;

bool logIsShowing = false;

class LogApiConfigScreen extends StatefulWidget {
  const LogApiConfigScreen({
    required this.navigatorKey,
    required this.child,
    this.enable = true,
    super.key,
  });

  final GlobalKey<NavigatorState> navigatorKey;

  final Widget child;

  final bool enable;

  @override
  State<LogApiConfigScreen> createState() => _LogApiConfigScreenState();
}

class _LogApiConfigScreenState extends State<LogApiConfigScreen> {
  ShakeDetector? detector;

  @override
  void initState() {
    super.initState();

    enable = widget.enable;

    if (widget.enable) {
      detector = ShakeDetector.autoStart(
        onPhoneShake: (event) {
          showPopupToNavigate();
        },
      );

      // Trigger View - Simulator can not shake or easy to trigger

      ServicesBinding.instance.keyboard.addHandler(_onKey);
    }
  }

  bool _onKey(KeyEvent event) {
    final key = event.logicalKey.keyLabel;

    if (event is KeyDownEvent && key == 'Arrow Up') {
      showPopupToNavigate();
    }

    return false;
  }

  @override
  void dispose() {
    detector?.stopListening();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }

  Future<void> showPopupToNavigate() async {
    if (logIsShowing) return;

    final context =
        GetIt.instance<NavigationService>().navigatorKey.currentContext;

    if (context == null) return;

    logIsShowing = true;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const LogApiBottomSheet(),
    ).then((value) => logIsShowing = false);
  }
}

class LogApiBottomSheet extends StatelessWidget {
  const LogApiBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 200,
      color: Colors.white,
      child: Column(
        children: [
          ListTile(
            onTap: () => showLogScreen(context),
            title: const Text(
              'Show API Log',
              style: TextStyle(color: Colors.black),
            ),
          ),
          ListTile(
            onTap: () => showLogScreenOld(context),
            title: const Text(
              'Show API Log Old',
              style: TextStyle(color: Colors.black),
            ),
          ),
          ListTile(
            onTap: () => Navigator.pop(context),
            title: const Text(
              'Đóng',
              style: TextStyle(color: Colors.black),
            ),
          ),
        ],
      ),
    );
  }

  void showLogScreen(BuildContext context) {
    Navigator.pop(context);
    GetIt.instance<Alice>().showInspector();
  }

  void showLogScreenOld(BuildContext context) {
    Navigator.pop(context);
    showGeneralDialog(
      context: context,
      pageBuilder: (_, __, ___) => const LogAPIScreen(),
    ).then((value) => logIsShowing = false);
  }
}
